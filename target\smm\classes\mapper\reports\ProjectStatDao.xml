<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.reports.dao.ProjectStatDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.reports.entity.ProjectStat" id="projectStatMap">
        <result property="id" column="ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="deptId" column="DEPT_ID"/>
        <result property="deptCode" column="DEPT_CODE"/>
        <result property="areaCode" column="AREA_CODE"/>
        <result property="projectType" column="PROJECT_TYPE"/>
        <result property="yearMonth" column="YEAR_MONTH"/>
        <result property="year" column="YEAR"/>
        <result property="month" column="MONTH"/>
        <result property="billCount" column="BILL_COUNT"/>
        <result property="totalVehicleLoad" column="TOTAL_VEHICLE_LOAD"/>
    </resultMap>

    <select id="queryProjectStat" resultType="com.ybkj.smm.modules.reports.entity.ProjectStat">
        SELECT
        pro.DEPT_ID deptId,
        pro.DEPT_CODE deptCode,
        pro.AREA_CODE areaCode,
        pro.TYPE projectType,
        COUNT(bill.ID) billCount,
        SUM(bill.VEHICLE_LOAD) totalVehicleLoad
        FROM
        smm_bill_bill bill
        LEFT JOIN smm_project_project pro on bill.PROJECT_ID = pro.DEPT_ID
        where pro.DELETED = 0 and bill.CREATE_TIME &gt;= #{startTime} and bill.CREATE_TIME &lt;= #{endTime}
        GROUP BY bill.PROJECT_ID
    </select>


</mapper>
