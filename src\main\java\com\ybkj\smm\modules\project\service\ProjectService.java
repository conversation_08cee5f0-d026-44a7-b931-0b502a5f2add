package com.ybkj.smm.modules.project.service;

import com.sloth.common.utils.PageUtils;
import com.sloth.core.service.BaseService;
import com.ybkj.smm.modules.project.dto.ProjectStatusDto;
import com.ybkj.smm.modules.project.entity.Project;
import com.ybkj.smm.modules.project.query.ProjectQuery;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.List;


/**
 * 项目/沙场信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-21 14:10:16
 */
public interface ProjectService extends BaseService<Project> {

    /**
     * <li>功能描述：获取分页数据列表</li>
     *
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2023-08-21 14:10:16
     */
    PageUtils queryPage(ProjectQuery queryParams);

    /**
     *<li>功能描述：获取不分页数据列表</li>
     * @author: wang<PERSON>o
     * @date:  2024/7/4 15:04
     */
    List<Project> queryList(ProjectQuery queryParams);

    /**
     * <li>功能描述：通过ID获取单条数据</li>
     *
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2023-08-21 14:10:16
     */
    Project getInfoById(String deptId);

    /**
     * <li>功能描述：批量删除</li>
     *
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2023-08-21 14:10:16
     */
    void deleteByIds(List<String> deptIds, String currentUserDeptId);

    /**
     * <li>功能描述：单个删除</li>
     *
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2023-08-21 14:10:16
     */
    void deleteById(String deptId, String currentUserDeptId);

    /**
     * <li>功能描述：导出操作</li>
     *
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2023-08-21 14:10:16
     */
    void export(ProjectQuery queryParams) throws IOException;

    /**
     * <li>功能描述：新增</li>
     *
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2023-08-21 14:10:16
     */
    void saveProject(Project project);

    /**
     * <li>功能描述：修改</li>
     *
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2023-08-21 14:10:16
     */
    void updateProjectById(Project project);

    /**
     *<li>功能描述：查询项目数量，过滤部门权限</li>
     * @author: <EMAIL>
     * @date:  2023/9/6 15:17
     */
    Long projectCount(ProjectQuery projectQuery);

    /**
     * 根据ID查询单挑数据，包括已经逻辑删除的数据
     * @param id
     * @return
     */
    Project getByIdWithDeleted(String id);

    /**
     *<li>功能描述：查询当日允许开单数量： 每日授权数量+今日</li>
     * @author: <EMAIL>
     * @date:  2024/2/20 11:05
     */
    Integer getTodayAllowCount(String projectId);


    /**
     * <li>功能描述：生成小程序码</li>
     *
     * @return
     * @auther: liumengke
     * @Date: 2024/3/5 14:23
     */
    String getWxCode(String createUserId, String deptId) throws Exception;

    /**
     *<li>功能描述：获取开启终端的项目列表</li>
     * @author: wangxiao
     * @date:  2024/8/7 上午10:49
     */
    List<Project> listProjectNamesByUseClient();

    /**
     *<li>功能描述：获取当前用户所在部门可查看的项目名称</li>
     * @author: wangxiao
     * @date:  2024/11/6 上午10:21
     */
    List<Project> listProjectByDeptId(ProjectQuery queryParams);

    /**
     *<li>功能描述：获取当前用户有数据权限的所有项目ID</li>
     * @author: wangxiao
     * @date:  2024/11/6 上午10:21
     */
    List<String> queryProjectIdByAuth(ProjectQuery query);

    /**
     *<li>功能描述：创建下载的二维码图片</li>
     * @author: wangxiao
     * @date:  2024/12/19 上午11:01
     */
    BufferedImage createWxCodeImg(String createUserId, String deptId) throws Exception;

    void updateProjectStatus(ProjectStatusDto projectStatusDto);

    /**
     *<li>功能描述：检查项目采砂量是否充足</li>
     * @author: 仕伟
     * @date:  2025/8/23 
     * @param projectId 项目ID
     * @return true-采砂量充足，false-采砂量不足
     */
    boolean checkSandVolumeAvailable(String projectId);
}

