package com.ybkj.smm.modules.bill.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.f4b6a3.ulid.UlidCreator;
import com.sloth.common.annotation.DataFilter;
import com.sloth.common.exception.ServiceException;
import com.sloth.common.utils.AuthUtils;
import com.sloth.common.utils.DateUtils;
import com.sloth.common.utils.PageUtils;
import com.sloth.common.utils.PageWrapper;
import com.sloth.common.xss.SQLFilter;
import com.sloth.core.service.impl.BaseServiceImpl;
import com.sloth.modules.com.entity.UploadFile;
import com.sloth.modules.com.service.UploadFileService;
import com.sloth.modules.sys.entity.SysUserEntity;
import com.sloth.modules.sys.service.SysUserService;
import com.sloth.modules.sys.shiro.ShiroUtils;
import com.ybkj.smm.common.constant.SmmConstants;
import com.ybkj.smm.common.constant.SmmErrorMsgConstants;
import com.ybkj.smm.modules.bill.dao.ApplyDao;
import com.ybkj.smm.modules.bill.dto.ApplyResultDto;
import com.ybkj.smm.modules.bill.dto.SaveInDataDto;
import com.ybkj.smm.modules.bill.entity.AnNounCement;
import com.ybkj.smm.modules.bill.entity.Apply;
import com.ybkj.smm.modules.bill.entity.Bill;
import com.ybkj.smm.modules.bill.query.ApplyQuery;
import com.ybkj.smm.modules.bill.service.ApplyService;
import com.ybkj.smm.modules.bill.service.BillService;
import com.ybkj.smm.modules.client.entity.TempDataPool;
import com.ybkj.smm.modules.client.service.ClientService;
import com.ybkj.smm.modules.client.service.ClientSharingService;
import com.ybkj.smm.modules.client.service.TempDataPoolService;
import com.ybkj.smm.modules.mqtt.service.SendMessageService;
import com.ybkj.smm.modules.project.entity.Project;
import com.ybkj.smm.modules.project.query.ProjectQuery;
import com.ybkj.smm.modules.project.service.ProjectService;
import com.ybkj.smm.modules.sys.entity.Signature;
import com.ybkj.smm.modules.sys.service.SignatureService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.List;

@Service("applyService")
public class ApplyServiceImpl extends BaseServiceImpl<ApplyDao, Apply> implements ApplyService {

    /**
     * 日志对象
     */
    private static final Logger log= LoggerFactory.getLogger(ApplyServiceImpl.class);

    @Autowired
    @Lazy
    private BillService billService;

    @Autowired
    private SignatureService signatureService;

    @Autowired
    private SendMessageService sendMessageService;

    @Autowired
    private ClientSharingService clientSharingService;

    @Autowired
    private ClientService clientService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    @Lazy
    private ProjectService projectService;

    @Autowired
    private UploadFileService uploadFileService;

    @Autowired
    @Lazy
    private TempDataPoolService tempDataPoolService;


    @Override
    @DataFilter(deptAlias="appl",tableAlias="appl",deptId = "PROJECT_ID")
    public PageUtils queryPage(ApplyQuery queryParams) {

        //默认以创建时间排序
        if (StringUtils.isBlank(queryParams.getSidx())) {
            queryParams.setSidx("appl.ID");
            queryParams.setOrder("desc");
        }

        //拼接查询条件
        QueryWrapper<Apply> ew = new QueryWrapper<Apply>();
        ew.like(StringUtils.isNotBlank(queryParams.getProjectName()), "project.NAME", queryParams.getProjectName());
        ew.like(StringUtils.isNotBlank(queryParams.getSectionName()), "project.SECTION_NAME", queryParams.getSectionName());
        ew.eq(StringUtils.isNotBlank(queryParams.getStatus()),"appl.STATUS", queryParams.getStatus());
        ew.ge(StringUtils.isNotBlank(queryParams.getCreateStartDateTime()), "appl.CREATE_TIME", queryParams.getCreateStartDateTime());
        ew.le(StringUtils.isNotBlank(queryParams.getCreateEndDateTime()), "appl.CREATE_TIME", queryParams.getCreateEndDateTime() + "24:00:00");

        ew.apply(StringUtils.isNotBlank(queryParams.getKeyWord()),
                "(appl.CAR_NUMBER like {0} or user.SHOW_NAME like {0} or user.MOBILE like {0})",
                "%" + SQLFilter.sqlInject(queryParams.getKeyWord()) + "%");
        ew.apply(StringUtils.isNotBlank(queryParams.getSqlFilter()), queryParams.getSqlFilter());

        Page<Apply> page = baseMapper.selectApplyPage(new PageWrapper<Apply>(queryParams, "appl.ID").getPage(), ew);

        return new PageUtils(page);
    }

    @Override
    @DataFilter(deptAlias="appl",tableAlias="appl",deptId = "PROJECT_ID")
    public PageUtils maQueryPage(ApplyQuery queryParams) {

        //默认以创建时间排序
        if (StringUtils.isBlank(queryParams.getSidx())) {
            queryParams.setSidx("appl.ID");
            queryParams.setOrder("desc");
        }

        //拼接查询条件
        QueryWrapper<Apply> ew = new QueryWrapper<Apply>();
        ew.like(StringUtils.isNotBlank(queryParams.getProjectName()), "project.NAME", queryParams.getProjectName());
        ew.like(StringUtils.isNotBlank(queryParams.getSectionName()), "project.SECTION_NAME", queryParams.getSectionName());
        ew.eq(StringUtils.isNotBlank(queryParams.getStatus()),"appl.STATUS", queryParams.getStatus());
        ew.apply(StringUtils.isNotBlank(queryParams.getKeyWord()),
                "(appl.CAR_NUMBER like {0} or user.SHOW_NAME like {0} or user.MOBILE like {0})",
                "%" + SQLFilter.sqlInject(queryParams.getKeyWord()) + "%");
        ew.apply(StringUtils.isNotBlank(queryParams.getSqlFilter()), queryParams.getSqlFilter());

        Page<ApplyResultDto> page = baseMapper.selectMaApplyPage(new PageWrapper<Apply>(queryParams, "appl.ID").getPage(), ew);

        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveApply(Apply apply) {
        Project project = projectService.getByIdWithDeleted(apply.getProjectId());
        if(project.getStatus().equals("pastDue") || project.getStatus().equals("complete") || project.getStatus().equals("abnormal")){
            throw new ServiceException(2005,"项目状态异常，不能提交运沙申请");
        }
        //存在未开单的申请，不能继续申请
        long count = this.count(new QueryWrapper<Apply>()
                .eq("CREATE_USER_ID", AuthUtils.getCurrentUserId())
                .in("STATUS", SmmConstants.APPLY_STATUS.NOT_IN, SmmConstants.APPLY_STATUS.NOT_BILLED));
        if (count > 0) {
            throw new ServiceException(2003, SmmErrorMsgConstants.EXIST_NOT_BILLED_APPLY_2003);
        }

        //存在未核销的转运单不能申请
        long count1 = billService.count(new QueryWrapper<Bill>()
                .eq("DRIVER_ID", AuthUtils.getCurrentUserId())
                .ne("STATUS", SmmConstants.BILL_STATUS.COMPLETED));
        if (count1 > 0) {
            throw new ServiceException(2004, SmmErrorMsgConstants.EXIST_NOT_COMPLETE_BILL_2004);
        }

        //同一个车牌号，已存在未开单的申请，不能继续申请
        Apply sameNumberApply = this.getOne(new LambdaQueryWrapper<Apply>()
                .select(Apply::getCreateUserId, Apply::getId)
                .eq(Apply::getCarNumber, apply.getCarNumber())
                .in(Apply::getStatus, SmmConstants.APPLY_STATUS.NOT_IN, SmmConstants.APPLY_STATUS.NOT_BILLED));
        if (sameNumberApply != null) {
            SysUserEntity user = sysUserService.getById(sameNumberApply.getCreateUserId());
            throw new ServiceException(2025, MessageFormat.format(SmmErrorMsgConstants.EXIST_NOT_BILLED_APPLY_2025, apply.getCarNumber(), user == null ? "" : user.getShowName()));
        }

        //检查项目采砂量是否充足
        if (!projectService.checkSandVolumeAvailable(apply.getProjectId())) {
            throw new ServiceException(2045, SmmErrorMsgConstants.SAND_VOLUME_INSUFFICIENT_2045);
        }

        apply.setStatus(SmmConstants.APPLY_STATUS.NOT_IN);
        apply.setDriverSignatureId(UlidCreator.getUlid().toString());

        boolean save = this.save(apply);

        if (save) {
            //保存运砂人签名
            Signature driverSignature = new Signature(apply.getDriverSignatureId(), apply.getCreateUserId(), apply.getId(),
                    SmmConstants.SignatureFieldName.BILL_SIGNATURE, apply.getDriverSignatureCode());
            signatureService.save(driverSignature);

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization(){
                @Override
                public void afterCommit() {
                    sendMessageService.sendCarNumbers(apply.getProjectId());
                }
            });

        }
    }

    @Override
    public List<String> notBilledCarNumbersByProjectId(String projectId) {
        return this.listObjs(new LambdaQueryWrapper<Apply>()
                .select(Apply::getCarNumber)
                .eq(Apply::getProjectId, projectId)
                .in(Apply::getStatus, SmmConstants.APPLY_STATUS.NOT_IN, SmmConstants.APPLY_STATUS.NOT_BILLED), o -> (String) o);
    }

    @Override
    public List<String> notBilledCarNumbersWithSharing(String projectId) {
        List<String> projectIds = clientSharingService.listSharingProjectId(projectId);
        if (CollectionUtils.isNotEmpty(projectIds)) {
            projectIds.add(projectId);
            return this.listObjs(new LambdaQueryWrapper<Apply>()
                    .select(Apply::getCarNumber)
                    .in(Apply::getProjectId, projectIds)
                    .in(Apply::getStatus, SmmConstants.APPLY_STATUS.NOT_IN, SmmConstants.APPLY_STATUS.NOT_BILLED), o -> (String) o);
        }else {
            return this.notBilledCarNumbersByProjectId(projectId);
        }
    }


    @Override
    public boolean save(Apply entity) {
        entity.setCreateTime(DateUtils.getCurrentDate());
        if (StringUtils.isBlank(entity.getCreateUserId())) {
            entity.setCreateUserId(ShiroUtils.getUserId());
        }
        entity.setUpdateTime(DateUtils.getCurrentDate());
        if (StringUtils.isBlank(entity.getUpdateUserId())) {
            entity.setUpdateUserId(ShiroUtils.getUserId());
        }
        return super.save(entity);
    }

    @Override
    public boolean updateById(Apply entity) {
        entity.setUpdateTime(DateUtils.getCurrentDate());
        if (StringUtils.isBlank(entity.getUpdateUserId())) {
            entity.setUpdateUserId(ShiroUtils.getUserId());
        }
        return super.updateById(entity);
    }



    @Override
    public Boolean getByCreateUserIdExists(String[] createUserId) {
        return this.exists(new QueryWrapper<Apply>()
                .in("CREATE_USER_ID", createUserId)
                .in("STATUS", SmmConstants.APPLY_STATUS.NOT_IN, SmmConstants.APPLY_STATUS.NOT_BILLED));
    }

    @Override
    public boolean validateCarNumber(String carNumber, String projectId) {
        return this.exists(new QueryWrapper<Apply>()
                .eq("CAR_NUMBER", carNumber)
                .eq("PROJECT_ID", projectId)
                .in("STATUS", SmmConstants.APPLY_STATUS.NOT_IN, SmmConstants.APPLY_STATUS.NOT_BILLED));
    }

    @Override
    public boolean hasBillAuth(String applyId, String userId) {
        Apply apply = this.getById(applyId, "PROJECT_ID");
        if (apply == null) {
            return false;
        }
        //查出当前用户有权查看的所有项目
        ProjectQuery query = new ProjectQuery();
        List<String> projectIds = projectService.queryProjectIdByAuth(query);

        return projectIds.contains(apply.getProjectId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateApply(Apply apply) {

        //同一个车牌号，已存在未开单的申请，不能继续申请
        Apply sameNumberApply = this.getOne(new LambdaQueryWrapper<Apply>()
                .select(Apply::getCreateUserId, Apply::getId)
                .eq(Apply::getCarNumber, apply.getCarNumber())
                .in(Apply::getStatus, SmmConstants.APPLY_STATUS.NOT_IN, SmmConstants.APPLY_STATUS.NOT_BILLED)
                .ne(Apply::getId, apply.getId()));
        if (sameNumberApply != null) {
            SysUserEntity user = sysUserService.getById(sameNumberApply.getCreateUserId());
            throw new ServiceException(2025, MessageFormat.format(SmmErrorMsgConstants.EXIST_NOT_BILLED_APPLY_2025, apply.getCarNumber(), user == null ? "" : user.getShowName()));
        }
        //查出数据库中原有的信息，看项目ID有没有变化
        Apply applyInDB = this.getById(apply.getId(),"PROJECT_ID");

        boolean update = this.updateById(apply);

        if (update) {
            //修改运砂人签字图片
            boolean update1 = signatureService.update(
                    new UpdateWrapper<Signature>()
                            .set("IMG_CODE", apply.getDriverSignatureCode())
                            .eq("ID", apply.getDriverSignatureId()));
            if (!update1) {
                throw new ServiceException(1030, SmmErrorMsgConstants.DATA_VERSION_ERROR_1030);
            }
            //更新客户端车牌号列表
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization(){
                @Override
                public void afterCommit() {
                    sendMessageService.sendCarNumbers(apply.getProjectId());
                    if(!applyInDB.getProjectId().equals(apply.getProjectId())){
                        sendMessageService.sendCarNumbers(applyInDB.getProjectId());
                    }
                }
            });
        }



    }

    @Override
    public Apply getNotBilledApply(String carNumber) {
        return getOne(new LambdaQueryWrapper<Apply>()
                .eq(Apply::getCarNumber, carNumber)
                .in(Apply::getStatus, SmmConstants.APPLY_STATUS.NOT_IN, SmmConstants.APPLY_STATUS.NOT_BILLED)
                .last(" limit 1 "));
    }

    @Override
    public Apply getNotBilledApply(String carNumber, String projectId) {
        return getOne(new LambdaQueryWrapper<Apply>()
                .eq(Apply::getCarNumber, carNumber)
                .eq(Apply::getProjectId, projectId)
                .in(Apply::getStatus, SmmConstants.APPLY_STATUS.NOT_IN, SmmConstants.APPLY_STATUS.NOT_BILLED)
                .last(" limit 1 "));
    }

    @Override
    public AnNounCement getAnnouncement() {
        String userId = AuthUtils.getCurrentUserId();
        return baseMapper.selectAnnouncement(userId);
    }

    @Override
    public void updateAnnouncement(String status) {
        String userId = AuthUtils.getCurrentUserId();
        if(status.equals("true")){
            baseMapper.updateAnnouncementStatus(userId,status);
        }
    }

    @Override
    public void saveInData(SaveInDataDto dto) {
        Apply apply = this.getById(dto.getApplyId(), "ID", "STATUS");
        if (!SmmConstants.APPLY_STATUS.NOT_IN.equals(apply.getStatus())) {
            throw new ServiceException(2041, SmmErrorMsgConstants.APPLY_STATUS_ERROR_2041);
        }
        apply.setInWeight(dto.getInWeight());
        apply.setStatus(SmmConstants.APPLY_STATUS.NOT_BILLED);
        apply.setInImgIds(dto.getInImgIds());
        this.updateById(apply);
        //防止临时上传的附件被删除，设置一下ownerId
        uploadFileService.update(new LambdaUpdateWrapper<UploadFile>()
                .set(UploadFile::getOwnerId, apply.getId())
                .in(UploadFile::getId, dto.getInImgIds()));
    }

    @Override
    public void saveInOutData(TempDataPool tempDataPool) {
        //根据车牌号和项目id，获取申请数据
        Apply apply=this.getNotBilledApply(tempDataPool.getCarNumber());
        if (apply == null) {
            log.warn("没有找到运砂申请,carNumber={},weight={},dataType={}", tempDataPool.getCarNumber(), tempDataPool.getWeight(), tempDataPool.getDataType());
            return;
        }
        //如果是进场数据，设置进场
        if (SmmConstants.TempDataType.IN.equals(tempDataPool.getDataType())) {
            apply.setInDataId(tempDataPool.getId());
            apply.setInWeight(tempDataPool.getWeight());
            apply.setInStationId(tempDataPool.getStationId());
            apply.setInImgIds(tempDataPool.getImgFileIds());
            if (SmmConstants.APPLY_STATUS.NOT_IN.equals(apply.getStatus())) {
                apply.setStatus(SmmConstants.APPLY_STATUS.NOT_BILLED);
            }
        }else{
            apply.setOutDataId(tempDataPool.getId());
            apply.setOutWeight(tempDataPool.getWeight());
            apply.setOutStationId(tempDataPool.getStationId());
            apply.setOutImgIds(tempDataPool.getImgFileIds());
            //这里加一层判断，如果这个时候净重量小于1，说明是之前把出场数据错误提交成入场了。
            //这里查一下是否有正确的入场数据，如果有，自动回滚下入场
            if (apply.getInWeight() != null && (apply.getOutWeight().subtract(apply.getInWeight())).compareTo(new BigDecimal("1")) < 0) {
                List<TempDataPool> tempDataPools = tempDataPoolService.list(new LambdaQueryWrapper<TempDataPool>()
                        .eq(TempDataPool::getCarNumber, apply.getCarNumber())
                        .eq(TempDataPool::getProjectId, apply.getProjectId())
                        .eq(TempDataPool::getDataType, SmmConstants.TempDataType.IN)
                        .orderByDesc(TempDataPool::getCreateTime));
                if(tempDataPools!=null && tempDataPools.size()>1){
                    for (int i = 1; i < tempDataPools.size(); i++) {
                        TempDataPool temData = tempDataPools.get(i);
                        if (apply.getOutWeight().subtract(temData.getWeight()).compareTo(new BigDecimal("1")) > 0) {
                            apply.setInDataId(temData.getId());
                            apply.setInWeight(temData.getWeight());
                            apply.setInStationId(temData.getStationId());
                            apply.setInImgIds(temData.getImgFileIds());
                            break;
                        }
                    }
                }

            }
        }
        this.updateById(apply);
    }

    @Override
    public List<String> notBilledCarNumbersByClientId(String clientId) {
        List<String> projectIds = clientService.getProjectIdByClientIdWithSharing(clientId);

        return this.listObjs(new LambdaQueryWrapper<Apply>()
                .select(Apply::getCarNumber)
                .in(Apply::getProjectId, projectIds)
                .in(Apply::getStatus, SmmConstants.APPLY_STATUS.NOT_IN, SmmConstants.APPLY_STATUS.NOT_BILLED), o -> (String) o);
    }

    @Override
    public void setReIn(String id) {
        boolean update = this.update(new LambdaUpdateWrapper<Apply>()
                .set(Apply::getInDataId, "")
                .set(Apply::getOutDataId, "")
                .set(Apply::getInImgIds, null)
                .set(Apply::getOutImgIds, null)
                .set(Apply::getInWeight, null)
                .set(Apply::getOutWeight, null)
                .set(Apply::getStatus, SmmConstants.APPLY_STATUS.NOT_IN)
                .eq(Apply::getId, id)
                .ne(Apply::getStatus, SmmConstants.APPLY_STATUS.BILLED));
        if(!update){
            throw new ServiceException(2043, SmmErrorMsgConstants.APPLY_STATUS_ERROR_2043);
        }
    }

}
