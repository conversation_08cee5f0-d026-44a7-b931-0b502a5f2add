<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.client.dao.TempDataCopyDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.client.entity.TempDataCopy" id="tempDataCopyMap">
        <result property="id" column="ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="clientId" column="CLIENT_ID"/>
        <result property="projectId" column="PROJECT_ID"/>
        <result property="stationId" column="STATION_ID"/>
        <result property="carNumber" column="CAR_NUMBER"/>
        <result property="weight" column="WEIGHT"/>
        <result property="dataType" column="DATA_TYPE"/>
        <result property="imgFileIds" column="IMG_FILE_IDS"/>
    </resultMap>


</mapper>