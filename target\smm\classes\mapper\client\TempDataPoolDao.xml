<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.client.dao.TempDataPoolDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.client.entity.TempDataPool" id="tempDataPoolMap">
        <result property="id" column="ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="clientId" column="CLIENT_ID"/>
        <result property="stationId" column="STATION_ID"/>
        <result property="carNumber" column="CAR_NUMBER"/>
        <result property="weight" column="WEIGHT"/>
        <result property="imgFileIds" column="IMG_FILE_IDS"/>
    </resultMap>

    <select id="selectTempDataPoolDaoPage" resultType="com.ybkj.smm.modules.client.entity.TempDataPool">
        select
        tempDataPoolMap.ID,
        tempDataPoolMap.CREATE_TIME,
        tempDataPoolMap.CLIENT_ID,
        tempDataPoolMap.STATION_ID,
        tempDataPoolMap.CAR_NUMBER,
        tempDataPoolMap.WEIGHT,
        tempDataPoolMap.DATA_TYPE,
        tempDataPoolMap.IMG_FILE_IDS,
        project.NAME AS projectName,
        project.SECTION_NAME AS sectionName,
        station.NAME AS stationName
        from
        smm_client_temp_data_pool tempDataPoolMap
        LEFT JOIN smm_project_project project ON tempDataPoolMap.PROJECT_ID = project.DEPT_ID
        LEFT JOIN smm_project_weighing_station station ON tempDataPoolMap.STATION_ID = station.ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="getDataByNumber" resultType="com.ybkj.smm.modules.client.entity.TempDataPool"
            parameterType="java.lang.String">
        SELECT * FROM smm_client_temp_data_pool WHERE CAR_NUMBER = #{carNumber} AND DATA_TYPE = 'out' ORDER BY CREATE_TIME DESC LIMIT 1
    </select>
    <select id="selectTempDataPoolDaoList" resultType="com.ybkj.smm.modules.client.entity.TempDataPool">
        SELECT * FROM smm_client_temp_data_pool WHERE PROJECT_ID = #{projectId} AND CAR_NUMBER = #{carNumber}
    </select>

    <insert id="saveLiftRodData" parameterType="com.ybkj.smm.modules.client.entity.LiftRodData">
        INSERT INTO smm_take_pole_data
            (ID, CREATE_TIME, TYPE, CAR_NUMBER, CLIENT_ID, TAKE_OFF, BILL_NO,STATUS)
        VALUES (#{id}, #{createTime}, #{type}, #{carNumber}, #{clientId}, #{takeOff}, #{billNo},#{status})
    </insert>

    <insert id="saveAppealPoleLift" parameterType="com.ybkj.smm.modules.client.entity.LiftRodAppeal">
        INSERT INTO smm_appeal_pole_lift
            (ID, CREATE_TIME, CAR_NUMBER,PROJECT_ID, STATION_ID, CAUSE_APPEAL,STATUS,TIME_NUMBER)
        VALUES (#{id}, #{createTime}, #{carNumber},#{projectId}, #{stationId}, #{causeAppeal}, #{status}, #{timeNumber})
    </insert>

    <update id="updateAppealAuditStatus">
        UPDATE smm_appeal_pole_lift
        SET STATUS = #{status},
            APPROVER_USER_ID = #{userId}
        WHERE ID = #{id}
    </update>
    <update id="updateAppealAuditStatusReject">
        UPDATE smm_appeal_pole_lift
        SET STATUS = #{status},
            APPROVER_USER_ID = #{userId},
            REJECT_CAUSE = #{rejectCause}
        WHERE ID = #{id}
    </update>
    <update id="updateAppealTimeNumber">
        UPDATE smm_appeal_pole_lift
        SET TIME_NUMBER = #{timeNumber}
        WHERE ID = #{id}
    </update>

    <select id="getAppealData" resultType="com.ybkj.smm.modules.client.entity.LiftRodAppeal">
        SELECT *
        FROM smm_appeal_pole_lift
        WHERE STATION_ID = #{stationId}
          AND CAR_NUMBER = #{carNumber}
        ORDER BY CREATE_TIME DESC LIMIT 1
    </select>
    <select id="getLiftAppealData" resultType="com.ybkj.smm.modules.client.entity.LiftRodAppeal">
        SELECT *
        FROM smm_appeal_pole_lift sl
                 LEFT JOIN sys_dept sd
                           ON sd.DEPT_ID = sl.PROJECT_ID
                 LEFT JOIN smm_client_client sc
                           ON sc.STATION_ID = sl.STATION_ID
        WHERE (sd.DEPT_ID = #{deptId}
           OR
            sd.DEPT_ID IN (SELECT DEPT_ID FROM sys_dept WHERE DEL_FLAG = 0 AND DEPT_CODE LIKE CONCAT(#{deptCode}, '%')))
            AND sl.STATION_ID = #{stationId}
    </select>
    <select id="getAppealDataInfo" resultType="com.ybkj.smm.modules.client.entity.LiftRodAppeal">
        SELECT *
        FROM smm_appeal_pole_lift
        WHERE ID = #{id}
    </select>
</mapper>
