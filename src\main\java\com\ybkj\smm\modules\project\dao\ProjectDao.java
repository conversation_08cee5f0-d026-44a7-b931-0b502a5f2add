package com.ybkj.smm.modules.project.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ybkj.smm.modules.project.dto.ProjectReportDto;
import com.ybkj.smm.modules.project.entity.Project;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ybkj.smm.modules.project.query.ProjectStatisticsQuery;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 项目/沙场信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-21 14:10:16
 */
public interface ProjectDao extends BaseMapper<Project> {

    /**
     * 查询项目管理,使用MP分页方式
     *
     * @return 项目信息管理集合
     */
    Page<Project> selectProjectPage(Page<Project> page, @Param("ew") QueryWrapper<Project> ew);

    /**
     * <li>功能描述：根据ID查询数据，包括已删除的数据</li>
     *
     * @author: p<PERSON><PERSON><PERSON>@126.com
     * @date: 2023/10/27 16:02
     */
    Project getByIdWithDeleted(@Param("id") String id);

    /**
     *<li>功能描述：不分页查询列表</li>
     * @author: wangxiao
     * @date:  2024/7/4 14:46
     */
    List<Project> selectProjectList(@Param("ew") QueryWrapper<Project> ew);

    /**
     *<li>功能描述：获取开启终端的项目列表</li>
     * @author: wangxiao
     * @date:  2024/8/7 上午10:49
     */
    List<Project> listProjectNamesByUseClient();

    /**
     * 项目统计 查询项目列表,使用MP分页方式
     *
     * @return 项目统计信息集合
     */
    Page<Project> selectProjectPageByUser(Page<Project> page,
                                          @Param("userID") String userID,
                                          @Param("name") String name,
                                          @Param("sectionName") String sectionName,
                                          @Param("type") String type,
                                          @Param("areaCode") String areaCode,
                                          @Param("warningType") String warningType,
                                          @Param("sqlFilter") String sqlFilter);

    List<Project> selectProjectPageByUser(@Param("userID") String userID,
                                          @Param("name") String name,
                                          @Param("sectionName") String sectionName,
                                          @Param("type") String type,
                                          @Param("areaCode") String areaCode,
                                          @Param("warningType") String warningType,
                                          @Param("sqlFilter") String sqlFilter);

    /**
     *  月报表导出和月统计查询列表方法
     * @param areaCode
     * @param startTime
     * @param endTime
     * @param userID
     * @param sqlFilter
     * @return
     */
    List<Project> getProjectListByUser(@Param("areaCode") String areaCode,
                                       @Param("startTime") String startTime,
                                       @Param("endTime") String endTime,
                                       @Param("userID") String userID,
                                       @Param("sqlFilter") String sqlFilter);

    /**
     * 获取项目列表
     * @param userID
     * @param sqlFilter
     * @return
     */
    List<Project> getProjectList(@Param("userID") String userID,
                                 @Param("sqlFilter") String sqlFilter);


    /**
     * 获取标段列表
     * @param userID
     * @param name
     * @param sectionName
     * @param type
     * @param areaCode
     * @param warningType
     * @param sqlFilter
     * @return
     */
    List<Project> selectSectionList( @Param("userID") String userID,
                                     @Param("name") String name,
                                     @Param("sectionName") String sectionName,
                                     @Param("type") String type,
                                     @Param("areaCode") String areaCode,
                                     @Param("warningType") String warningType,
                                     @Param("sqlFilter") String sqlFilter);

    /**
     * 获取对应地市、区县统计信息列分页列表
     * @param page
     * @param ew
     * @param deptCodeLength
     * @param startTime
     * @param endTime
     * @return
     */
    Page<ProjectReportDto> selectProjectAreaPage(
            Page<ProjectReportDto> page,
            @Param("ew") QueryWrapper<ProjectReportDto> ew,
            @Param("deptCodeLength") String deptCodeLength,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime
    );

    /**
     * 获取对应区县所有项目的统计信息分页列表
     * @param page
     * @param ew
     * @param startTime
     * @param endTime
     * @return
     */
    Page<ProjectReportDto> selectProjectDetailsPage(
            Page<ProjectReportDto> page,
            @Param("ew")  QueryWrapper<ProjectReportDto> ew,
             @Param("startTime") String startTime,
            @Param("endTime") String endTime
    );

    /**
     * 获取项目汇总信息
     * @param ew
     * @param startTime
     * @param endTime
     */
    List<ProjectReportDto> selectProjectTotal( @Param("ew")  QueryWrapper<ProjectReportDto> ew,
                                              @Param("startTime") String startTime,
                                              @Param("endTime") String endTime);

    /**
     * 获取市本级项目汇总信息
     * @param ew
     * @param startTime
     * @param endTime
     */
    List<ProjectReportDto> selectCityTotal(@Param("ew") QueryWrapper<ProjectReportDto> ew,  @Param("startTime")String startTime, @Param("endTime")String endTime);

    /**
     *<li>功能描述：查询指定项目已完成的采砂总重量</li>
     * @author: 仕伟
     * @date:  2025/8/23
     * @param projectId 项目ID
     * @return 已采砂总重量（吨）
     */
    BigDecimal getMinedVolumeByProjectId(@Param("projectId") String projectId);
}
