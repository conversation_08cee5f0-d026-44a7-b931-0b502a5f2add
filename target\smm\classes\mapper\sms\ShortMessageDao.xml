<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.sms.dao.ShortMessageDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sloth.modules.sms.entity.ShortMessage" id="shortMessageMap">
        <result property="id" column="ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="sendTime" column="SEND_TIME"/>
        <result property="receiveTime" column="RECEIVE_TIME"/>
        <result property="mobile" column="MOBILE"/>
        <result property="signName" column="SIGN_NAME"/>
        <result property="templateId" column="TEMPLATE_ID"/>
        <result property="templateCode" column="TEMPLATE_CODE"/>
        <result property="templateParams" column="TEMPLATE_PARAMS"/>
        <result property="content" column="CONTENT"/>
        <result property="ownerId" column="OWER_ID"/>
        <result property="bizId" column="BIZ_ID"/>
        <result property="response" column="RESPONSE"/>
        <result property="charCount" column="CHAR_COUNT"/>
        <result property="smsCount" column="SMS_COUNT"/>
        <result property="status" column="STATUS"/>
        <result property="type" column="TYPE"/>
    </resultMap>
    <select id="selectPage2" resultType="com.sloth.modules.sms.entity.ShortMessage">
        select ssm.*, st.NAME as templateName
        from sms_short_message ssm
        join sms_template st on ssm.TEMPLATE_ID = st.ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>


</mapper>
