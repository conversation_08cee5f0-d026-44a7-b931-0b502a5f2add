<#import "/macro/macro.ftl" as my />
<!DOCTYPE html>
<html>
<@my.head jqgrid=true  validate=true>
    <title>报表设计表</title>
</@my.head>
<body>
<div id="app" v-cloak>
    <div v-show="showList=='list'">
        <div class="grid-btn form-inline">
            <div class="form-group">
                <a class="btn btn-default" @click="query"><i class="fa fa-search"></i>&nbsp;查询</a>
                <a class="btn btn-default" @click="queryAll"><i class="fa fa-refresh"></i>&nbsp;全部</a>
            </div>
			<#if shiro.hasPermission("report:reportdesign:save")>
            <div class="form-group">
            <a class="btn btn-primary" href="${request.contextPath}/ureport/designer" target="_blank"><i class="fa fa-plus"></i>&nbsp;报表设计</a>
            </div>
			</#if>
			<#if shiro.hasPermission("report:reportdesign:delete")>
            <div class="form-group">
            <a class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;批量删除</a>
            </div>
			</#if>
        </div>
        <table id="jqGrid"></table>
        <div id="jqGridPager"></div>
    </div>
    <div v-show="showList=='edit'" class="panel panel-default">
        <div class="panel-heading">{{title}}</div>
        <form class="form-horizontal" id="frm2">

            <div class="form-group">
                <div class="col-sm-3 control-label">报表名称</div>
                <div class="col-sm-9">
                    <input type="text" id="reportName" class="form-control" v-model.trim="reportDesign.name" placeholder="报表名称" maxlength="32" autocomplete="off"
                    rules="[{notNull:true,message:'报表名称不能为空'}]"/>
                </div>
            </div>

            <div class="form-group">
                <div class="col-sm-3 control-label">报表导出名称</div>
                <div class="col-sm-9">
                    <input type="text" id="exportName" class="form-control" v-model.trim="reportDesign.exportName" placeholder="报表导出名称" maxlength="255" autocomplete="off"
                           rules="[{notNull:true,message:'报表导出名称不能为空'}]"/>
                </div>
            </div>

            <div class="form-group">
                <div class="col-sm-3 control-label"></div>
                <input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
                &nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
            </div>
        </form>
    </div>
    <div v-if="showList=='params'">
        <div class="grid-btn form-inline">
            <div class="form-group">
                <a class="btn btn-primary" @click="addParam('0')"><i class="fa fa-plus"></i>&nbsp;新增</a>
            </div>
        </div>
        <table id="paramsGrid"></table>
        <form class="form-horizontal" style="width: 100%">
            <div class="form-group">
                <center>
                <input type="button" class="btn btn-primary" @click="saveParams" value="保存"/>
                &nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
                </center>
            </div>
        </form>
    </div>
    <#--弹出编辑参数窗口-->
    <div id="paramsLayer" style="display: none" v-show="false">
        <form class="form-horizontal" id="frm">
                <div class="form-group">
                    <div class="col-sm-3 control-label">type</div>
                    <div class="col-sm-9">
                        <select id="type" class="form-control" v-model.trim="param.type">
                            <option value="text">text</option>
                            <option value="radio">radio</option>
                        <#--<option value="checkbox">checkbox</option>-->
                            <option value="date">date</option>
                            <option value="select">select</option>
                            <option value="hidden">hidden</option>
                            <option value="fixed">fixed</option>
                            <option value="button">button</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-3 control-label">custom</div>
                    <div class="col-sm-9">
                        <textarea  id="custom" class="form-control" v-model.trim="param.custom" placeholder="自定义html代码，不为空时优先使用"></textarea>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-3 control-label">id</div>
                    <div class="col-sm-9">
                        <input type="text" id="id" class="form-control" v-model.trim="param.id" placeholder="控件的Id"
                               rules="[{notNull:true,message:'id不能为空'}]"/>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-3 control-label">name</div>
                    <div class="col-sm-9">
                        <input type="text" id="name" class="form-control" v-model.trim="param.name" placeholder="控件的name"
                               rules="[{notNull:true,message:'name不能为空'}]"/>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-3 control-label">readonly</div>
                    <div class="col-sm-9">
                        <div class="checkbox">
                            <label class="radio-inline">
                                <input type="radio" name="readonly"  v-model.trim="param.readonly" value="true"/>是
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="readonly"  v-model.trim="param.readonly" value="false"/>否
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-3 control-label">placeholder</div>
                    <div class="col-sm-9">
                        <input type="text" id="placeholder" class="form-control" v-model.trim="param.placeholder" placeholder="控件的placeholder"
                               :rules="param.type!='select'?'[{notNull:true,message:\'placeholder不能为空\'}]':''"/>
                    </div>
                </div>
                <div v-if="param.type=='radio'||param.type=='checkbox'||param.type=='select'" class="form-group">
                    <div class="col-sm-3 control-label">pvalue</div>
                    <div class="col-sm-9">
                        <input type="text" id="pvalue" class="form-control" v-model.trim="param.pvalue" placeholder="对应的字典值。radio，checkbox，select此项不能为空"
                               rules="[{notNull:true,message:'pvalue不能为空'}]" class="form-group"/>
                        <label for="pvalue" class="normal-label"></label>

                    </div>
                </div>
                <div v-if="param.type=='date'" class="form-group">
                    <div class="col-sm-3 control-label">datePattern</div>
                    <div class="col-sm-9">
                        <input type="text" id="datePattern" class="form-control" v-model.trim="param.datePattern" placeholder="日期格式"
                               /><#--rules="[{notNull:true,message:'datePattern不能为空'}]"-->
                        <label for="datePattern" class="normal-label"></label>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-3 control-label">event</div>
                    <div class="col-sm-9">
                        <textarea rows="6" id="event" class="form-control" v-model.trim="param.event" placeholder="控件绑定的事件，如onclick='showTree'"></textarea>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-3 control-label">script</div>
                    <div class="col-sm-9">
                        <textarea rows="6" id="script" class="form-control" v-model.trim="param.script" placeholder="如果有绑定事件，这里写事件对应的function"
                                  :rules="(param.event!='')?'[{notNull:true,message:\'event不为空时必须填写function定义\'}]':''"></textarea>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-3 control-label">hiddenHtml</div>
                    <div class="col-sm-9">
                        <textarea rows="6" id="hiddenHtml" class="form-control" v-model.trim="param.hiddenHtml" placeholder="其他辅助的html代码，一般为隐藏属性。针对ztree"></textarea>
                    </div>
                </div>
        </form>
    </div>
</div>

<script type="application/javascript">
   function loadParamsGrid(data){
       $("#paramsGrid").jqGrid({
           datatype: "local",
           colModel: [
               { label: '控件类型', name: 'type', index: 'type', width: 80 },
               { label: '控件ID', name: 'id', index: 'id', width: 80 ,key:true},
               { label: '控件name', name: 'name', index: 'name', width: 80},
               { label: '控件placeholder', name: 'placeholder', index: 'placeholder', width: 80 },
               { label: '操作',name:'caozuo',index:'',width:80,sortable: false, resize: false,
                   formatter: function (value,options,rowObject) {
                       var returnstr = "";
                       returnstr += "<a  href='javascript:void(0);' class='btn btn-xs btn-success' onclick='vm.addParam(\""+rowObject['id']+"\")' title='插入'><i class='fa fa-plus'></i></a>";
                       returnstr += "&nbsp;<a  href='javascript:void(0);' class='btn btn-xs btn-success' onclick='vm.updateParam(\""+rowObject['id']+"\")' title='编辑'><i class='fa fa-pencil-square-o'></i></a>";
                         returnstr += "&nbsp;<a  href='javascript:void(0);' class='btn btn-xs btn-success' onclick='vm.delParam(\""+rowObject['id']+"\")' title='删除'><i class='fa fa-trash-o'></i></a>";
                       return returnstr;
                   }
               }
           ],
           viewrecords: true,
           height: 'auto',
           rowNum: 99999,
           rowList : [10,30,50],
           rownumbers: true,
           rownumWidth: 55,
           autowidth:true,
           multiselect: false,
           data: data,
           pager: "",
           jsonReader: {
               root: data,
               page: 1,
               total: data.length,
               records: data.length
           },
           caption:'&nbsp;&nbsp;设计查询参数',
           //禁用操作按钮行点击事件
           beforeSelectRow: function (rowid, e) {
               var isBtn = $(e.target).hasClass("btn") || $(e.target).hasClass("fa");
               return !isBtn;
           },
           gridComplete:function(){
               //隐藏grid底部滚动条，和最小高度
               $("#paramsGrid").closest(".ui-jqgrid-bdiv").css({ "overflow-x" : "hidden","min-height" : "300px" });
               //显示排序图标
               addSortAbleCss(this);
           }
       }).setGridWidth($(window).width()-30);
   }
    $(function () {
        $("#jqGrid").jqGrid({
            url: baseURL + 'report/reportdesign/list',
            datatype: "json",
            colModel: [
                     { label: 'id', name: 'id', index: 'srd.ID', width: 50, key: true,hidden:true},
                     { label: '报表名称', name: 'name', index: 'srd.NAME', width: 80 },
                     { label: '导出名称', name: 'exportName', index: 'srd.EXPORT_NAME', width: 80 },
                     { label: '查询参数', name: 'params', index: 'srd.PARAMS', width: 80,formatter:function (value) {
                         if(value!=null)
                          value=value.replace(/</g,"< ");
                             return value
                         }},
                     { label: '创建时间', name: 'createTime', index: 'srd.CREATE_TIME', width: 80 },
                    { label: '创建人', name: 'createUserName', index: 'su.show_name', width: 80 },
                    { label: '操作',name:'caozuo',index:'',width:80,sortable: false, resize: false,
                    formatter: function (value,options,rowObject) {
                         var returnstr = "";
                         <#if shiro.hasPermission("report:reportdesign:update")>
                         returnstr += "<a  href='javascript:void(0);' class='btn btn-xs btn-success' onclick='vm.update(\""+rowObject['id']+"\")' title='编辑'><i class='fa fa-pencil-square-o'></i></a>";
                         returnstr += "&nbsp;<a  href='${request.contextPath}/ureport/designer?_u=db:"+rowObject['name']+"' target='_blank' class='btn btn-xs btn-success'  title='设计'><i class='fa fa-bar-chart'></i></a>";
                         returnstr += "&nbsp;<a  href='javascript:void(0);' class='btn btn-xs btn-success' onclick='vm.designParams(\""+rowObject['id']+"\")' title='设计查询参数'><i class='fa fa-pencil-square'></i></a>";
                         </#if>
                         <#if shiro.hasPermission("report:reportdesign:delete")>
                         returnstr += "&nbsp;<a  href='javascript:void(0);' class='btn btn-xs btn-success' onclick='vm.delOne(\""+rowObject['id']+"\")' title='删除'><i class='fa fa-trash-o'></i></a>";
                         </#if>
                         return returnstr;
                     }
                }
            ],
            viewrecords: true,
            height: 'auto',
            rowNum: 10,
            rowList : [10,30,50],
            rownumbers: true,
            rownumWidth: 55,
            autowidth:true,
            multiselect: true,
            pager: "#jqGridPager",
            jsonReader : {
                root: "page.list",
                page: "page.currPage",
                total: "page.totalPage",
                records: "page.totalCount"
            },
            prmNames : {
                page:"page",
                rows:"limit",
                order: "order"
            },
            //禁用操作按钮行点击事件
            beforeSelectRow: function (rowid, e) {
                var isBtn = $(e.target).hasClass("btn") || $(e.target).hasClass("fa");
                return !isBtn;
            },
            gridComplete:function(){
                //隐藏grid底部滚动条，和最小高度
                $("#jqGrid").closest(".ui-jqgrid-bdiv").css({ "overflow-x" : "hidden","min-height" : "300px" });
                //显示排序图标
                addSortAbleCss(this);
            }
        }).setGridWidth($(window).width());
    });
</script>
<script src="${request.contextPath}/statics/js/modules/report/reportdesign.js?_${sloth.version()}"></script>
</body>
</html>