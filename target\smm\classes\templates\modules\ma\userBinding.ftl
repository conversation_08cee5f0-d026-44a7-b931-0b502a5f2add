<#import "/macro/macro.ftl" as my />
<!DOCTYPE html>
<html>
<@my.head jqgrid=true component=true vueValidate=true elementUI=true ztree=true  ueditor=true>
    <title>微信用户与系统用户绑定关系表</title>
</@my.head>
<body>
<div id="app" v-cloak>
    <div v-show="showList">
        <div class="grid-btn form-inline">
            <div class="form-group">
                <a class="btn btn-default" @click="query"><i class="fa fa-search"></i>&nbsp;查询</a>
                <a class="btn btn-default" @click="queryAll"><i class="fa fa-refresh"></i>&nbsp;全部</a>
            </div>
			<#if shiro.hasPermission("ma:userBinding:save")>
            <div class="form-group">
            <a class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
            </div>
			</#if>
			<#if shiro.hasPermission("ma:userBinding:delete")>
            <div class="form-group">
            <a class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;批量删除</a>
            </div>
			</#if>
        </div>
        <a class="pull-right" @click="drawer=true">更多查询<i class="fa fa-plus"></i></a>
        <div class="clearfix"></div>
        <el-drawer
                :visible.sync="drawer"
                :with-header="false"
                direction="rtl">
            <div class="form-horizontal" style="width:100%;padding: 10px">
                <div class="form-group">
                </div>
                <div class="form-group">
                    <a class="btn btn-default" @click="query('drawer')"><i class="fa fa-search"></i>&nbsp;查询</a>
                    <a class="btn btn-default" @click="clearQ"><i class="fa fa-refresh"></i>&nbsp;清空</a>
                </div>
            </div>
        </el-drawer>
        <my-table url="${request.contextPath}/ma/userBinding/list" ref="userBindingTable" row-key="id">
            <el-table-column  label="主键" min-width="80" prop="id" sortable="custom" column-key="ID"></el-table-column>
            <el-table-column  label="用户ID" min-width="80" prop="userId" sortable="custom" column-key="USER_ID"></el-table-column>
            <el-table-column  label="小程序用户表ID" min-width="80" prop="maUserId" sortable="custom" column-key="MA_USER_ID"></el-table-column>
            <el-table-column  label="上次登录时间" min-width="80" prop="lastLoginTime" sortable="custom" column-key="LAST_LOGIN_TIME"></el-table-column>
            <el-table-column  label="上次访问时间" min-width="80" prop="lastAccessTime" sortable="custom" column-key="LAST_ACCESS_TIME"></el-table-column>
            <el-table-column  label="登录状态;true-已登录，false-未登录" min-width="80" prop="loginStatus" sortable="custom" column-key="LOGIN_STATUS"></el-table-column>
            <el-table-column  label="创建时间" min-width="80" prop="createTime" sortable="custom" column-key="CREATE_TIME"></el-table-column>
            <el-table-column label="操作" column-key="caozuo" min-width="80" fixed="right">
                <template slot-scope="scope">
                    <button @click="update(scope.row.id)" class="btn btn-xs btn-success" title="编辑" type="button"><i
                            class="fa fa-edit" aria-hidden="true"></i></button>
                    <button @click="delOne(scope.row.id)" class="btn btn-xs btn-success" title="删除" type="button"><i
                            class="fa fa-trash" aria-hidden="true"></i></button>
                </template>
            </el-table-column>
        </my-table>
    </div>

    <div v-show="!showList" class="panel panel-default">
        <div class="panel-heading">{{title}}</div>
        <form class="form-horizontal" id="frm">
														                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">用户ID</div>
                <div class="col-sm-9">
                    <my-input type="text" id="userId" v-model.trim="userBinding.userId" placeholder="用户ID"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">小程序用户表ID</div>
                <div class="col-sm-9">
                    <my-input type="text" id="maUserId" v-model.trim="userBinding.maUserId" placeholder="小程序用户表ID"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">上次登录时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="lastLoginTime" v-model.trim="userBinding.lastLoginTime" placeholder="上次登录时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">上次访问时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="lastAccessTime" v-model.trim="userBinding.lastAccessTime" placeholder="上次访问时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">登录状态;true-已登录，false-未登录</div>
                <div class="col-sm-9">
                    <my-input type="text" id="loginStatus" v-model.trim="userBinding.loginStatus" placeholder="登录状态;true-已登录，false-未登录"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">创建时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="createTime" v-model.trim="userBinding.createTime" placeholder="创建时间"></my-input>
                </div>
            </div>
							
            <div class="form-group center">
                <input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
                &nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
            </div>
        </form>
    </div>
</div>
<script src="${request.contextPath}/statics/js/modules/ma/userBinding.js?_${sloth.version()}"></script>
</body>
</html>
