<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.sys.dao.UserDataDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sloth.modules.sys.entity.UserData" id="userDataMap">
        <result property="id" column="ID"/>
        <result property="dataGroupId" column="DATA_GROUP_ID"/>
        <result property="userId" column="USER_ID"/>
    </resultMap>


</mapper>