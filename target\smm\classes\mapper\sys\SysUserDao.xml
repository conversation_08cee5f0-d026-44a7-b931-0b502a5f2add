<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.sys.dao.SysUserDao">

	<!-- 查询用户的所有权限 -->
	<select id="queryAllPerms" resultType="String">
		select m.perms from sys_user_role ur
			LEFT JOIN sys_role_menu rm on ur.role_id = rm.role_id
			LEFT JOIN sys_menu m on rm.menu_id = m.menu_id
		where ur.user_id = #{userId}
	</select>

	<!-- 查询用户的所有菜单ID -->
	<select id="queryAllMenuId" resultType="String">
		select distinct rm.menu_id from sys_user_role ur
			LEFT JOIN sys_role_menu rm on ur.role_id = rm.role_id
		<if test="userId!=null and userId !=''">
			where ur.user_id = #{userId}
		</if>
	</select>

	<select id="selectUserPage" resultType="com.sloth.modules.sys.entity.SysUserEntity">
		select su.*, sd.name deptName, a.ROLE_NAMES
			from sys_user su
			left join sys_dept sd on su.dept_id=sd.dept_id
			left join (
			    select sur.USER_ID, concat(',',GROUP_CONCAT(DISTINCT sur.ROLE_ID),',') ROLE_IDS,
					GROUP_CONCAT(sr.ROLE_NAME) AS ROLE_NAMES
			    from sys_user_role sur, sys_role sr
			    where sur.ROLE_ID = sr.ROLE_ID
			    group by sur.USER_ID
		) a on su.USER_ID=a.USER_ID
			<where>
				${ew.sqlSegment}
			</where>
	</select>

	<select id="selectDeptUserTree" resultType="java.util.Map">
		select a.* from(
		select d.DEPT_ID id,IFNULL(d.PARENT_ID,0) parentId,d.NAME name,case when type='company' then '${params.ctx}/statics/plugins/ztree/css/metroStyle/img/company.png' else '${params.ctx}/statics/plugins/ztree/css/metroStyle/img/dept.png' end icon,
		'dept' type,d.type deptType,d.ORDER_NUM,d.DEPT_CODE from sys_dept d
		where d.DEl_FLAG=0
		<if test="params.deptCode!=null and params.deptCode!=''">
			and d.DEPT_CODE like #{params.deptCode}
		</if>
		<if test="params.roleIds!=null and params.roleIds!=''">
			and exists(
			select 1 from sys_user as u,sys_user_role as ur,sys_dept as d2
			where u.USER_ID=ur.USER_ID and u.DEL_FLAG=0 and d2.DEL_FLAG=0 and ur.ROLE_ID in (
			<foreach collection="params.roleIdList" item="roleId" separator=",">#{roleId}</foreach>
			) and (u.DEPT_ID=d.DEPT_ID or u.DEPT_ID=d2.DEPT_ID and d2.PARENT_ID =d.DEPT_ID)
			)
		</if>
		union
		select u.USER_ID id,u.DEPT_ID parentId,u.SHOW_NAME name,'${params.ctx}/statics/plugins/ztree/css/metroStyle/img/user2.png' icon,
		       'user' type,null deptType,null ORDER_NUM,null DEPT_CODE from sys_user u left join sys_dept d on u.DEPT_ID=d.DEPT_ID
			where u.USERNAME not in('system','admin') and u.DEL_FLAG=0 and d.DEL_FLAG=0
			<if test="params.deptCode!=null and params.deptCode!=''">
				and d.DEPT_CODE like #{params.deptCode}
			</if>
			<if test="params.roleIds!=null and params.roleIds!=''">
				and exists(select 1 from sys_user_role ur where ur.USER_ID=u.USER_ID
					and ur.ROLE_ID in (
						<foreach collection="params.roleIdList" item="roleId" separator=",">#{roleId}</foreach>
				))
			</if>
         )a order by a.deptType,a.ORDER_NUM,a.DEPT_CODE
	</select>

	<select id="getByIdWithDeleted" resultType="com.sloth.modules.sys.entity.SysUserEntity">
		select *
		from sys_user
		where
		USER_ID=#{userId}
		<if test="!withDeleted">and !DEL_FLAG</if>
	</select>
	<select id="getRoleNameByUserId" resultType="java.lang.String" parameterType="java.util.List">
		SELECT ROLE_NAME FROM sys_role WHERE ROLE_ID IN (SELECT ROLE_ID FROM sys_user_role WHERE USER_ID = #{currentUserId})
	</select>
	<select id="selectUserPageSG" resultType="com.sloth.modules.sys.entity.SysUserEntity">
		SELECT
		su.*,
		sd.name AS deptName,
		a.ROLE_NAMES
		FROM sys_user su
		LEFT JOIN sys_dept sd ON su.dept_id = sd.dept_id
		LEFT JOIN (
		SELECT
		sur.USER_ID,
		CONCAT(',', GROUP_CONCAT(DISTINCT sur.ROLE_ID), ',') AS ROLE_IDS,
		GROUP_CONCAT(sr.ROLE_NAME) AS ROLE_NAMES
		FROM sys_user_role sur
		JOIN sys_role sr ON sur.ROLE_ID = sr.ROLE_ID
		GROUP BY sur.USER_ID
		) a ON su.USER_ID = a.USER_ID
		WHERE su.MOBILE IN (
		SELECT spd.MOBILE
		FROM smm_project_driver spd
		WHERE spd.PROJECT_ID = (
		SELECT DEPT_ID FROM sys_user WHERE USER_ID = #{currentUserId}
		)
		)
		<if test="ew != null and ew.sqlSegment != null">
			AND ${ew.sqlSegment}
		</if>
	</select>
	<select id="getAreaLevel" resultType="java.lang.Integer" parameterType="java.lang.String">
		SELECT sa.AREA_LEVEL FROM sys_area sa LEFT JOIN sys_dept sd ON sa.AREA_CODE = sd.AREA_CODE WHERE sd.DEPT_ID = #{deptId}
	</select>
	<select id="selectUserPageSJ" resultType="com.sloth.modules.sys.entity.SysUserEntity">
		SELECT
		su.*,
		sd.name AS deptName,
		a.ROLE_NAMES
		FROM sys_user su
		LEFT JOIN sys_dept sd ON su.dept_id = sd.dept_id
		LEFT JOIN (
		SELECT
		sur.USER_ID,
		CONCAT(',', GROUP_CONCAT(DISTINCT sur.ROLE_ID), ',') AS ROLE_IDS,
		GROUP_CONCAT(sr.ROLE_NAME) AS ROLE_NAMES
		FROM sys_user_role sur
		JOIN sys_role sr ON sur.ROLE_ID = sr.ROLE_ID
		GROUP BY sur.USER_ID
		) a ON su.USER_ID = a.USER_ID
		WHERE su.MOBILE IN (
		    <foreach collection="mobileList" item="mobile" separator=",">#{mobile}</foreach>
		)
		<if test="ew != null and ew.sqlSegment != null">
			AND ${ew.sqlSegment}
		</if>
	</select>
	<select id="getDeptIdByDeptCode" resultType="java.lang.String" parameterType="java.lang.String">
		SELECT DEPT_ID FROM sys_dept
		WHERE DEPT_CODE LIKE CONCAT(#{deptCode}, '%') AND TYPE = 'sand'
	</select>
	<select id="getMobileByDeptId" resultType="java.lang.String"
			parameterType="java.util.List">
		SELECT DISTINCT MOBILE FROM smm_project_driver WHERE PROJECT_ID IN (
		    <foreach collection="deptIdList" item="deptId" separator=",">#{deptId}</foreach>
			)
	</select>
	<select id="getdeptTypeByDeptCode" resultType="java.lang.String" parameterType="java.lang.String">
		SELECT TYPE FROM sys_dept WHERE DEPT_CODE = #{deptCode}
	</select>

</mapper>
