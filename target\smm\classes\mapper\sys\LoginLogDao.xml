<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.sys.dao.LoginLogDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sloth.modules.sys.entity.LoginLog" id="loginLogMap">
        <result property="id" column="ID"/>
        <result property="userId" column="USER_ID"/>
        <result property="userName" column="USER_NAME"/>
        <result property="loginTime" column="LOGIN_TIME"/>
        <result property="logoutTime" column="LOGOUT_TIME"/>
        <result property="onlineTimeLength" column="ONLINE_TIME_LENGTH"/>
        <result property="loginIp" column="LOGIN_IP"/>
        <result property="remark" column="REMARK"/>
    </resultMap>

    <!--查询列表-->
    <select id="queryPage" resultType="com.sloth.modules.sys.entity.LoginLog">
        SELECT log.ID, log.USER_ID, u.SHOW_NAME AS USER_NAME, log.LOGIN_TIME, log.LOGOUT_TIME, log.ONLINE_TIME_LENGTH, log.LOGIN_IP, log.REMARK
        FROM sys_login_log log, sys_user u
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

</mapper>