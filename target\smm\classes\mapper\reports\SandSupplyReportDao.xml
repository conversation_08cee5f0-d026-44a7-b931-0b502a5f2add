<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.reports.dao.SandSupplyReportDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.reports.entity.SandSupplyReport" id="sandSupplyReportMap">
        <result property="id" column="ID"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="deptId" column="DEPT_ID"/>
        <result property="deptCode" column="DEPT_CODE"/>
        <result property="areaCode" column="AREA_CODE"/>
        <result property="year" column="YEAR"/>
        <result property="quarter" column="QUARTER"/>
        <result property="price" column="PRICE"/>
        <result property="status" column="STATUS"/>
        <result property="reportUserId" column="REPORT_USER_ID"/>
        <result property="reportTime" column="REPORT_TIME"/>
    </resultMap>

    <select id="queryCitySupplyReport" resultType="com.ybkj.smm.modules.reports.entity.SandSupplyReport">
        SELECT
             DEPT_ID,
             DEPT_CODE,
             AREA_CODE
        FROM
            sys_dept
            where LENGTH(DEPT_CODE) = 9 and type = "dept" and DEL_FLAG = 0
    </select>

    <select id="queryCitySupplyReportHistory" resultType="com.ybkj.smm.modules.reports.dto.SandSupplyReportDto">
        SELECT
        supply.ID,
        supply.STATUS,
        area.AREA_NAME,
        supply.`YEAR`,
        supply.`QUARTER`,
        IFNULL(stat.quarterSandTotal,0) quarterSandTotal,
        IFNULL(stat2.quarterSandTotal,0) yearSandTotal,
        IFNULL(stat.quarterAbandonTotal,0) quarterAbandonTotal,
        IFNULL(stat2.quarterAbandonTotal,0) yearAbandonTotal,
        supply.PRICE
        FROM
        smm_reports_sand_supply_report supply
        LEFT JOIN (
        SELECT
        SUBSTR(DEPT_CODE,1,9) deptCode,
        `YEAR`,
        `QUARTER`,
        SUM( SAND_TOTAL_VEHICLE_LOAD ) quarterSandTotal,
        SUM( ABANDON_TOTAL_VEHICLE_LOAD ) quarterAbandonTotal
        FROM
        smm_reports_area_stat
        where `year` = #{year}
        <if test='quarter != null &amp;&amp; quarter != "" '>
            and `QUARTER` = #{quarter}
        </if>
        GROUP BY
        `YEAR`,
        `QUARTER`,
        SUBSTR(DEPT_CODE,1,9)
        ) stat on stat.deptCode = supply.DEPT_CODE and stat.`YEAR` = supply.`YEAR`
        LEFT JOIN (
        SELECT
        SUBSTR( DEPT_CODE, 1, 9 ) deptCode,
        `YEAR`,
        `QUARTER`,
        SUM( SAND_TOTAL_VEHICLE_LOAD ) quarterSandTotal,
        SUM( ABANDON_TOTAL_VEHICLE_LOAD ) quarterAbandonTotal
        FROM
        smm_reports_area_stat
        WHERE
        `year` = #{year}
        <if test='quarter != null &amp;&amp; quarter != "" '>
            and `QUARTER` &lt;= #{quarter}
        </if>
        GROUP BY
        `YEAR`,
        SUBSTR( DEPT_CODE, 1, 9 )
        ) stat2 ON stat2.deptCode = supply.DEPT_CODE AND stat2.`YEAR` = supply.`YEAR`

        LEFT JOIN sys_area area on supply.AREA_CODE = area.AREA_CODE
        LEFT JOIN sys_dept dept on dept.dept_id = supply.dept_id
        where supply.`year` = #{year}
        <if test='quarter != null &amp;&amp; quarter != "" '>
            and supply.`QUARTER` = #{quarter}
        </if>
        <if test='areaCode != null &amp;&amp; areaCode != "" '>
            and area.AREA_CODE = #{areaCode}
        </if>
        <if test='status != null &amp;&amp; status != "" '>
            AND supply.STATUS = #{status}
        </if>
        <if test='sqlFilter != null &amp;&amp; sqlFilter != "" '>
            and ${sqlFilter}
        </if>
        GROUP BY supply.DEPT_CODE, supply.`year`,supply.`QUARTER`
    </select>

    <select id="queryCitySupplyReportCurrent" resultType="com.ybkj.smm.modules.reports.dto.SandSupplyReportDto">
        SELECT
            supply.ID,
            supply.STATUS,
            supply.`YEAR`,
            area.AREA_NAME,
            supply.`QUARTER`,
            IFNULL(SUM(stat.quarterSandTotal),0) quarterSandTotal,
            IFNULL(SUM(stat.sandTotal),0) yearSandTotal,
            IFNULL(SUM(quarterAbandonTotal),0) quarterAbandonTotal,
            IFNULL(SUM(stat.abandonTotal),0) yearAbandonTotal,
            IFNULL(supply.PRICE,0) price
        FROM
            smm_reports_sand_supply_report supply
            LEFT JOIN (
                SELECT
                b.DEPT_CODE,
                SUM(case when b.TYPE = 'riverSand' and LOCATE( SUBSTR(a.CREATE_TIME,6,2),#{monthStr}) > 0 then a.VEHICLE_LOAD else 0 end ) quarterSandTotal,
                SUM( CASE WHEN b.TYPE = 'riverSand' THEN a.VEHICLE_LOAD ELSE 0 END ) sandTotal,
                SUM(case when b.TYPE = 'abandonSand' and LOCATE( SUBSTR(a.CREATE_TIME,6,2),#{monthStr}) > 0 then a.VEHICLE_LOAD else 0 end ) quarterAbandonTotal,
                SUM( CASE WHEN b.TYPE = 'abandonSand' THEN a.VEHICLE_LOAD ELSE 0 END ) abandonTotal
            FROM
                smm_bill_bill a
                LEFT JOIN smm_project_project b on a.PROJECT_ID = b.DEPT_ID
                where b.DELETED = 0 and SUBSTR(a.CREATE_TIME,1,4) = #{year}
                GROUP BY a.PROJECT_ID
            ) stat on SUBSTR(stat.DEPT_CODE,1,9) = supply.DEPT_CODE
            LEFT JOIN sys_area area on supply.AREA_CODE = area.AREA_CODE
            LEFT JOIN sys_dept dept on dept.dept_id = supply.dept_id
            where supply.`year` = #{year} and supply.`QUARTER` = #{quarter}
            <if test='areaCode != null &amp;&amp; areaCode != "" '>
                and area.AREA_CODE = #{areaCode}
            </if>
            <if test='status != null &amp;&amp; status != "" '>
                AND supply.STATUS = #{status}
            </if>
            <if test='sqlFilter != null &amp;&amp; sqlFilter != "" '>
                and ${sqlFilter}
            </if>
            GROUP BY supply.DEPT_CODE

    </select>

    <select id="queryCitySupplyReportAll" resultType="com.ybkj.smm.modules.reports.dto.SandSupplyReportDto">
        select result.* from(
        SELECT
        supply.ID,
        supply.STATUS,
        area.AREA_NAME,
        supply.`YEAR`,
        supply.`QUARTER`,
        IFNULL(stat.quarterSandTotal,0) quarterSandTotal,
        IFNULL(stat2.quarterSandTotal,0) yearSandTotal,
        IFNULL(stat.quarterAbandonTotal,0) quarterAbandonTotal,
        IFNULL(stat2.quarterAbandonTotal,0) yearAbandonTotal,
        IFNULL(supply.PRICE,0) price
        FROM
        smm_reports_sand_supply_report supply
        LEFT JOIN (
        SELECT
        SUBSTR(DEPT_CODE,1,9) deptCode,
        `YEAR`,
        `QUARTER`,
        SUM( SAND_TOTAL_VEHICLE_LOAD ) quarterSandTotal,
        SUM( ABANDON_TOTAL_VEHICLE_LOAD ) quarterAbandonTotal
        FROM
        smm_reports_area_stat
        where `year` &lt;&gt; #{year} or `QUARTER` &lt;&gt; #{quarter}
        GROUP BY
        `YEAR`,
        `QUARTER`,
        SUBSTR(DEPT_CODE,1,9)
        ) stat on stat.deptCode = supply.DEPT_CODE and stat.`YEAR` = supply.`YEAR`

        LEFT JOIN (
        SELECT
        SUBSTR( DEPT_CODE, 1, 9 ) deptCode,
        `YEAR`,
        `QUARTER`,
        SUM( SAND_TOTAL_VEHICLE_LOAD ) quarterSandTotal,
        SUM( ABANDON_TOTAL_VEHICLE_LOAD ) quarterAbandonTotal
        FROM
        smm_reports_area_stat
        WHERE
        `year` = #{year}
        <if test='quarter != null &amp;&amp; quarter != "" '>
            and `QUARTER` &lt;= #{quarter}
        </if>
        GROUP BY
        `YEAR`,
        SUBSTR( DEPT_CODE, 1, 9 )
        ) stat2 ON stat2.deptCode = supply.DEPT_CODE AND stat2.`YEAR` = supply.`YEAR`

        LEFT JOIN sys_area area on supply.AREA_CODE = area.AREA_CODE
        LEFT JOIN sys_dept dept on dept.dept_id = supply.dept_id
        where (supply.`year` &lt;&gt; #{year} or supply.`QUARTER` &lt;&gt; #{quarter} )
        <if test='areaCode != null &amp;&amp; areaCode != "" '>
            and area.AREA_CODE = #{areaCode}
        </if>
        <if test='status != null &amp;&amp; status != "" '>
            AND supply.STATUS = #{status}
        </if>
        <if test='sqlFilter != null &amp;&amp; sqlFilter != "" '>
            and ${sqlFilter}
        </if>
        GROUP BY supply.DEPT_CODE, supply.`year`,supply.`QUARTER`

        union all

        SELECT
        supply.ID,
        supply.STATUS,
        area.AREA_NAME,
        supply.`YEAR`,
        supply.`QUARTER`,
        IFNULL(SUM(stat.quarterSandTotal),0) quarterSandTotal,
        IFNULL(SUM(stat.sandTotal),0) yearSandTotal,
        IFNULL(SUM(quarterAbandonTotal),0) quarterAbandonTotal,
        IFNULL(SUM(stat.abandonTotal),0) yearAbandonTotal,
        IFNULL(supply.PRICE,0) price
        FROM
        smm_reports_sand_supply_report supply
        LEFT JOIN (
        SELECT
        b.DEPT_CODE,
        SUM(case when b.TYPE = 'riverSand' and LOCATE( SUBSTR(a.CREATE_TIME,6,2),#{monthStr}) > 0 then a.VEHICLE_LOAD
        else 0 end ) quarterSandTotal,
        SUM( CASE WHEN b.TYPE = 'riverSand' THEN a.VEHICLE_LOAD ELSE 0 END ) sandTotal,
        SUM(case when b.TYPE = 'abandonSand' and LOCATE( SUBSTR(a.CREATE_TIME,6,2),#{monthStr}) > 0 then a.VEHICLE_LOAD
        else 0 end ) quarterAbandonTotal,
        SUM( CASE WHEN b.TYPE = 'abandonSand' THEN a.VEHICLE_LOAD ELSE 0 END ) abandonTotal
        FROM
        smm_bill_bill a
        LEFT JOIN smm_project_project b on a.PROJECT_ID = b.DEPT_ID
        where b.DELETED = 0 and SUBSTR(a.CREATE_TIME,1,4) = #{year}
        GROUP BY a.PROJECT_ID
        ) stat on SUBSTR(stat.DEPT_CODE,1,9) = supply.DEPT_CODE
        LEFT JOIN sys_area area on supply.AREA_CODE = area.AREA_CODE
        LEFT JOIN sys_dept dept on dept.dept_id = supply.dept_id
        where supply.`year` = #{year} and supply.`QUARTER` = #{quarter}
        <if test='areaCode != null &amp;&amp; areaCode != "" '>
            and area.AREA_CODE = #{areaCode}
        </if>
        <if test='status != null &amp;&amp; status != "" '>
            AND supply.STATUS = #{status}
        </if>
        <if test='sqlFilter != null &amp;&amp; sqlFilter != "" '>
            and ${sqlFilter}
        </if>
        GROUP BY supply.DEPT_CODE
        ) AS result
        order by result.`YEAR` desc,result.`QUARTER` desc
    </select>

</mapper>
