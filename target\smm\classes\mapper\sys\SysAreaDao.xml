<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.sys.dao.SysAreaDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sloth.modules.sys.entity.SysAreaEntity" id="sysAreaMap">
        <result property="id" column="ID"/>
        <result property="areaCode" column="AREA_CODE"/>
        <result property="areaName" column="AREA_NAME"/>
        <result property="parentCode" column="PARENT_ID"/>
        <result property="selfCode" column="PARENT_ID"/>
        <result property="areaLevel" column="AREA_LEVEL"/>
        <result property="delFlag" column="DEL_FLAG"/>
    </resultMap>

    <!-- 更新区域编码（含父编码） -->
    <update id="updateAreaCode" parameterType="map">
		UPDATE sys_area
        SET AREA_CODE = REPLACE(AREA_CODE, #{oldAreaCode}, #{newAreaCode}),
            PARENT_CODE = REPLACE(PARENT_CODE, #{oldAreaCode}, #{newAreaCode})
        WHERE AREA_CODE LIKE CONCAT(#{oldAreaCode}, '%')
	</update>
</mapper>