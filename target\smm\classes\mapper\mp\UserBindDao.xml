<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.mp.dao.UserBindDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sloth.modules.mp.entity.UserBind" id="userBindMap">
        <result property="id" column="ID"/>
        <result property="userId" column="USER_ID"/>
        <result property="openId" column="OPEN_ID"/>
        <result property="openIdKf" column="OPEN_ID_KF"/>
        <result property="unionId" column="UNION_ID"/>
        <result property="nickName" column="NICK_NAME"/>
        <result property="createTime" column="CREATE_TIME"/>
    </resultMap>
    <select id="selectOpenIds" resultType="java.lang.String">
        select OPEN_ID from mp_user_bind
        <where>
            ${ew.sqlSegment}
        </where>
    </select>


</mapper>