<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.video.dao.LiveCameraDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.video.entity.LiveCamera" id="liveCameraMap">
        <result property="id" column="ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="deptId" column="DEPT_ID"/>
        <result property="deptCode" column="DEPT_CODE"/>
        <result property="name" column="NAME"/>
        <result property="longitude" column="LONGITUDE"/>
        <result property="latitude" column="LATITUDE"/>
        <result property="platform" column="PLATFORM"/>
        <result property="platformSn" column="PLATFORM_SN"/>
        <result property="channel" column="CHANNEL"/>
        <result property="appKey" column="APP_KEY"/>
        <result property="appSecret" column="APP_SECRET"/>
    </resultMap>
    <select id="queryDeptList" resultType="com.sloth.modules.sys.entity.SysDeptEntity">
        SELECT dept.*, parent.NAME parentName,config.HAS_CAMERA hasCamera
        FROM sys_dept dept
        LEFT JOIN sys_dept parent ON dept.PARENT_ID = parent.DEPT_ID
        left join smm_project_project_config config ON  dept.DEPT_ID = config.DEPT_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>
