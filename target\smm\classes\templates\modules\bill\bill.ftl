<#import "/macro/macro.ftl" as my />
<!DOCTYPE html>
<html>
<@my.head jqgrid=true component=true vueValidate=true elementUI=true ztree=true  ueditor=true>
    <title>运砂单据表</title>
</@my.head>
<body>
<div id="app" v-cloak>
    <div v-show="showList">
        <div class="grid-btn form-inline">
            <div class="form-group">
                <a class="btn btn-default" @click="query"><i class="fa fa-search"></i>&nbsp;查询</a>
                <a class="btn btn-default" @click="queryAll"><i class="fa fa-refresh"></i>&nbsp;全部</a>
            </div>
			<#if shiro.hasPermission("bill:bill:save")>
            <div class="form-group">
            <a class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
            </div>
			</#if>
			<#if shiro.hasPermission("bill:bill:delete")>
            <div class="form-group">
            <a class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;批量删除</a>
            </div>
			</#if>
        </div>
        <a class="pull-right" @click="drawer=true">更多查询<i class="fa fa-plus"></i></a>
        <div class="clearfix"></div>
        <el-drawer
                :visible.sync="drawer"
                :with-header="false"
                direction="rtl">
            <div class="form-horizontal" style="width:100%;padding: 10px">
                <div class="form-group">
                </div>
                <div class="form-group">
                    <a class="btn btn-default" @click="query('drawer')"><i class="fa fa-search"></i>&nbsp;查询</a>
                    <a class="btn btn-default" @click="clearQ"><i class="fa fa-refresh"></i>&nbsp;清空</a>
                </div>
            </div>
        </el-drawer>
        <my-table url="${request.contextPath}/bill/bill/list" ref="billTable" row-key="id">
            <el-table-column  label="id 主键" min-width="80" prop="id" sortable="custom" column-key="ID"></el-table-column>
            <el-table-column  label="创建人id" min-width="80" prop="createUserId" sortable="custom" column-key="CREATE_USER_ID"></el-table-column>
            <el-table-column  label="修改人id" min-width="80" prop="updateUserId" sortable="custom" column-key="UPDATE_USER_ID"></el-table-column>
            <el-table-column  label="创建时间" min-width="80" prop="createTime" sortable="custom" column-key="CREATE_TIME"></el-table-column>
            <el-table-column  label="修改时间" min-width="80" prop="updateTime" sortable="custom" column-key="UPDATE_TIME"></el-table-column>
            <el-table-column  label="单据编号" min-width="80" prop="billNo" sortable="custom" column-key="BILL_NO"></el-table-column>
            <el-table-column  label="项目ID" min-width="80" prop="projectId" sortable="custom" column-key="PROJECT_ID"></el-table-column>
            <el-table-column  label="车牌号" min-width="80" prop="carNumber" sortable="custom" column-key="CAR_NUMBER"></el-table-column>
            <el-table-column  label="所属公司/个人" min-width="80" prop="carCompany" sortable="custom" column-key="CAR_COMPANY"></el-table-column>
            <el-table-column  label="品牌型号" min-width="80" prop="carBrand" sortable="custom" column-key="CAR_BRAND"></el-table-column>
            <el-table-column  label="车主姓名" min-width="80" prop="carOwnerName" sortable="custom" column-key="CAR_OWNER_NAME"></el-table-column>
            <el-table-column  label="自重（吨）" min-width="80" prop="carKerbWeight" sortable="custom" column-key="CAR_KERB_WEIGHT"></el-table-column>
            <el-table-column  label="最大载重（吨）" min-width="80" prop="carMaxPayload" sortable="custom" column-key="CAR_MAX_PAYLOAD"></el-table-column>
            <el-table-column  label="运砂人" min-width="80" prop="driverName" sortable="custom" column-key="DRIVER_NAME"></el-table-column>
            <el-table-column  label="联系电话" min-width="80" prop="driverMobile" sortable="custom" column-key="DRIVER_MOBILE"></el-table-column>
            <el-table-column  label="实际载运量" min-width="80" prop="vehicleLoad" sortable="custom" column-key="VEHICLE_LOAD"></el-table-column>
            <el-table-column  label="卸砂地点" min-width="80" prop="destination" sortable="custom" column-key="DESTINATION"></el-table-column>
            <el-table-column  label="卸砂地点经度" min-width="80" prop="destLongitude" sortable="custom" column-key="DEST_LONGITUDE"></el-table-column>
            <el-table-column  label="卸砂地点维度" min-width="80" prop="destLatitude" sortable="custom" column-key="DEST_LATITUDE"></el-table-column>
            <el-table-column  label="预计到达时间" min-width="80" prop="estimatedArrivalTime" sortable="custom" column-key="ESTIMATED_ARRIVAL_TIME"></el-table-column>
            <el-table-column  label="卸砂点负责人" min-width="80" prop="destLeader" sortable="custom" column-key="DEST_LEADER"></el-table-column>
            <el-table-column  label="负责人电话" min-width="80" prop="destLeaderMobile" sortable="custom" column-key="DEST_LEADER_MOBILE"></el-table-column>
            <el-table-column  label="现场签字时间" min-width="80" prop="signTime" sortable="custom" column-key="SIGN_TIME"></el-table-column>
            <el-table-column  label="主管部门签字时间" min-width="80" prop="auditTime" sortable="custom" column-key="AUDIT_TIME"></el-table-column>
            <el-table-column  label="状态;字典值： waitingAduit-待审核、inTransit-在运、completed-已核销" min-width="80" prop="status" sortable="custom" column-key="STATUS"></el-table-column>
            <el-table-column  label="承运人签字" min-width="80" prop="driverSignatureId" sortable="custom" column-key="DRIVER_SIGNATURE_ID"></el-table-column>
            <el-table-column  label="采砂人签字" min-width="80" prop="projectLeaderSignatureId" sortable="custom" column-key="PROJECT_LEADER_SIGNATURE_ID"></el-table-column>
            <el-table-column  label="采砂现场监管人员签字" min-width="80" prop="supervisorSignatureId" sortable="custom" column-key="SUPERVISOR_SIGNATURE_ID"></el-table-column>
            <el-table-column  label="水行政主管部门签字" min-width="80" prop="authoritySignatureId" sortable="custom" column-key="AUTHORITY_SIGNATURE_ID"></el-table-column>
            <el-table-column  label="水行政主管人ID" min-width="80" prop="authorityId" sortable="custom" column-key="AUTHORITY_ID"></el-table-column>
            <el-table-column  label="核销人ID" min-width="80" prop="verifyCancelUserId" sortable="custom" column-key="VERIFY_CANCEL_USER_ID"></el-table-column>
            <el-table-column  label="核销时间" min-width="80" prop="verifyCancelTime" sortable="custom" column-key="VERIFY_CANCEL_TIME"></el-table-column>
            <el-table-column  label="核销地点经度" min-width="80" prop="verifyCancelLongitude" sortable="custom" column-key="VERIFY_CANCEL_LONGITUDE"></el-table-column>
            <el-table-column  label="核销地点纬度" min-width="80" prop="verifyCancelLatitude" sortable="custom" column-key="VERIFY_CANCEL_LATITUDE"></el-table-column>
            <el-table-column  label="核销地点与卸砂点距离（米）" min-width="80" prop="distance" sortable="custom" column-key="DISTANCE"></el-table-column>
            <el-table-column label="操作" column-key="caozuo" min-width="80" fixed="right">
                <template slot-scope="scope">
                    <button @click="update(scope.row.id)" class="btn btn-xs btn-success" title="编辑" type="button"><i
                            class="fa fa-edit" aria-hidden="true"></i></button>
                    <button @click="delOne(scope.row.id)" class="btn btn-xs btn-success" title="删除" type="button"><i
                            class="fa fa-trash" aria-hidden="true"></i></button>
                </template>
            </el-table-column>
        </my-table>
    </div>

    <div v-show="!showList" class="panel panel-default">
        <div class="panel-heading">{{title}}</div>
        <form class="form-horizontal" id="frm">
														                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">创建人id</div>
                <div class="col-sm-9">
                    <my-input type="text" id="createUserId" v-model.trim="bill.createUserId" placeholder="创建人id"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">修改人id</div>
                <div class="col-sm-9">
                    <my-input type="text" id="updateUserId" v-model.trim="bill.updateUserId" placeholder="修改人id"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">创建时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="createTime" v-model.trim="bill.createTime" placeholder="创建时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">修改时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="updateTime" v-model.trim="bill.updateTime" placeholder="修改时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">单据编号</div>
                <div class="col-sm-9">
                    <my-input type="text" id="billNo" v-model.trim="bill.billNo" placeholder="单据编号"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">项目ID</div>
                <div class="col-sm-9">
                    <my-input type="text" id="projectId" v-model.trim="bill.projectId" placeholder="项目ID"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">车牌号</div>
                <div class="col-sm-9">
                    <my-input type="text" id="carNumber" v-model.trim="bill.carNumber" placeholder="车牌号"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">所属公司/个人</div>
                <div class="col-sm-9">
                    <my-input type="text" id="carCompany" v-model.trim="bill.carCompany" placeholder="所属公司/个人"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">品牌型号</div>
                <div class="col-sm-9">
                    <my-input type="text" id="carBrand" v-model.trim="bill.carBrand" placeholder="品牌型号"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">车主姓名</div>
                <div class="col-sm-9">
                    <my-input type="text" id="carOwnerName" v-model.trim="bill.carOwnerName" placeholder="车主姓名"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">自重（吨）</div>
                <div class="col-sm-9">
                    <my-input type="number" id="carKerbWeight" v-model.trim="bill.carKerbWeight" placeholder="自重（吨）"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">最大载重（吨）</div>
                <div class="col-sm-9">
                    <my-input type="number" id="carMaxPayload" v-model.trim="bill.carMaxPayload" placeholder="最大载重（吨）"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">运砂人</div>
                <div class="col-sm-9">
                    <my-input type="text" id="driverName" v-model.trim="bill.driverName" placeholder="运砂人"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">联系电话</div>
                <div class="col-sm-9">
                    <my-input type="text" id="driverMobile" v-model.trim="bill.driverMobile" placeholder="联系电话"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">实际载运量</div>
                <div class="col-sm-9">
                    <my-input type="number" id="vehicleLoad" v-model.trim="bill.vehicleLoad" placeholder="实际载运量"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">卸砂地点</div>
                <div class="col-sm-9">
                    <my-input type="text" id="destination" v-model.trim="bill.destination" placeholder="卸砂地点"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">卸砂地点经度</div>
                <div class="col-sm-9">
                    <my-input type="number" id="destLongitude" v-model.trim="bill.destLongitude" placeholder="卸砂地点经度"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">卸砂地点维度</div>
                <div class="col-sm-9">
                    <my-input type="number" id="destLatitude" v-model.trim="bill.destLatitude" placeholder="卸砂地点维度"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">预计到达时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="estimatedArrivalTime" v-model.trim="bill.estimatedArrivalTime" placeholder="预计到达时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">卸砂点负责人</div>
                <div class="col-sm-9">
                    <my-input type="text" id="destLeader" v-model.trim="bill.destLeader" placeholder="卸砂点负责人"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">负责人电话</div>
                <div class="col-sm-9">
                    <my-input type="text" id="destLeaderMobile" v-model.trim="bill.destLeaderMobile" placeholder="负责人电话"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">现场签字时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="signTime" v-model.trim="bill.signTime" placeholder="现场签字时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">主管部门签字时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="auditTime" v-model.trim="bill.auditTime" placeholder="主管部门签字时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">状态;字典值： waitingAduit-待审核、inTransit-在运、completed-已核销</div>
                <div class="col-sm-9">
                    <my-input type="text" id="status" v-model.trim="bill.status" placeholder="状态;字典值： waitingAduit-待审核、inTransit-在运、completed-已核销"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">承运人签字</div>
                <div class="col-sm-9">
                    <my-input type="text" id="driverSignatureId" v-model.trim="bill.driverSignatureId" placeholder="承运人签字"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">采砂人签字</div>
                <div class="col-sm-9">
                    <my-input type="text" id="projectLeaderSignatureId" v-model.trim="bill.projectLeaderSignatureId" placeholder="采砂人签字"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">采砂现场监管人员签字</div>
                <div class="col-sm-9">
                    <my-input type="text" id="supervisorSignatureId" v-model.trim="bill.supervisorSignatureId" placeholder="采砂现场监管人员签字"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">水行政主管部门签字</div>
                <div class="col-sm-9">
                    <my-input type="text" id="authoritySignatureId" v-model.trim="bill.authoritySignatureId" placeholder="水行政主管部门签字"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">水行政主管人ID</div>
                <div class="col-sm-9">
                    <my-input type="text" id="authorityId" v-model.trim="bill.authorityId" placeholder="水行政主管人ID"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">核销人ID</div>
                <div class="col-sm-9">
                    <my-input type="text" id="verifyCancelUserId" v-model.trim="bill.verifyCancelUserId" placeholder="核销人ID"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">核销时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="verifyCancelTime" v-model.trim="bill.verifyCancelTime" placeholder="核销时间"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">核销地点经度</div>
                <div class="col-sm-9">
                    <my-input type="number" id="verifyCancelLongitude" v-model.trim="bill.verifyCancelLongitude" placeholder="核销地点经度"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">核销地点纬度</div>
                <div class="col-sm-9">
                    <my-input type="number" id="verifyCancelLatitude" v-model.trim="bill.verifyCancelLatitude" placeholder="核销地点纬度"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">核销地点与卸砂点距离（米）</div>
                <div class="col-sm-9">
                    <my-input type="number" id="distance" v-model.trim="bill.distance" placeholder="核销地点与卸砂点距离（米）"></my-input>
                </div>
            </div>
							
            <div class="form-group center">
                <input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
                &nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
            </div>
        </form>
    </div>
</div>
<script src="${request.contextPath}/statics/js/modules/bill/bill.js?_${sloth.version()}"></script>
</body>
</html>
