<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybkj.smm.modules.rlc.mapper.RlcInspectionMapper">

    <resultMap type="RlcInspection" id="RlcInspectionResult">
        <result property="id"    column="id"    />
        <result property="projId"    column="proj_id"    />
        <result property="reguBody"    column="regu_body"    />
        <result property="isConsRec"    column="is_cons_rec"    />
        <result property="isFinished"    column="is_finished"    />
        <result property="finishedDate"    column="finished_date"    />
        <result property="isSpecialAccept"    column="is_special_accept"    />
        <result property="specialAcceptDate"    column="special_accept_date"    />
        <result property="remainPro"    column="remain_pro"    />
        <result property="checkDate"    column="check_date"    />
        <result property="isCompliance"    column="is_compliance"    />
        <result property="handSugg"    column="hand_sugg"    />
        <result property="processResult"    column="process_result"    />
        <result property="otherPro"    column="other_pro"    />
        <result property="urlPics"    column="url_pics"    />
        <result property="urlVids"    column="url_vids"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectRlcInspectionVo">
        select id, proj_id, regu_body, is_cons_rec, is_finished, finished_date, is_special_accept, special_accept_date, remain_pro, check_date, is_compliance, hand_sugg, process_result, other_pro, url_pics, url_vids, create_by, create_time, update_by, update_time, remark from rlc_inspection
    </sql>

    <select id="selectRlcInspectionList" parameterType="RlcInspection" resultMap="RlcInspectionResult">
        <include refid="selectRlcInspectionVo"/>
        <where>
            <if test="projId != null "> and proj_id = #{projId}</if>
            <if test="reguBody != null  and reguBody != ''"> and regu_body = #{reguBody}</if>
            <if test="isConsRec != null  and isConsRec != ''"> and is_cons_rec = #{isConsRec}</if>
            <if test="isFinished != null  and isFinished != ''"> and is_finished = #{isFinished}</if>
            <if test="finishedDate != null "> and finished_date = #{finishedDate}</if>
            <if test="isSpecialAccept != null  and isSpecialAccept != ''"> and is_special_accept = #{isSpecialAccept}</if>
            <if test="specialAcceptDate != null "> and special_accept_date = #{specialAcceptDate}</if>
            <if test="remainPro != null  and remainPro != ''"> and remain_pro = #{remainPro}</if>
            <if test="checkDate != null "> and check_date = #{checkDate}</if>
            <if test="isCompliance != null  and isCompliance != ''"> and is_compliance = #{isCompliance}</if>
            <if test="handSugg != null  and handSugg != ''"> and hand_sugg = #{handSugg}</if>
            <if test="processResult != null  and processResult != ''"> and process_result = #{processResult}</if>
            <if test="otherPro != null  and otherPro != ''"> and other_pro = #{otherPro}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectRlcInspectionById" parameterType="Long" resultMap="RlcInspectionResult">
        <include refid="selectRlcInspectionVo"/>
        where id = #{id}
    </select>

    <insert id="insertRlcInspection" parameterType="RlcInspection" useGeneratedKeys="true" keyProperty="id">
        insert into rlc_inspection
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projId != null">proj_id,</if>
            <if test="reguBody != null">regu_body,</if>
            <if test="isConsRec != null">is_cons_rec,</if>
            <if test="isFinished != null">is_finished,</if>
            <if test="finishedDate != null">finished_date,</if>
            <if test="isSpecialAccept != null">is_special_accept,</if>
            <if test="specialAcceptDate != null">special_accept_date,</if>
            <if test="remainPro != null">remain_pro,</if>
            <if test="checkDate != null">check_date,</if>
            <if test="isCompliance != null">is_compliance,</if>
            <if test="handSugg != null">hand_sugg,</if>
            <if test="processResult != null">process_result,</if>
            <if test="otherPro != null">other_pro,</if>
            <if test="urlPics != null">url_pics,</if>
            <if test="urlVids != null">url_vids,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projId != null">#{projId},</if>
            <if test="reguBody != null">#{reguBody},</if>
            <if test="isConsRec != null">#{isConsRec},</if>
            <if test="isFinished != null">#{isFinished},</if>
            <if test="finishedDate != null">#{finishedDate},</if>
            <if test="isSpecialAccept != null">#{isSpecialAccept},</if>
            <if test="specialAcceptDate != null">#{specialAcceptDate},</if>
            <if test="remainPro != null">#{remainPro},</if>
            <if test="checkDate != null">#{checkDate},</if>
            <if test="isCompliance != null">#{isCompliance},</if>
            <if test="handSugg != null">#{handSugg},</if>
            <if test="processResult != null">#{processResult},</if>
            <if test="otherPro != null">#{otherPro},</if>
            <if test="urlPics != null">#{urlPics},</if>
            <if test="urlVids != null">#{urlVids},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateRlcInspection" parameterType="RlcInspection">
        update rlc_inspection
        <trim prefix="SET" suffixOverrides=",">
            <if test="projId != null">proj_id = #{projId},</if>
            <if test="reguBody != null">regu_body = #{reguBody},</if>
            <if test="isConsRec != null">is_cons_rec = #{isConsRec},</if>
            <if test="isFinished != null">is_finished = #{isFinished},</if>
            <if test="finishedDate != null">finished_date = #{finishedDate},</if>
            <if test="isSpecialAccept != null">is_special_accept = #{isSpecialAccept},</if>
            <if test="specialAcceptDate != null">special_accept_date = #{specialAcceptDate},</if>
            <if test="remainPro != null">remain_pro = #{remainPro},</if>
            <if test="checkDate != null">check_date = #{checkDate},</if>
            <if test="isCompliance != null">is_compliance = #{isCompliance},</if>
            <if test="handSugg != null">hand_sugg = #{handSugg},</if>
            <if test="processResult != null">process_result = #{processResult},</if>
            <if test="otherPro != null">other_pro = #{otherPro},</if>
            <if test="urlPics != null">url_pics = #{urlPics},</if>
            <if test="urlVids != null">url_vids = #{urlVids},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRlcInspectionById" parameterType="Long">
        delete from rlc_inspection where id = #{id}
    </delete>

    <delete id="deleteRlcInspectionByIds" parameterType="String">
        delete from rlc_inspection where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <delete id="deleteRlcInspectionByProjId" parameterType="Long">
        delete from rlc_inspection where proj_id = #{id}
    </delete>
</mapper>
