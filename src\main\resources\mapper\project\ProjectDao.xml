<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.project.dao.ProjectDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.project.entity.Project" id="projectMap">
        <result property="deptId" column="DEPT_ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="deptCode" column="DEPT_CODE"/>
        <result property="name" column="NAME"/>
        <result property="areaName" column="AREA_NAME"/>
        <result property="areaCode" column="AREA_CODE"/>
        <result property="type" column="TYPE"/>
        <result property="parentId" column="PARENT_ID"/>
        <result property="totalYield" column="TOTAL_YIELD"/>
        <result property="licenseNo" column="LICENSE_NO"/>
        <result property="leaderName" column="LEADER_NAME"/>
        <result property="contact" column="CONTACT"/>
        <result property="deleted" column="DELETED"/>
    </resultMap>

    <select id="selectProjectPage" resultType="com.ybkj.smm.modules.project.entity.Project">
        select
        project.DEPT_ID,
        project.CREATE_USER_ID,
        project.UPDATE_USER_ID,
        project.CREATE_TIME,
        project.UPDATE_TIME,
        project.DEPT_CODE,
        project.NAME,
        project.SECTION_NAME,
        project.AREA_NAME,
        project.AREA_CODE,
        project.TYPE,
        project.PARENT_ID,
        project.TOTAL_YIELD,
        project.LICENSE_NO,
        project.LEADER_NAME,
        project.CONTACT,
        project.STATUS,
        dept.NAME AS deptName
        from
        smm_project_project project
        left join sys_dept dept on project.PARENT_ID = dept.DEPT_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="getByIdWithDeleted" resultType="com.ybkj.smm.modules.project.entity.Project">
        select * from smm_project_project where DEPT_ID=#{id}
    </select>
    <select id="selectProjectList" resultType="com.ybkj.smm.modules.project.entity.Project">
        select
        (@row_number:=@row_number+1) orderNumber,
        project.DEPT_ID,
        project.CREATE_USER_ID,
        project.UPDATE_USER_ID,
        project.CREATE_TIME,
        project.UPDATE_TIME,
        project.DEPT_CODE,
        project.NAME,
        project.SECTION_NAME,
        project.AREA_NAME,
        project.AREA_CODE,
        project.TYPE,
        project.PARENT_ID,
        project.TOTAL_YIELD,
        project.LICENSE_NO,
        project.LEADER_NAME,
        project.CONTACT,
        dept.NAME AS deptName
        from
        smm_project_project project
        left join sys_dept dept on project.PARENT_ID = dept.DEPT_ID
        join (select @row_number:=0) temp on 1=1
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="listProjectNamesByUseClient" resultType="com.ybkj.smm.modules.project.entity.Project">
        SELECT
            project.NAME,
            project.DEPT_ID,
            project.SECTION_NAME
        FROM
            smm_project_project project
                LEFT JOIN smm_project_project_config config ON project.DEPT_ID = config.DEPT_ID
        WHERE config.USE_CLIENT = TRUE
        ORDER BY project.DEPT_ID DESC
    </select>

    <select id="selectProjectPageByUser" resultType="com.ybkj.smm.modules.project.entity.Project">
        SELECT
            spp.DEPT_ID,
            spp.`NAME`,
            spp.SECTION_NAME,
            spp.AREA_NAME,
            spp.TYPE,
            SUM(spp.TOTAL_YIELD) * 10000 TOTAL_YIELD,
            IFNULL( SUM(billTable.MINED), 0 ) MINED,
            SUM((
            spp.TOTAL_YIELD * 10000 - IFNULL( billTable.MINED, 0 ))) REMAINING_SAND,
            IFNULL( billTimeTable.MINED_TIME, 0 ) MINED_TIME,
            deptTable.NAME deptName,
            spp.UPDATE_TIME,
            spp.COORDS,
            spp.GEO_JSON,
        IFNULL( SUM(billTable.MINED), 0 ) dailyVehicleLoad,
        SUM(spp.TOTAL_YIELD) * 10000 monthVehicleLoad,
            round( IFNULL( SUM(billTable.MINED), 0 )/(SUM(spp.TOTAL_YIELD) * 10000), 4 ) minedRate,
            case when count(spp.DEPT_ID)>1 then true else false end as multiSection
        FROM
            smm_project_project spp
                LEFT JOIN ( SELECT sbb.PROJECT_ID, SUM( VEHICLE_LOAD ) MINED
                            FROM smm_bill_bill sbb
                            GROUP BY sbb.PROJECT_ID ) billTable
                    ON spp.DEPT_ID = billTable.PROJECT_ID
                LEFT JOIN ( SELECT sbb.PROJECT_ID, SUM( VEHICLE_LOAD ) MINED_TIME
                            FROM smm_bill_bill sbb
                            GROUP BY sbb.PROJECT_ID ) billTimeTable
                    ON spp.DEPT_ID = billTimeTable.PROJECT_ID
                LEFT JOIN ( SELECT sd.DEPT_ID, sd.`NAME` FROM sys_dept sd WHERE sd.DEL_FLAG = 0 ) deptTable
                    ON spp.PARENT_ID = deptTable.DEPT_ID
        WHERE
            spp.DEPT_CODE LIKE ( SELECT CONCAT( DEPT_CODE, '%' )
                                 FROM `sys_user`
                                 WHERE USER_ID = #{userID}
                                   AND DELETED = 0 )
          AND spp.AREA_CODE LIKE CONCAT(#{areaCode}, '%')
          AND spp.DELETED = 0
          <if test='name != null &amp;&amp; name != "" '>
              AND spp.`NAME` = #{name}
          </if>
          <if test='sectionName != null &amp;&amp; sectionName != "" '>
              AND spp.`SECTION_NAME` = #{sectionName}
          </if>
          <if test='type != null &amp;&amp; type != "" '>
              AND spp.`TYPE` = #{type}
          </if>
          <if test="warningType!=null and warningType!=''">
              and spp.name in (
              select name from smm_project_project spp2
              left  join  (SELECT sbb.PROJECT_ID, SUM( VEHICLE_LOAD ) MINED
              FROM smm_bill_bill sbb
              GROUP BY sbb.PROJECT_ID ) billTable2 on spp2.DEPT_ID=billTable2.PROJECT_ID
              WHERE deleted = 0
              <if test="warningType=='good'">
                  and  IFNULL( billTable2.MINED, 0 )/(spp2.TOTAL_YIELD * 10000) &lt; 0.9 )
              </if>
              <if test="warningType=='warning'">
                  and  IFNULL( billTable2.MINED, 0 )/(spp2.TOTAL_YIELD * 10000) &lt; 1 and IFNULL( billTable2.MINED, 0 )/(spp2.TOTAL_YIELD * 10000) &gt;= 0.9 )
              </if>
              <if test="warningType=='lack'">
                  and  IFNULL( billTable2.MINED, 0 ) / ( spp2.TOTAL_YIELD * 10000 ) >= 1 )
              </if>
          </if>
          <if test='sqlFilter != null'>
            AND ${sqlFilter}
          </if>
        GROUP BY
        spp.`NAME`
        ORDER BY
        spp.UPDATE_TIME DESC
    </select>

    <resultMap id="projectMonthlyReportsMap" type="com.ybkj.smm.modules.project.dto.ProjectMonthlyReportsDto">
        <result property="deptId" column="DEPT_ID" />
        <result property="projectName" column="PROJECT_NAME"/>
        <result property="leaderName" column="LEADER_NAME" />
        <result property="licenseNo" column="LICENSE_NO" />
        <result property="totalYield" column="TOTAL_YIELD"/>
        <result property="coords" column="COORDS"/>
        <result property="geoJson" column="GEO_JSON"/>
        <result property="governCoords" column="GOVERN_COORDS"/>
        <result property="leftshoreCoords" column="LEFTSHORE_COORDS"/>
        <result property="rightshoreCoords" column="RIGHTSHORE_COORDS"/>
        <result property="governanceContent" column="GOVERNANCE_CONTENT"/>
        <result property="monthSandMined" column="MONTH_SAND_MINED"/>
        <result property="totalSandMined" column="TOTAL_SAND_MINED"/>
    </resultMap>
    <select id="getProjectListByUser" resultMap="projectMonthlyReportsMap">
        SELECT
            (@row_number:=@row_number+1) orderNumber,
            spp.DEPT_ID,
            CONCAT(spp.`NAME`,spp.SECTION_NAME) PROJECT_NAME,
            spp.LEADER_NAME,
            spp.LICENSE_NO,
            spp.AREA_NAME,
            spp.TOTAL_YIELD TOTAL_YIELD,
            IFNULL( billTimeTable.MINED_TIME, 0 ) MONTH_SAND_MINED,
            IFNULL( billTable.MINED, 0 ) TOTAL_SAND_MINED,
            SPP.UPDATE_TIME,
            spp.COORDS,
            spp.GEO_JSON
        FROM
            smm_project_project spp
                LEFT JOIN ( SELECT sbb.PROJECT_ID, SUM( VEHICLE_LOAD )/1000 MINED FROM smm_bill_bill sbb GROUP BY sbb.PROJECT_ID ) billTable ON spp.DEPT_ID = billTable.PROJECT_ID
                LEFT JOIN (
                SELECT
                    sbb.PROJECT_ID,
                    SUM( VEHICLE_LOAD )/1000 MINED_TIME
                FROM
                    smm_bill_bill sbb
                WHERE
                    YEAR ( sbb.UPDATE_TIME )= YEAR (CURDATE())
                  AND MONTH ( sbb.UPDATE_TIME )= MONTH (CURDATE())
                GROUP BY
                    sbb.PROJECT_ID
            ) billTimeTable ON spp.DEPT_ID = billTimeTable.PROJECT_ID
            join (select @row_number:=0) temp on 1=1
        WHERE
            spp.DEPT_CODE LIKE ( SELECT CONCAT( DEPT_CODE, '%' ) FROM `sys_user` WHERE USER_ID = #{userID} AND DEL_FLAG = 0 )
        <if test='areaCode != null &amp;&amp; areaCode != "" '>
            AND spp.AREA_CODE LIKE CONCAT(#{areaCode}, '%')
        </if>
        <if test='(startTime != null &amp;&amp; startTime != "") &amp;&amp; (endTime != null &amp;&amp; endTime != "") '>
            AND spp.UPDATE_TIME BETWEEN #{startTime} AND #{endTime}
        </if>
          AND spp.DELETED = 0
          AND spp.TYPE = 'riverSand'
        <if test='sqlFilter != null'>
            AND ${sqlFilter}
        </if>
        ORDER BY
            spp.UPDATE_TIME DESC
    </select>

    <!--  根据当前登录用户检索项目列表（项目名+标段名）  -->
    <select id="getProjectList" resultType="com.ybkj.smm.modules.project.entity.Project">
        SELECT
            spp.DEPT_ID,
            spp.DEPT_CODE,
            spp.`NAME`,
            spp.SECTION_NAME,
            SPP.UPDATE_TIME
        FROM
            smm_project_project spp
        WHERE
            spp.DEPT_CODE LIKE ( SELECT CONCAT( DEPT_CODE, '%' ) FROM `sys_user` WHERE USER_ID = #{userID} AND DEL_FLAG = 0 )
          AND spp.DELETED = 0
          <if test='sqlFilter != null'>
            AND ${sqlFilter}
          </if>
        ORDER BY
            spp.UPDATE_TIME DESC
    </select>
    <select id="selectSectionList" resultType="com.ybkj.smm.modules.project.entity.Project">
        SELECT
        spp.DEPT_ID,
        spp.`NAME`,
        spp.SECTION_NAME,
        spp.AREA_NAME,
        spp.TYPE,
        spp.TOTAL_YIELD * 10000 TOTAL_YIELD,
        IFNULL( billTable.MINED, 0 ) MINED,
        (spp.TOTAL_YIELD * 10000 - IFNULL( billTable.MINED, 0 )) REMAINING_SAND,
        IFNULL( billTimeTable.MINED_TIME, 0 ) MINED_TIME,
        deptTable.NAME deptName,
        spp.UPDATE_TIME,
        spp.COORDS,
        spp.GEO_JSON,
        round(IFNULL( billTable.MINED, 0 )/(spp.TOTAL_YIELD * 10000),4) minedRate
        FROM
        smm_project_project spp
        LEFT JOIN ( SELECT sbb.PROJECT_ID, SUM( VEHICLE_LOAD ) MINED
        FROM smm_bill_bill sbb
        GROUP BY sbb.PROJECT_ID ) billTable
        ON spp.DEPT_ID = billTable.PROJECT_ID
        LEFT JOIN ( SELECT sbb.PROJECT_ID, SUM( VEHICLE_LOAD ) MINED_TIME
        FROM smm_bill_bill sbb
        GROUP BY sbb.PROJECT_ID ) billTimeTable
        ON spp.DEPT_ID = billTimeTable.PROJECT_ID
        LEFT JOIN ( SELECT sd.DEPT_ID, sd.`NAME` FROM sys_dept sd WHERE sd.DEL_FLAG = 0 ) deptTable
        ON spp.PARENT_ID = deptTable.DEPT_ID
        WHERE
        spp.DEPT_CODE LIKE ( SELECT CONCAT( DEPT_CODE, '%' )
        FROM `sys_user`
        WHERE USER_ID = #{userID}
        AND DELETED = 0 )
        AND spp.AREA_CODE LIKE CONCAT(#{areaCode}, '%')
        AND spp.DELETED = 0
        <if test='name != null &amp;&amp; name != "" '>
            AND spp.`NAME` = #{name}
        </if>
        <if test='sectionName != null &amp;&amp; sectionName != "" '>
            AND spp.`SECTION_NAME` = #{sectionName}
        </if>
        <if test='type != null &amp;&amp; type != "" '>
            AND spp.`TYPE` = #{type}
        </if>
        <if test='sqlFilter != null'>
            AND ${sqlFilter}
        </if>
        order by spp.UPDATE_TIME desc
    </select>
    <select id="selectProjectAreaPage" resultType="com.ybkj.smm.modules.project.dto.ProjectReportDto">
        SELECT
        LEFT ( parent.DEPT_CODE, #{deptCodeLength} ) AS deptCode,
        SUM( project.TOTAL_YIELD * 10000 ) totalYield,
        SUM(
        IFNULL( billTable.MINED, 0 )) mined
        FROM
        smm_project_project project
        left join sys_dept parent on project.PARENT_ID = parent.DEPT_ID
        LEFT JOIN ( SELECT sbb.PROJECT_ID, SUM( VEHICLE_LOAD ) MINED FROM smm_bill_bill sbb WHERE sbb.CREATE_TIME BETWEEN #{startTime} AND #{endTime} GROUP BY sbb.PROJECT_ID ) billTable ON project.DEPT_ID = billTable.PROJECT_ID
        <where>
            ${ew.sqlSegment}
        </where>
        GROUP BY
        deptCode
    </select>
    <select id="selectProjectDetailsPage" resultType="com.ybkj.smm.modules.project.dto.ProjectReportDto">
        SELECT
            project.`NAME`,
            project.TYPE as type,
            project.DEPT_CODE AS deptCode,
            SUM(project.TOTAL_YIELD * 10000) totalYield,
            SUM(IFNULL( billTable.MINED, 0 )) mined
        FROM
            smm_project_project project
        left join sys_dept parent on project.PARENT_ID = parent.DEPT_ID
        LEFT JOIN ( SELECT sbb.PROJECT_ID, SUM( VEHICLE_LOAD ) MINED FROM smm_bill_bill sbb  WHERE sbb.CREATE_TIME BETWEEN #{startTime} AND #{endTime} GROUP BY sbb.PROJECT_ID ) billTable ON project.DEPT_ID = billTable.PROJECT_ID
        <where>
            ${ew.sqlSegment}
        </where>
        GROUP BY
        project.`NAME`
    </select>
    <select id="selectProjectTotal" resultType="com.ybkj.smm.modules.project.dto.ProjectReportDto">
        SELECT LEFT
            ( project.DEPT_CODE, 9 ) AS deptCode,
            SUM( project.TOTAL_YIELD * 10000 ) totalYieldTotal,
            SUM(
            IFNULL( billTable.MINED, 0 )) minedTotal
        FROM
            smm_project_project project
            LEFT JOIN ( SELECT sbb.PROJECT_ID, SUM( VEHICLE_LOAD ) MINED FROM smm_bill_bill sbb WHERE sbb.CREATE_TIME BETWEEN #{startTime} AND #{endTime} GROUP BY sbb.PROJECT_ID ) billTable ON project.DEPT_ID = billTable.PROJECT_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="selectCityTotal" resultType="com.ybkj.smm.modules.project.dto.ProjectReportDto">
        SELECT LEFT
        ( project.DEPT_CODE, 9 ) AS deptCode,
        SUM( project.TOTAL_YIELD * 10000 ) totalYieldTotal,
        SUM(
        IFNULL( billTable.MINED, 0 )) minedTotal
        FROM
        smm_project_project project
        left join sys_dept parent on project.PARENT_ID = parent.DEPT_ID
        LEFT JOIN ( SELECT sbb.PROJECT_ID, SUM( VEHICLE_LOAD ) MINED FROM smm_bill_bill sbb WHERE sbb.CREATE_TIME BETWEEN #{startTime} AND #{endTime} GROUP BY sbb.PROJECT_ID ) billTable ON project.DEPT_ID = billTable.PROJECT_ID
        <where>
            ${ew.sqlSegment}
        </where>

    </select>

    <select id="getMinedVolumeByProjectId" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(VEHICLE_LOAD), 0) 
        FROM smm_bill_bill 
        WHERE PROJECT_ID = #{projectId} 
        AND STATUS = 'completed'
    </select>

</mapper>
