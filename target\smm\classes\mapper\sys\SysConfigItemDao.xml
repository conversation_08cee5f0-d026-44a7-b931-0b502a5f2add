<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.sys.dao.SysConfigItemDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sloth.modules.sys.entity.SysConfigItemEntity" id="sysConfigItemMap">
        <result property="id" column="ID"/>
        <result property="configCode" column="CONFIG_CODE"/>
        <result property="configName" column="CONFIG_NAME"/>
        <result property="category" column="CATEGORY"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="nullable" column="NULLABLE"/>
        <result property="defaultValue" column="DEFAULT_VALUE"/>
        <result property="remark" column="REMARK"/>
    </resultMap>


</mapper>