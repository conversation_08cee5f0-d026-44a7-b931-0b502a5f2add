<#import "/macro/macro.ftl" as my />
<!DOCTYPE html>
<html>
<@my.head jqgrid=true component=true vueValidate=true elementUI=true ztree=true  ueditor=true>
    <title>过期的运砂申请</title>
</@my.head>
<body>
<div id="app" v-cloak>
    <div v-show="showList">
        <div class="grid-btn form-inline">
            <div class="form-group">
                <a class="btn btn-default" @click="query"><i class="fa fa-search"></i>&nbsp;查询</a>
                <a class="btn btn-default" @click="queryAll"><i class="fa fa-refresh"></i>&nbsp;全部</a>
            </div>
			<#if shiro.hasPermission("bill:applyExpire:save")>
            <div class="form-group">
            <a class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
            </div>
			</#if>
			<#if shiro.hasPermission("bill:applyExpire:delete")>
            <div class="form-group">
            <a class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;批量删除</a>
            </div>
			</#if>
        </div>
        <a class="pull-right" @click="drawer=true">更多查询<i class="fa fa-plus"></i></a>
        <div class="clearfix"></div>
        <el-drawer
                :visible.sync="drawer"
                :with-header="false"
                direction="rtl">
            <div class="form-horizontal" style="width:100%;padding: 10px">
                <div class="form-group">
                </div>
                <div class="form-group">
                    <a class="btn btn-default" @click="query('drawer')"><i class="fa fa-search"></i>&nbsp;查询</a>
                    <a class="btn btn-default" @click="clearQ"><i class="fa fa-refresh"></i>&nbsp;清空</a>
                </div>
            </div>
        </el-drawer>
        <my-table url="${request.contextPath}/bill/applyExpire/list" ref="applyExpireTable" row-key="id">
            <el-table-column  label="id 主键" min-width="80" prop="id" sortable="custom" column-key="ID"></el-table-column>
            <el-table-column  label="创建人id" min-width="80" prop="createUserId" sortable="custom" column-key="CREATE_USER_ID"></el-table-column>
            <el-table-column  label="修改人id" min-width="80" prop="updateUserId" sortable="custom" column-key="UPDATE_USER_ID"></el-table-column>
            <el-table-column  label="创建时间" min-width="80" prop="createTime" sortable="custom" column-key="CREATE_TIME"></el-table-column>
            <el-table-column  label="修改时间" min-width="80" prop="updateTime" sortable="custom" column-key="UPDATE_TIME"></el-table-column>
            <el-table-column  label="项目ID" min-width="80" prop="projectId" sortable="custom" column-key="PROJECT_ID"></el-table-column>
            <el-table-column  label="车牌号" min-width="80" prop="carNumber" sortable="custom" column-key="CAR_NUMBER"></el-table-column>
            <el-table-column  label="所属公司/个人" min-width="80" prop="carCompany" sortable="custom" column-key="CAR_COMPANY"></el-table-column>
            <el-table-column  label="品牌型号" min-width="80" prop="carBrand" sortable="custom" column-key="CAR_BRAND"></el-table-column>
            <el-table-column  label="车主姓名" min-width="80" prop="carOwnerName" sortable="custom" column-key="CAR_OWNER_NAME"></el-table-column>
            <el-table-column  label="自重（吨）" min-width="80" prop="carKerbWeight" sortable="custom" column-key="CAR_KERB_WEIGHT"></el-table-column>
            <el-table-column  label="最大载重（吨）" min-width="80" prop="carMaxPayload" sortable="custom" column-key="CAR_MAX_PAYLOAD"></el-table-column>
            <el-table-column  label="卸砂地点" min-width="80" prop="destination" sortable="custom" column-key="DESTINATION"></el-table-column>
            <el-table-column  label="卸砂地点经度（gps）" min-width="80" prop="destLongitudeGps" sortable="custom" column-key="DEST_LONGITUDE_GPS"></el-table-column>
            <el-table-column  label="卸砂地点纬度（gps）" min-width="80" prop="destLatitudeGps" sortable="custom" column-key="DEST_LATITUDE_GPS"></el-table-column>
            <el-table-column  label="卸砂地点经度（腾讯）" min-width="80" prop="destLongitude" sortable="custom" column-key="DEST_LONGITUDE"></el-table-column>
            <el-table-column  label="卸砂地点维度（腾讯）" min-width="80" prop="destLatitude" sortable="custom" column-key="DEST_LATITUDE"></el-table-column>
            <el-table-column  label="预计到达时间" min-width="80" prop="estimatedArrivalTime" sortable="custom" column-key="ESTIMATED_ARRIVAL_TIME"></el-table-column>
            <el-table-column  label="卸砂点负责人？" min-width="80" prop="destLeader" sortable="custom" column-key="DEST_LEADER"></el-table-column>
            <el-table-column  label="负责人电话？" min-width="80" prop="destLeaderMobile" sortable="custom" column-key="DEST_LEADER_MOBILE"></el-table-column>
            <el-table-column  label="状态;字典值。notBilled-未开单，billed-已开单" min-width="80" prop="status" sortable="custom" column-key="STATUS"></el-table-column>
            <el-table-column  label="数据版本" min-width="80" prop="version" sortable="custom" column-key="VERSION"></el-table-column>
            <el-table-column  label="删除事假" min-width="80" prop="deletedTime" sortable="custom" column-key="DELETED_TIME"></el-table-column>
            <el-table-column label="操作" column-key="caozuo" min-width="80" fixed="right">
                <template slot-scope="scope">
                    <button @click="update(scope.row.id)" class="btn btn-xs btn-success" title="编辑" type="button"><i
                            class="fa fa-edit" aria-hidden="true"></i></button>
                    <button @click="delOne(scope.row.id)" class="btn btn-xs btn-success" title="删除" type="button"><i
                            class="fa fa-trash" aria-hidden="true"></i></button>
                </template>
            </el-table-column>
        </my-table>
    </div>

    <div v-show="!showList" class="panel panel-default">
        <div class="panel-heading">{{title}}</div>
        <form class="form-horizontal" id="frm">
														                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">创建人id</div>
                <div class="col-sm-9">
                    <my-input type="text" id="createUserId" v-model.trim="applyExpire.createUserId" placeholder="创建人id"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">修改人id</div>
                <div class="col-sm-9">
                    <my-input type="text" id="updateUserId" v-model.trim="applyExpire.updateUserId" placeholder="修改人id"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">创建时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="createTime" v-model.trim="applyExpire.createTime" placeholder="创建时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">修改时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="updateTime" v-model.trim="applyExpire.updateTime" placeholder="修改时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">项目ID</div>
                <div class="col-sm-9">
                    <my-input type="text" id="projectId" v-model.trim="applyExpire.projectId" placeholder="项目ID"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">车牌号</div>
                <div class="col-sm-9">
                    <my-input type="text" id="carNumber" v-model.trim="applyExpire.carNumber" placeholder="车牌号"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">所属公司/个人</div>
                <div class="col-sm-9">
                    <my-input type="text" id="carCompany" v-model.trim="applyExpire.carCompany" placeholder="所属公司/个人"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">品牌型号</div>
                <div class="col-sm-9">
                    <my-input type="text" id="carBrand" v-model.trim="applyExpire.carBrand" placeholder="品牌型号"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">车主姓名</div>
                <div class="col-sm-9">
                    <my-input type="text" id="carOwnerName" v-model.trim="applyExpire.carOwnerName" placeholder="车主姓名"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">自重（吨）</div>
                <div class="col-sm-9">
                    <my-input type="number" id="carKerbWeight" v-model.trim="applyExpire.carKerbWeight" placeholder="自重（吨）"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">最大载重（吨）</div>
                <div class="col-sm-9">
                    <my-input type="number" id="carMaxPayload" v-model.trim="applyExpire.carMaxPayload" placeholder="最大载重（吨）"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">卸砂地点</div>
                <div class="col-sm-9">
                    <my-input type="text" id="destination" v-model.trim="applyExpire.destination" placeholder="卸砂地点"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">卸砂地点经度（gps）</div>
                <div class="col-sm-9">
                    <my-input type="number" id="destLongitudeGps" v-model.trim="applyExpire.destLongitudeGps" placeholder="卸砂地点经度（gps）"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">卸砂地点纬度（gps）</div>
                <div class="col-sm-9">
                    <my-input type="number" id="destLatitudeGps" v-model.trim="applyExpire.destLatitudeGps" placeholder="卸砂地点纬度（gps）"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">卸砂地点经度（腾讯）</div>
                <div class="col-sm-9">
                    <my-input type="number" id="destLongitude" v-model.trim="applyExpire.destLongitude" placeholder="卸砂地点经度（腾讯）"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">卸砂地点维度（腾讯）</div>
                <div class="col-sm-9">
                    <my-input type="number" id="destLatitude" v-model.trim="applyExpire.destLatitude" placeholder="卸砂地点维度（腾讯）"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">预计到达时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="estimatedArrivalTime" v-model.trim="applyExpire.estimatedArrivalTime" placeholder="预计到达时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">卸砂点负责人？</div>
                <div class="col-sm-9">
                    <my-input type="text" id="destLeader" v-model.trim="applyExpire.destLeader" placeholder="卸砂点负责人？"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">负责人电话？</div>
                <div class="col-sm-9">
                    <my-input type="text" id="destLeaderMobile" v-model.trim="applyExpire.destLeaderMobile" placeholder="负责人电话？"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">状态;字典值。notBilled-未开单，billed-已开单</div>
                <div class="col-sm-9">
                    <my-input type="text" id="status" v-model.trim="applyExpire.status" placeholder="状态;字典值。notBilled-未开单，billed-已开单"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">数据版本</div>
                <div class="col-sm-9">
                    <my-input type="number" id="version" v-model.trim="applyExpire.version" placeholder="数据版本"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">删除事假</div>
                <div class="col-sm-9">
                    <my-input type="text" id="deletedTime" v-model.trim="applyExpire.deletedTime" placeholder="删除事假"></my-input>
                </div>
            </div>
							
            <div class="form-group center">
                <input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
                &nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
            </div>
        </form>
    </div>
</div>
<script src="${request.contextPath}/statics/js/modules/bill/applyExpire.js?_${sloth.version()}"></script>
</body>
</html>
