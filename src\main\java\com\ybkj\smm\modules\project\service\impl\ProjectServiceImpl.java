package com.ybkj.smm.modules.project.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.alibaba.fastjson.util.TypeUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.f4b6a3.ulid.UlidCreator;
import com.sloth.common.annotation.DataFilter;
import com.sloth.common.constant.EhcacheConstants;
import com.sloth.common.exception.ServiceException;
import com.sloth.common.utils.*;
import com.sloth.core.service.impl.BaseServiceImpl;
import com.sloth.modules.com.entity.UploadFile;
import com.sloth.modules.com.service.UploadFileService;
import com.sloth.modules.sys.entity.SysDeptEntity;
import com.sloth.modules.sys.service.SysDeptService;
import com.sloth.modules.sys.shiro.ShiroUtils;
import com.ybkj.smm.common.constant.SmmConstants;
import com.ybkj.smm.common.constant.SmmErrorMsgConstants;
import com.ybkj.smm.modules.bill.entity.Apply;
import com.ybkj.smm.modules.bill.service.ApplyService;
import com.ybkj.smm.modules.project.dao.ProjectDao;
import com.ybkj.smm.modules.project.dto.ProjectStatusDto;
import com.ybkj.smm.modules.project.entity.BillAdditionRecord;
import com.ybkj.smm.modules.project.entity.Driver;
import com.ybkj.smm.modules.project.entity.Project;
import com.ybkj.smm.modules.project.entity.WeighingStation;
import com.ybkj.smm.modules.project.query.ProjectQuery;
import com.ybkj.smm.modules.project.service.BillAdditionRecordService;
import com.ybkj.smm.modules.project.service.DriverService;
import com.ybkj.smm.modules.project.service.ProjectService;
import com.ybkj.smm.modules.project.service.WeighingStationService;
import com.ybkj.smm.modules.video.entity.LiveCamera;
import com.ybkj.smm.modules.video.service.LiveCameraService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;


@Service("projectService")
public class ProjectServiceImpl extends BaseServiceImpl<ProjectDao, Project> implements ProjectService {

    @Autowired
    private SysDeptService sysDeptService;

    @Autowired
    private UploadFileService uploadFileService;

    @Autowired
    private WeighingStationService weighingStationService;

    @Autowired
    @Lazy
    private BillAdditionRecordService billAdditionRecordService;

    @Autowired
    private WxMaService wxMaService;

    @Autowired
    private LiveCameraService liveCameraService;

    @Autowired
    private DriverService driverService;

    @Autowired
    @Lazy
    private ApplyService applyService;

    @Override
    @DataFilter(deptAlias = "project",tableAlias = "project")
    public PageUtils queryPage(ProjectQuery queryParams) {
        QueryWrapper<Project> ew = getProjectQueryWrapper(queryParams);
        Page<Project> page = baseMapper.selectProjectPage(new PageWrapper<Project>(queryParams, "project.DEPT_ID").getPage(), ew);
        return new PageUtils(page);
    }

    @Override
    @DataFilter(deptAlias = "project",tableAlias = "project")
    public List<Project> queryList(ProjectQuery queryParams){
        //默认以创建时间排序
        QueryWrapper<Project> ew = getProjectQueryWrapper(queryParams);
        ew.orderBy(true, "asc".equalsIgnoreCase(queryParams.getOrder()), queryParams.getSidx(),"project.DEPT_ID");
        if (queryParams.getLimit() != null) {
            ew.last(" limit " + queryParams.getLimit());
        }
        List<Project> list = baseMapper.selectProjectList(ew);
        return list;

    }

    /**
     *<li>功能描述：公共查询条件</li>
     * @author: wangxiao
     * @date:  2024/7/4 14:47
     */
    private static QueryWrapper<Project> getProjectQueryWrapper(ProjectQuery queryParams) {
        //默认以创建时间排序
        if (StringUtils.isBlank(queryParams.getSidx())) {
            queryParams.setSidx("create_time");
            queryParams.setOrder("desc");
        }

        //拼接查询条件
        QueryWrapper<Project> ew = new QueryWrapper<Project>();
        ew.eq(StringUtils.isNotBlank(queryParams.getType()), "project.TYPE", queryParams.getType());
        ew.like(StringUtils.isNotBlank(queryParams.getName()), "project.NAME", queryParams.getName());
        ew.like(StringUtils.isNotBlank(queryParams.getSectionName()), "project.SECTION_NAME", queryParams.getSectionName());
        ew.likeRight(StringUtils.isNotBlank(queryParams.getAreaCode()), "project.AREA_CODE", queryParams.getAreaCode());
        ew.eq("project.DELETED", 0);
        ew.apply(StringUtils.isNotBlank(queryParams.getSqlFilter()), queryParams.getSqlFilter());
        return ew;
    }

    @Override
    public Project getInfoById(String deptId) {
        //根据deptId获取项目详情
        Project project = this.getById(deptId);
        SysDeptEntity deptEntity = sysDeptService.getById(project.getParentId());
        if (deptEntity != null) {
            project.setDeptName(deptEntity.getName());
        }

        //根据deptId获取项目附件
        List<UploadFile> fileList = uploadFileService.findUploadFileListByOwnerId(deptId, SmmConstants.UploadFieldName.PROJECT_FILE);
        if (CollectionUtils.isNotEmpty(fileList)) {
            project.setUploadFileList(fileList);
        }
        //根据deptId获取印章图片
        List<UploadFile> sealImageList= uploadFileService.findUploadFileListByOwnerId(deptId, SmmConstants.UploadFieldName.SEAL_IMAGE);
        if (CollectionUtils.isNotEmpty(sealImageList)) {
            project.setFileList(sealImageList);
        }
        //获取项目中配置的上级部门印章
        List<UploadFile> parentImgList= uploadFileService.findUploadFileListByOwnerId(deptId, SmmConstants.UploadFieldName.PARENT_DEPT_IMG);
        if (CollectionUtils.isNotEmpty(parentImgList)) {
            project.setParentImgList(parentImgList);
        }

        //获取项目中配置的上级部门印章
        List<UploadFile> paiMaiFileImgList= uploadFileService.findUploadFileListByOwnerId(deptId, SmmConstants.UploadFieldName.PROJECT_PAI_MAI_FILE);
        if (CollectionUtils.isNotEmpty(paiMaiFileImgList)) {
            project.setPaiMaiFileList(paiMaiFileImgList);
        }


        return project;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveProject(Project entity) {
        //查询监管部门的部门编码
        SysDeptEntity parentDept = sysDeptService.getById(entity.getParentId());

        //生成deptId
        entity.setDeptId(UlidCreator.getUlid().toString());

        //新增部门
        SysDeptEntity dept = new SysDeptEntity();
        dept.setDeptId(entity.getDeptId());
        dept.setParentId(entity.getParentId());
        if(StringUtils.isNotBlank(entity.getSectionName())){
            dept.setName(entity.getName()+'('+entity.getSectionName()+')');
        }else {
            dept.setName(entity.getName());
        }
        dept.setParentCode(parentDept.getDeptCode());
        dept.setAreaCode(entity.getAreaCode());
        dept.setType(SmmConstants.DeptType.SAND);
        dept.setOrderNum(SmmConstants.ORDER_NUM);
        sysDeptService.saveDept(dept);

        //新增项目
        entity.setDeptCode(dept.getDeptCode());
        boolean save = this.save(entity);

        //给项目附件设置ownerId
        if (save) {
            if (CollectionUtils.isNotEmpty(entity.getUploadFileList())) {
                List<String> fileIdList = entity.getUploadFileList().stream().map(e -> e.getId()).collect(Collectors.toList());
                uploadFileService.update(new UpdateWrapper<UploadFile>()
                        .set("OWNER_ID", entity.getDeptId())
                        .in("ID", fileIdList));
            }
            //给印章图片设置ownerId
            if (CollectionUtils.isNotEmpty(entity.getFileList())) {
                List<String> sealImageList = entity.getFileList().stream().map(e -> e.getId()).collect(Collectors.toList());
                uploadFileService.update(new UpdateWrapper<UploadFile>()
                        .set("OWNER_ID", entity.getDeptId())
                        .in("ID", sealImageList));
            }
            //给监管部门印章设置ownerId
            if (CollectionUtils.isNotEmpty(entity.getParentImgList())) {
                List<String> imgIds = entity.getParentImgList().stream().map(e -> e.getId()).collect(Collectors.toList());
                uploadFileService.update(new UpdateWrapper<UploadFile>()
                        .set("OWNER_ID", entity.getDeptId())
                        .in("ID", imgIds));
            }
            //给中拍附件设置ownerId
            if (CollectionUtils.isNotEmpty(entity.getPaiMaiFileList())) {
                List<String> imgIds = entity.getPaiMaiFileList().stream().map(e -> e.getId()).collect(Collectors.toList());
                uploadFileService.update(new UpdateWrapper<UploadFile>()
                        .set("OWNER_ID", entity.getDeptId())
                        .in("ID", imgIds));
            }
        }




    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProjectById(Project entity) {
        //修改部门信息
        SysDeptEntity dept = new SysDeptEntity();
        dept.setDeptId(entity.getDeptId());
        if(StringUtils.isNotBlank(entity.getSectionName())){
            dept.setName(entity.getName()+'('+entity.getSectionName()+')');
        }else {
            dept.setName(entity.getName());
        }
        dept.setAreaCode(entity.getAreaCode());
        sysDeptService.updateById(dept);

        //修改项目信息
        boolean update = this.updateById(entity);

        //修改项目附件
        if (update) {
            List<UploadFile> fileList = entity.getUploadFileList();
            if (CollectionUtils.isNotEmpty(fileList)) {
                List<String> fileIdList = fileList.stream().map(UploadFile::getId).collect(Collectors.toList());
                uploadFileService.remove(new QueryWrapper<UploadFile>()
                        .eq("OWNER_ID", entity.getDeptId())
                        .eq("FIELD_NAME", SmmConstants.UploadFieldName.PROJECT_FILE)
                        .notIn(CollectionUtils.isNotEmpty(fileIdList), "ID", fileIdList)
                );
                uploadFileService.update(new UpdateWrapper<UploadFile>()
                        .set("OWNER_ID", entity.getDeptId())
                        .eq("FIELD_NAME", SmmConstants.UploadFieldName.PROJECT_FILE)
                        .in("ID", fileIdList));
            } else {
                uploadFileService.remove(new QueryWrapper<UploadFile>()
                        .eq("OWNER_ID", entity.getDeptId())
                        .eq("FIELD_NAME", SmmConstants.UploadFieldName.PROJECT_FILE));
            }
            //修改印章图片
            List<UploadFile> sealImageList = entity.getFileList();
            if (CollectionUtils.isNotEmpty((sealImageList))){
                List<String> sealImageIdList = sealImageList.stream().map(UploadFile::getId).collect(Collectors.toList());
                uploadFileService.remove(new QueryWrapper<UploadFile>()
                        .eq("OWNER_ID", entity.getDeptId())
                        .eq("FIELD_NAME", SmmConstants.UploadFieldName.SEAL_IMAGE)
                        .notIn(CollectionUtils.isNotEmpty(sealImageIdList), "ID", sealImageIdList)
                );
                uploadFileService.update(new UpdateWrapper<UploadFile>()
                        .set("OWNER_ID", entity.getDeptId())
                        .eq("FIELD_NAME", SmmConstants.UploadFieldName.SEAL_IMAGE)
                        .in("ID", sealImageIdList));

            } else {
                uploadFileService.remove(new QueryWrapper<UploadFile>()
                        .eq("OWNER_ID", entity.getDeptId())
                        .eq("FIELD_NAME", SmmConstants.UploadFieldName.SEAL_IMAGE));
            }
            //修改监管部门印章图片
            List<UploadFile> parentImgList = entity.getParentImgList();
            if (CollectionUtils.isNotEmpty((parentImgList))){
                List<String> parentImgIds = parentImgList.stream().map(UploadFile::getId).collect(Collectors.toList());
                uploadFileService.remove(new QueryWrapper<UploadFile>()
                        .eq("OWNER_ID", entity.getDeptId())
                        .eq("FIELD_NAME", SmmConstants.UploadFieldName.PARENT_DEPT_IMG)
                        .notIn(CollectionUtils.isNotEmpty(parentImgIds), "ID", parentImgIds)
                );
                uploadFileService.update(new UpdateWrapper<UploadFile>()
                        .set("OWNER_ID", entity.getDeptId())
                        .eq("FIELD_NAME", SmmConstants.UploadFieldName.PARENT_DEPT_IMG)
                        .in("ID", parentImgIds));

            } else {
                uploadFileService.remove(new QueryWrapper<UploadFile>()
                        .eq("OWNER_ID", entity.getDeptId())
                        .eq("FIELD_NAME", SmmConstants.UploadFieldName.PARENT_DEPT_IMG));
            }
            //修改中拍附件图片
            List<UploadFile> paiMaiFileList = entity.getPaiMaiFileList();
            if (CollectionUtils.isNotEmpty((paiMaiFileList))){
                List<String> paiMaiFileImgIds = paiMaiFileList.stream().map(UploadFile::getId).collect(Collectors.toList());
                uploadFileService.remove(new QueryWrapper<UploadFile>()
                        .eq("OWNER_ID", entity.getDeptId())
                        .eq("FIELD_NAME", SmmConstants.UploadFieldName.PROJECT_PAI_MAI_FILE)
                        .notIn(CollectionUtils.isNotEmpty(paiMaiFileImgIds), "ID", paiMaiFileImgIds)
                );
                uploadFileService.update(new UpdateWrapper<UploadFile>()
                        .set("OWNER_ID", entity.getDeptId())
                        .eq("FIELD_NAME", SmmConstants.UploadFieldName.PROJECT_PAI_MAI_FILE)
                        .in("ID", paiMaiFileImgIds));

            } else {
                uploadFileService.remove(new QueryWrapper<UploadFile>()
                        .eq("OWNER_ID", entity.getDeptId())
                        .eq("FIELD_NAME", SmmConstants.UploadFieldName.PROJECT_PAI_MAI_FILE));
            }
        }
    }

    @Override
    @DataFilter()
    public Long projectCount(ProjectQuery projectQuery) {

        return this.count(new QueryWrapper<Project>()
                .apply(StringUtils.isNotBlank(projectQuery.getSqlFilter()), projectQuery.getSqlFilter()));
    }

    @Override
    public Project getByIdWithDeleted(String id) {
        return baseMapper.getByIdWithDeleted(id);
    }

    @Override
    public Integer getTodayAllowCount(String projectId) {
        //授权开单数量
        Integer dailyBillCount = this.getObj(new LambdaQueryWrapper<Project>()
                .select(Project::getDailyBillCount)
                .eq(Project::getDeptId, projectId), o -> TypeUtils.castToInt(o));

        Integer addCount = billAdditionRecordService.getObj(new QueryWrapper<BillAdditionRecord>()
                .select("sum(ADD_COUNT)")
                .eq("PROJECT_ID", projectId), o -> TypeUtils.castToInt(o));

        return addCount == null ? dailyBillCount : dailyBillCount + addCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<String> deptIds, String currentUserDeptId) {
        //判断删除的项目是否存在摄像头
        boolean exists = liveCameraService.exists(new QueryWrapper<LiveCamera>().in("DEPT_ID", deptIds));
        if (exists) {
            throw new ServiceException(2028, SmmErrorMsgConstants.PROJECT_DELETED_VIDEO_ERROR_2028);
        }
        //判断是否存在磅站
        boolean existsStation = weighingStationService.exists(new QueryWrapper<WeighingStation>().in("PROJECT_ID", deptIds).eq("deleted", 0));
        if (existsStation) {
            throw new ServiceException(2033, SmmErrorMsgConstants.PROJECT_DELETED_STATION_ERROR_2033);
        }
        //判断是否存在运砂人
        boolean existsDriver = driverService.exists(new QueryWrapper<Driver>().in("PROJECT_ID", deptIds));
        if (existsDriver) {
            throw new ServiceException(2034, SmmErrorMsgConstants.PROJECT_DELETED_DRIVER_ERROR_2034);
        }
        //判断是否存在运砂申请
        boolean existsApply = applyService.exists(new QueryWrapper<Apply>().in("PROJECT_ID", deptIds));
        if (existsApply) {
            throw new ServiceException(2035, SmmErrorMsgConstants.PROJECT_DELETED_APPLY_ERROR_2035);
        }

        //批量删除沙场部门信息
        for (String deptId : deptIds) {
            sysDeptService.deleteDeptById(deptId, currentUserDeptId);
        }

        //批量删除项目附件
        uploadFileService.remove(new QueryWrapper<UploadFile>().in("OWNER_ID", deptIds));

        //批量删除项目信息
        this.removeByIds(deptIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(String deptId, String currentUserDeptId) {

        //判断删除的项目是否存在摄像头
        boolean exists = liveCameraService.exists(new QueryWrapper<LiveCamera>().eq("DEPT_ID", deptId));
        if (exists) {
            throw new ServiceException(2028, SmmErrorMsgConstants.PROJECT_DELETED_VIDEO_ERROR_2028);
        }

        //判断是否存在磅站
        boolean existsStation = weighingStationService.exists(new QueryWrapper<WeighingStation>().eq("PROJECT_ID", deptId).eq("deleted", 0));
        if (existsStation) {
            throw new ServiceException(2033, SmmErrorMsgConstants.PROJECT_DELETED_STATION_ERROR_2033);
        }
        //判断是否存在运砂人
        boolean existsDriver = driverService.exists(new QueryWrapper<Driver>().eq("PROJECT_ID", deptId));
        if (existsDriver) {
            throw new ServiceException(2034, SmmErrorMsgConstants.PROJECT_DELETED_DRIVER_ERROR_2034);
        }
        //判断是否存在运砂申请
        boolean existsApply = applyService.exists(new QueryWrapper<Apply>().eq("PROJECT_ID", deptId));
        if (existsApply) {
            throw new ServiceException(2035, SmmErrorMsgConstants.PROJECT_DELETED_APPLY_ERROR_2035);
        }

        //单个删除沙场部门信息
        sysDeptService.deleteDeptById(deptId, currentUserDeptId);

        //批量删除所属磅站信息
        weighingStationService.remove(new QueryWrapper<WeighingStation>()
                .eq("PROJECT_ID", deptId));

        //单个删除项目附件
        uploadFileService.remove(new QueryWrapper<UploadFile>().eq("OWNER_ID", deptId));

        //单个删除项目信息
        this.removeById(deptId);
    }

    @Override
    @DataFilter(deptAlias = "project",tableAlias = "project")
    public void export(ProjectQuery queryParams) throws IOException {
        queryParams.setPage(1);
        queryParams.setLimit(65535);
        List<Project> list = this.queryList(queryParams);
        for (Project project : list) {
            project.setType(DataDictUtils.getSubName(SmmConstants.DictValue.PROJECT_TYPE, project.getType()));
        }
        String templateFilePath = "/templates/export/project.xlsx";
        EasyExcelUtils.downloadExcel(templateFilePath, list, "项目管理.xlsx");
    }

    @Override
    public boolean save(Project entity) {
        entity.setCreateTime(DateUtils.getCurrentDate());
        if (StringUtils.isBlank(entity.getCreateUserId())) {
            entity.setCreateUserId(ShiroUtils.getUserId());
        }
        entity.setUpdateTime(DateUtils.getCurrentDate());
        if (StringUtils.isBlank(entity.getUpdateUserId())) {
            entity.setUpdateUserId(ShiroUtils.getUserId());
        }
        return super.save(entity);
    }

    @Override
    public boolean updateById(Project entity) {
        entity.setUpdateTime(DateUtils.getCurrentDate());
        if (StringUtils.isBlank(entity.getUpdateUserId())) {
            entity.setUpdateUserId(ShiroUtils.getUserId());
        }
        return super.updateById(entity);
    }

    @Override
    public String getWxCode(String createUserId, String deptId) throws Exception {
        File wxCode =  wxMaService.getQrcodeService().createWxaCode("/pages/apply/index?id="+deptId,580);
        UploadFile  uploadFile = uploadFileService.upLoadFile(FileUtils.fileToBase64(wxCode), SmmConstants.UploadFieldName.WX_CODE_IMAGE_IMG, deptId+".jpg", "image/jpg", deptId, createUserId);
        return uploadFile.getUploadFilePath();
    }

    @Override
    public List<Project> listProjectNamesByUseClient() {
        return baseMapper.listProjectNamesByUseClient();
    }

    @Override
    @DataFilter
    public List<Project> listProjectByDeptId(ProjectQuery queryParams) {
        QueryWrapper<Project> ew = new QueryWrapper<>();
        ew.select("NAME","DEPT_ID","SECTION_NAME");
        ew.orderByDesc("DEPT_ID");
        ew.apply(StringUtils.isNotBlank(queryParams.getSqlFilter()),queryParams.getSqlFilter());
        return this.list(ew);
    }

    @Override
    @DataFilter
    public List<String> queryProjectIdByAuth(ProjectQuery queryParams) {
        QueryWrapper<Project> ew = new QueryWrapper<>();
        ew.select("DEPT_ID");
        ew.apply(StringUtils.isNotBlank(queryParams.getSqlFilter()),queryParams.getSqlFilter());
        return this.listObjs(ew, o -> (String) o);
    }

    @Override
    public BufferedImage createWxCodeImg(String createUserId, String deptId) throws Exception {
        //加载背景图
        String filePath = PathUtils.getRealPath("/statics/images/wxCode.png");
        BufferedImage background = ImageIO.read(new File(filePath));

        //生成二维码
        UploadFile uploadFile = uploadFileService.findLastestFileByOwnerId(deptId,SmmConstants.UploadFieldName.WX_CODE_IMAGE_IMG);
        if (uploadFile == null) {
            File wxCode =  wxMaService.getQrcodeService().createWxaCode("/pages/apply/index?id="+deptId,580);
            uploadFile = uploadFileService.upLoadFile(FileUtils.fileToBase64(wxCode), SmmConstants.UploadFieldName.WX_CODE_IMAGE_IMG, deptId+".jpg", "image/jpg", deptId, createUserId);
        }
        InputStream inputStream = uploadFileService.getObject(uploadFile);
        BufferedImage wxCodeImage = ImageIO.read(inputStream);
        //合并成一个图片
        Graphics2D graphics = background.createGraphics();
        graphics.drawImage(wxCodeImage, 325, 300, null);
        graphics.dispose();
        //加载项目名称。
        Project project = this.getById(deptId);
        String showName = project.getName();
        if (StringUtils.isNotEmpty(showName) ) {
            if (StringUtils.isNotBlank(project.getSectionName())){
                showName = showName + "("+project.getSectionName() + ")";
            }
            showName = "项目名称：" + showName;

            Graphics2D graphics1 = background.createGraphics();
            //字体颜色
            graphics1.setColor(Color.black);
            //宋体
            File fontFile = new File(PathUtils.getRealPath("/font/simsun.ttc"));
            Font font = Font.createFont(Font.TRUETYPE_FONT, fontFile);
            //字体大小
            font = font.deriveFont(Font.BOLD, 30f);

            graphics1.setFont(font);
            //获取文字的宽度，让文字居中显示
            FontMetrics fontMetrics = graphics1.getFontMetrics();
            Rectangle2D stringBounds = fontMetrics.getStringBounds(showName, graphics1);
            int stringWith = (int) Math.round(stringBounds.getWidth());

            graphics1.drawString(showName,(background.getWidth()-stringWith)/2,280);
            graphics1.dispose();
        }
        return background;
    }

    @Override
    public void updateProjectStatus(ProjectStatusDto projectStatusDto) {
        LambdaUpdateWrapper<Project> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(Project::getDeptId,projectStatusDto.getDeptId())
                .set(Project::getStatus,projectStatusDto.getStatus());
        this.update(lambdaUpdateWrapper);
        CacheUtils.deleteByPrefix(EhcacheConstants.CONSTANTS_DATA_CACHE, SmmConstants.CacheKey.DRIVER_PROJECT_ID);
    }

    @Override
    public boolean checkSandVolumeAvailable(String projectId) {
        // 查询项目的总采砂量限制
        Project project = this.getByIdWithDeleted(projectId);
        if (project == null || project.getTotalYield() == null) {
            return true; // 如果项目不存在或未设置总量限制，默认允许申请
        }
        
        // 总采砂量限制（万吨转换为吨）
        BigDecimal totalYieldInTons = project.getTotalYield().multiply(new BigDecimal("10000"));
        
        // 查询该项目已完成的采砂总量
        BigDecimal minedVolume = baseMapper.getMinedVolumeByProjectId(projectId);
        if (minedVolume == null) {
            minedVolume = BigDecimal.ZERO;
        }
        
        // 比较已采砂量与总采砂量限制
        return minedVolume.compareTo(totalYieldInTons) < 0;
    }
}
