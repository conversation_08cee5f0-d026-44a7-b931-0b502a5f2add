															
var vm = new Vue({
	el:'#app',
	mixins: [basicTableMixin],
	data:{
		showList: true,
		title: null,
		drawer:false,
		q:{},
		projectConfig: {}
	},
	methods: {
		add: function(){
			vm.title = "新增";
			vm.projectConfig = {};
			vm.$nextTick(function () {
				vm.showList = false;
			})
		},
		update: function (deptId) {
			//var deptId = getSelectedRow();
			if(deptId == null){
				return ;
			}
			vm.showList = false;
            vm.title = "修改";
            vm.getInfo(deptId)
		},
		saveOrUpdate: function (event) {
		    if(!validateForm("frm")){
		        return;
			}
			var url = vm.projectConfig.deptId == null ? "project/projectConfig/save" : "project/projectConfig/update";
			var that=this;
			this.$http.postWithShade(baseURL + url, this.projectConfig)
					.then(function (r) {
						alert("操作成功", function () {
							that.reload();
						});
					});
		},
		del: function (event) {
			var deptIds = this.$refs.projectConfigTable.getSelectRowKeys();
			if(deptIds == null){
				alert('请选择一条记录');
				return ;
			}
			var that=this;
			confirm('确定要删除选中的 ' + deptIds.length + ' 条记录？', function () {
				that.$http.post(baseURL + "project/projectConfig/delete", deptIds)
						.then(function (r) {
							alert("操作成功", function () {
								that.reload();
							});
						});
			});
		},
		delOne: function (deptId) {
			var that=this;
			confirm('确定要删除选中的记录？', function(){
				that.$http.post(baseURL + "project/projectConfig/delete/" +deptId)
						.then(function (r) {
							alert("操作成功", function () {
								that.reload();
							});
						});
			});
        },
		getInfo: function(deptId){
			var that=this
			this.$http.post(baseURL + "project/projectConfig/info/" +deptId)
					.then(function (r) {
						that.projectConfig = r.projectConfig;
					});
		},
		reload: function (event) {
			vm.showList = true;
			this.$refs.projectConfigTable.search(this.q, event === 'query');
            $(".error-label").remove();
		}
	},
});
