<#import "/macro/macro.ftl" as my />
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title><@my.config configCode="webSite.name"/></title>
	<!-- Tell the browser to be responsive to screen width -->
	<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
	<link rel="shortcut icon" href="${request.contextPath}/statics/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="${request.contextPath}/statics/css/bootstrap.min.css">
	<link rel="stylesheet" href="${request.contextPath}/statics/css/font-awesome.min.css">
	<link rel="stylesheet" href="${request.contextPath}/statics/css/AdminLTE.min.css">
	<!-- AdminLTE Skins. Choose a skin from the css/skins
         folder instead of downloading all of them to reduce the load. -->
	<link rel="stylesheet" href="${request.contextPath}/statics/css/all-skins.min.css">
	<link rel="stylesheet" href="${request.contextPath}/statics/plugins/element-ui/lib/theme-chalk/index.css">
	<link rel="stylesheet" href="${request.contextPath}/statics/plugins/nProgress/css/nprogress.css?_${sloth.version()}">
	<link rel="stylesheet" href="${request.contextPath}/statics/css/main.css">
	<link rel="stylesheet" href="${request.contextPath}/statics/css/index2.css?_${sloth.version()}">
	<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
	<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
	<!--[if lt IE 9]>
  <script src="${request.contextPath}/statics/libs/html5shiv.min.js"></script>
  <script src="${request.contextPath}/statics/libs/respond.min.js"></script>
  <![endif]-->
</head>
<!-- ADD THE CLASS layout-boxed TO GET A BOXED LAYOUT -->
<body class="hold-transition skin-blue fixed sidebar-mini">
<!-- Site wrapper -->
<div class="wrapper" id="app" v-cloak>
	<header class="main-header">
		<a href="javascript:void(0);" class="logo">
			<!-- mini logo for sidebar mini 50x50 pixels -->
			<span class="logo-mini"><b><@my.config  configCode="webSite.shortName"/></b></span>
			<!-- logo for regular state and mobile devices -->
			<span class="logo-lg"><b><@my.config  configCode="webSite.name"/></b></span>
		</a>
		<!-- Header Navbar: style can be found in header.less -->
		<nav class="navbar navbar-static-top" role="navigation">
			<!-- Sidebar toggle button-->
			<a href="#" class="sidebar-toggle" data-toggle="offcanvas" role="button">
				<span class="sr-only">Toggle navigation</span>
			</a>
			<a href="javascript:;" @click="refresh" title="刷新" style="float:left;color:#fff;padding:15px 10px;" role="button">
					<i class="fa fa-refresh"></i>
			</a>
			<div class="navbar-custom-menu">
				<ul class="nav navbar-nav">
					<#--
                  <li><a href="javascript:;" @click="donate"><i class="fa fa-jpy"></i> &nbsp;捐赠作者</a></li>
                  <li><a href="http://www.yuanbenkeji.com" target="_blank"><i class="fa fa-home"></i> &nbsp;猿本科技</a></li>
                    -->
					<#--<li><a href="#modules/message/sitenotice.html"><i class="fa fa-envelope-o"></i> &nbsp;消息&nbsp;&nbsp;<span v-if="showCount"  class="label label-danger">{{msgCount}}</span></a></li>-->
					<li><a href="javascript:;">欢迎 {{user.showName}}</a></li>
					<li><a href="javascript:;" @click="updatePassword"><i class="fa fa-lock"></i> &nbsp;修改密码</a></li>
					<li><a href="logout"><i class="fa fa-sign-out"></i> &nbsp;退出系统</a></li>
				</ul>
			</div>
		</nav>
	</header>

	<!-- =============================================== -->

	<!-- Left side column. contains the sidebar -->
	<aside class="main-sidebar">
		<!-- sidebar: style can be found in sidebar.less -->
		<section class="sidebar">
			<!-- /.search form -->
			<!-- sidebar menu: : style can be found in sidebar.less -->
			<ul class="sidebar-menu">
				<#--<li class="header">导航菜单</li>-->

				<!-- vue生成的菜单 -->
				<menu-item :item="item" v-for="item in menuList"></menu-item>
			</ul>
		</section>
		<!-- /.sidebar -->
	</aside>
	<!-- =============================================== -->
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<div class="tab-tools">
			<el-dropdown @command="handleCommand">
			<span class="el-dropdown-link">
				<i class="el-icon-arrow-down el-icon--right"></i>
			</span>
				<el-dropdown-menu slot="dropdown">
					<el-dropdown-item command="1">关闭当前标签页</el-dropdown-item>
					<el-dropdown-item command="2">关闭其他标签页</el-dropdown-item>
					<el-dropdown-item command="3">关闭全部标签页</el-dropdown-item>
				</el-dropdown-menu>
			</el-dropdown>
		</div>
		<el-tabs id="el_tabs" v-model="currentTab"  @tab-remove="removeTab" @tab-click="tabClick">
			<el-tab-pane v-for="(item,index) in tabs" :closable="index!=0"
						 :key="item.name" :name="item.name">
				<span slot="label"><i :class="item.icon"></i> {{item.title}}</span>
				<section class="content" style="background:#fff;" :id="'content_iframe_'+item.name">
					<iframe  :id="'iframe_'+item.name" scrolling="yes" frameborder="0" style="width:100%;min-height:200px;overflow:visible;background:#fff;" :src="item.url"></iframe>
				</section>
			</el-tab-pane>
		</el-tabs>
	</div>
	<!-- /.content-wrapper -->

	<footer class="main-footer">
		<div class="pull-right hidden-xs">
			Version <@my.config  configCode="webSite.version"/>
		</div>
		<@my.config  configCode="webSite.copyright"/>
	</footer>

	<!-- Add the sidebar's background. This div must be placed
         immediately after the control sidebar -->
	<div class="control-sidebar-bg"></div>

	<!-- 修改密码 -->
	<div id="passwordLayer" style="display: none;">
		<form class="form-horizontal" id="frm">
			<div class="form-group">
				<div class="form-group">
					<div class="col-sm-3 control-label">用户名</div>
					<div class="col-sm-9">
						<span class="label label-success" style="vertical-align: bottom;">{{user.username}}</span>
					</div>
				</div>
				<div class="form-group">
					<div class="col-sm-3 control-label">原密码</div>
					<div class="col-sm-9">
						<input type="password" id="password" class="form-control" v-model="password" placeholder="原密码" rules="[{notNull:true,message:'原密码不能为空'},{regExp:/^[^\u4e00-\u9fa5]{0,}$/,message:'原密码不可包含汉字'}]"/>
					</div>
				</div>
				<div class="form-group">
					<div class="col-sm-3 control-label">新密码</div>
					<div class="col-sm-9">
						<input v-if="pp=='false'" type="password" id="newPassword" class="form-control" v-model="newPassword" placeholder="新密码" rules="[{notNull:true,message:'新密码不能为空'},{regExp:/^[^\u4e00-\u9fa5]{0,}$/,message:'新密码不可包含汉字'}]"/>
						<input v-else type="password" id="newPassword" class="form-control" v-model="newPassword" placeholder="新密码" rules="[{notNull:true,message:'新密码不能为空'},{regExp:/(?=.*[0-9])(?=.*[a-zA-Z]).{8,30}/,message:'必须包含字母和数字，长度8-30'},{regExp:/^[^\u4e00-\u9fa5]{0,}$/,message:'新密码不可包含汉字'}]"/>
					</div>
				</div>
				<div class="form-group">
					<div class="col-sm-3 control-label">确认新密码</div>
					<div class="col-sm-9">
						<input type="password" id="conPassword" class="form-control" v-model="conPassword" placeholder="确认新密码" rules="[{notNull:true,message:'确认密码不能为空'},{equalWith:'newPassword',message:'两次新密码必须相同'},]"/>
					</div>
				</div>
			</div>
		</form>
	</div>

</div>
<!-- ./wrapper -->


<script type="text/javascript">
	var pp='<@my.config  configCode="system.passwordPolicy"/>';
	var basePath="${basePath!''}";
	var webSocketUrl="${webSocketUrl!''}"
	var webSocketOpen="${webSocketOpen!''}"
	var webSocketHeartBeat="${webSocketHeartBeat!''}"
</script>
<script src="${request.contextPath}/statics/libs/jquery.min.js"></script>
<script src="${request.contextPath}/statics/libs/vue.min.js"></script>
<script src="${request.contextPath}/statics/libs/webSocket.js?_${sloth.version()}"></script>
<script src="${request.contextPath}/statics/plugins/element-ui/lib/index.js"></script>
<script src="${request.contextPath}/statics/libs/bootstrap.min.js"></script>
<script src="${request.contextPath}/statics/libs/jquery.slimscroll.min.js"></script>
<script src="${request.contextPath}/statics/libs/app.js"></script>
<script src="${request.contextPath}/statics/plugins/layer/layer.js"></script>
<script src="${request.contextPath}/statics/plugins/nProgress/nprogress.min.js"></script>
<script src="${request.contextPath}/statics/js/common.js"></script>
<script src="${request.contextPath}/statics/js/common.validate.js"></script>
<script src="${request.contextPath}/statics/js/index2.js?_${sloth.version()}"></script>
</body>
</html>
