/**
 * 把用于 Ajax 请求的 Axios 库，作为插件，引入 Vue 里；增加请求前后显示遮罩的方法。
 * 方法参数参照原 Axios 的 get、post 方法参数。
 * {@link http://www.axios-js.com/zh-cn/docs/index.html#请求配置}
 */

;(function () {
    try {
        if (axios === undefined || Vue === undefined) {
            return;
        }
    } catch (ex) {
        return;
    }
    var instance = axios.create();
    instance.interceptors.request.use(function (config) {
        //请求发起之前
        config.headers.appId = AppUtils.getUrlParam("appId");

        return config;
    }, function (error) {
        //请求错误

        return Promise.reject(error);
    });

    instance.interceptors.request.use(function (config) {
        config.headers['appId'] = AppUtils.getUrlParam("appId");
        return config;
    });

    instance.interceptors.response.use(function (response) {
        //任何 2xx 的 http 状态码触发这个函数。
        //处理响应数据。

        if (response.data) {
            if (typeof response.data === 'object' && 'code' in response.data && response.data.code) {
                if (!('noAlert' in response.config) || !response.config['noAlert']) {
                    if (response.data.msg) {
                        alert(response.data.msg);
                    } else {
                        alert('请求出错！');
                    }
                }

                return Promise.reject('请求出错！');
            } else {
                return response.data;
            }
        }
    }, function (error) {
        if (error.response) {
            //任何 2xx 以外的 http 状态码触发这个函数。
            //处理响应错误。

            //所有 http 头，都成为小写的。
            if (response.status === 302 && response.headers['redirect'] === "REDIRECT") {
                //取出后端重定向的地址,使用 window.location.href 实现重定向
                confirm("您的登录信息已超时，请重新登录", function () {
                    top.location.href = response.headers["contextpath"];
                });

                return Promise.reject('重新登陆！');
            } else {
                alert('请求出错！');
            }
        } else if (error.request) {
            //发出了请求，但是没有收到响应
            alert('连接出错！');
        } else {
            //请求未发出，请求的构造的问题，触发了错误

        }

        return Promise.reject('请求出错！');
    });

    var axiosPlugin = {
        install: function (vue, options) {
            vue.prototype.$http = {
                /**
                 * @arg url[, config]
                 * config 参数里，使用 params 配置参数传入 URL 参数，必须是普通对象或者 URLSearchParams 对象。<br>
                 * (config 参考 {@link http://www.axios-js.com/zh-cn/docs/index.html#请求配置} )
                 */
                get: instance.get,
                /**
                 * 同 get 方法。增加遮罩显示。
                 * @return {*}
                 */
                getWithShade: function () {
                    //显示遮罩
                    var ldgInst = vue.prototype.$loading();

                    return instance.get.apply(null, arguments)
                        .finally(function () {
                            //关闭遮罩
                            ldgInst.close();
                        });
                },
                /**
                 * @arg url[, data[, config]]
                 * data 参数，应用于 'PUT', 'POST', 'DELETE', 和 'PATCH' 方法；<br>
                 * 可以是以下类型：string, plain object, ArrayBuffer, ArrayBufferView, URLSearchParams, FormData, File, Blob。<br>
                 * 对象类型的值，自动 json 序列化。<br>
                 * config 格式同 get 方法。
                 */
                post: instance.post,
                /**
                 * 同 post 方法。增加遮罩显示。
                 */
                postWithShade: function () {
                    //显示遮罩
                    var ldgInst = vue.prototype.$loading();

                    return instance.post.apply(null, arguments)
                        .finally(function () {
                            //关闭遮罩
                            ldgInst.close();
                        });
                },
            };
        }
    };

    Vue.use(axiosPlugin);
})();
