# 通用配置
spring:
  application:
    name: sand-mining
  profiles:
    active: prod
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
      enabled: true
      location: ./temp
  cache:
    type: ehcache
    ehcache:
      config: classpath:ehcache.xml
  redis:
    database: 0
    host: localhost
    port: 6379
    password:      # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
  freemarker:
    suffix: .ftl
    request-context-attribute: request
  activiti:
    check-process-definitions: false  #启动时不进行模型检测
  main:
    allow-bean-definition-overriding: true #允许bean同名覆盖
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

#mybatis
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.sloth.modules.*.entity, com.ybkj.smm.modules.*.domain
  global-config:
    #刷新mapper 调试神器
    refresh-mapper: true
    #逻辑删除配置
    db-config:
      #主键类型
      id-type: ASSIGN_UUID
      #逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
  type-handlers-package: com.sloth.common.typehandler
knife4j:
  enable: true # 开启knife4j增强功能，是否开启文档由sloth.swagger.enable决定

--- # Actuator 监控端点的配置项
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
    logfile:
      external-file: ./logs/admin.log

--- # 监控配置
spring.boot.admin.client:
  # 增加客户端开关
  enabled: true
  # 设置 Spring Boot Admin Server 地址
  url: http://localhost:9090
  instance:
    service-host-type: IP
    metadata:
      key: IxClocOp2EOvh989gYPWntS5YUMGver5Keg/VtYiA2HySeEGH11YFkQIDAQAB
  username: admin
  password: 123456

#自定义配置
sloth:
  uploadFile:
    path: /home/<USER>/9017_smm_demo/smm/uploadFile/  #上传文件的根路径，使用绝对路径
  globalSessionTimeout: 3600   #单机环境，session过期时间为60分钟
  redis:
    open: true  #是否开启redis缓存  true开启   false关闭
    serverCount: 2
    sessionSyncInterval: 5 # 开启redis时，同步session的时间间隔，单位：分钟
  schedule:
    enable: true
  cmsEnable: false #是否用cms首页作为项目首页,boolean值
  wx:
    mp:
      count: 0 #微信公账号的个数
    cp:
      agentCount: 0 #企业微信的应用个数
    ma:
      count: 1 #小程序账号个数
  onlineUpdate:
    backupPath: D:\uploadFile\onlineUpdate\
  #druid-spring监控包名，多个时逗号分隔
  druid:
    pointcut: com.sloth.*,com.ybkj.*
  #xssFilter跳过uri,允许**模糊匹配
  #skipUris:
  #shiro不拦截的路径配置
  shiro:
    rememberMeCookie:
      name: smm.rememberme #cookie name
      time: 604800  #cookie登录记住时长，单位 ：秒
    sessionIdCookie:
      name: smm
    anonPaths: /druid/**,/sockjs-node/**
  async-pool: #异步任务线程配置
    core-pool-size: 4
    max-pool-size: 8
    queue-capacity: 1000
  logFilter:
    skipUris: /updateVersion/** #api模块记录请求日志忽略哪些路径
  apiPage: #前后分离项目集成部署，开启前段登陆
    open: false
  monitor:
    privateKey: i4XI3dFcE66CSAqV46LWGtUbrRpVVkgA+lvW4BzzQ2VaMc8vFP0=
    max-diff-time: 300000 # 允许最大时间误差
  sms:
    initPuller: false

minio:
  endpoint: http://127.0.0.1:9000
  accessKey: admin
  secretKey: 123456

# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.1
  # 版权年份
  copyrightYear: 2022
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  #  正式发布时
  profile: D:/app/rlc/profile/

  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
