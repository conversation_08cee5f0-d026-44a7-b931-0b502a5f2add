// iframe自适应
$(window).on('resize', function () {
    var $tabContent = $('.el-tabs__content');
    var $tabPane = $('.el-tab-pane');
    var $content = $('.content');
    $tabContent.height($(this).height() - 130);
    $tabPane.height($(this).height() - 130);
    $content.height($(this).height() - 160);
    $content.find('iframe').each(function () {
        $(this).height($content.height());
    });
}).resize();

NProgress.configure({
    // parent:'.el-tabs__content'
    parent:'#el_tabs'
})

//生成菜单
var menuItem = Vue.extend({
    name: 'menu-item',
    props: {item: {}},
    template: [
        '<li>',
        '	<a v-if="item.type === 0" href="javascript:;">',
        '		<i v-if="item.icon != null" :class="item.icon"></i>',
        '		<span>{{item.name}}</span>',
        '       <span class="pull-right-container">',
        '		    <i class="fa fa-angle-left pull-right"></i>',
        '            <small v-if="item.todoCount!=0" class="label pull-right bg-yellow">NEW</small>',
        '       </span>',
        '	</a>',
        '	<ul v-if="item.type === 0" class="treeview-menu">',
        '		<menu-item :item="item" v-for="item in item.list"></menu-item>',
        '	</ul>',

        '	<a v-if="item.type === 1 && item.parentId === \'00000000000000000000000000000000\'" :href="\'#\'+item.url"  :url="item.url">',
        '		<i v-if="item.icon != null" :class="item.icon"></i>',
        '       <i v-else class="fa fa-circle-o"></i> ',
        '		<span>{{item.name}}</span>',
        '       <span v-if="item.todoCount!=0" class="pull-right-container">',
        '            <small class="label pull-right bg-yellow">{{item.todoCount}}</small>',
        '       </span>',
        '	</a>',

        '	<a v-if="item.type === 1 && item.parentId != \'00000000000000000000000000000000\'"  :href="\'#\'+item.url" :url="item.url">',
        '       <i v-if="item.icon != null" :class="item.icon"></i>',
        '       <i v-else class="fa fa-circle-o"></i> ',
        '		<span>{{item.name}}</span>',
        '       <span v-if="item.todoCount!=0" class="pull-right-container">',
        '            <small class="label pull-right bg-yellow" >{{item.todoCount}}</small>',
        '       </span>',
        '   </a>',
        '</li>'
    ].join(''),
    mounted: function () {
        //刷新页面时定位到hash对应菜单
        var url = window.location.hash.replace("#", '');
        vm.openMenu(url);
    }
});

//注册菜单组件
Vue.component('menuItem', menuItem);
isquery = true;
var vm = new Vue({
    el: '#app',
    data: {
        user: {},
        menuList: {},
        password: '',
        newPassword: '',
        conPassword: '',
        navTitle: "首页"
        , pp: '',
        tabs: [],
        currentTab: '0',
        tabIndex: 0,
        msgCount: null,
        webSocket: null,
        retryExitTimes: 0,//点击退出按钮是重试发送ws的次数
        maxRetryTimes: 4,//点击退出按钮是重试发送ws的最大次数
        loginType: '',//登录方式
    },
    methods: {
        refresh: function () {
            var id = "iframe_" + this.currentTab;
            var tabs = this.tabs.filter(function (tab) {
                return tab.name === vm.currentTab
            });
            if (tabs) {
                var url = tabs[0].url;
                this.createIframe(id, url)
            }
        },
        //创建iframe
        createIframe: function (id, url) {
            var iframe = "<iframe id=\"" + id + "\" scrolling=\"yes\" frameborder=\"0\" style=\"width:100%;min-height:200px;overflow:visible;background:#fff;\" src=\"" + url + "\"></iframe>"
            $("#" + id).remove();
            $("#content_" + id).html(iframe);
            $("#content_" + id).height($(window).height() - 160);
            $("#" + id).height($("#content_" + id).height())
        },
        handleCommand: function (index) {
            if (index == 1) {
                this.removeTab(this.currentTab);
            } else if (index == 2) {
                this.removeOtherTab();
            } else if (index == 3) {
                this.removeAllTab();
            }
        },
        addTab: function (menu) {
            var newTabName = ++this.tabIndex + '';
            this.tabs.push({
                title: menu.name,
                name: newTabName,
                url: menu.url,
                icon: menu.icon
            });
            this.currentTab = newTabName;
            this.$nextTick(function () {
                $(window).resize();
            })
        },
        //点击tab切换
        tabClick: function (tab) {
            NProgress.start();
            this.$nextTick(function () {
                //解决my-table/el-table表格错位问题
                var clickIframe = document.getElementById("iframe_" + tab.name);
                $(clickIframe.contentWindow).resize();
                NProgress.done();
            });
        },
        //移除tab
        removeTab: function (targetName) {
            if (targetName == '1') {
                return;
            }
            var tabs = this.tabs;
            var activeName = this.currentTab;
            if (activeName === targetName) {
                tabs.forEach(function (tab, index) {
                    if (tab.name === targetName) {
                        var nextTab = tabs[index + 1] || tabs[index - 1];
                        if (nextTab) {
                            window.location.hash = nextTab.url;
                        }
                    }
                });
            }
            this.tabs = tabs.filter(function (tab) {
                return tab.name !== targetName;
            });
        },
        //移除非当前tab
        removeOtherTab: function () {
            var currentTab = this.currentTab;
            this.tabs = this.tabs.filter(function (tab) {
                return tab.name === currentTab || tab.name === '1';
            });
        },
        removeAllTab: function () {
            window.location.hash = 'main.html';
            this.currentTab = '1';
            this.tabs = this.tabs.filter(function (tab) {
                return tab.name === '1';
            });
        },
        //根据url展开菜单
        openMenu: function (url) {
            var a = $("a[url='" + url + "']");
            if ((a == undefined || a.length == 0) && url.indexOf('?') != -1) {
                var preUrl = url.split("?")[0] + "?";
                var params = url.split("?")[1].split("&");
                for (var i = 0; i < params.length; i++) {
                    preUrl += params[i] + "&";
                    url = preUrl.substring(0, preUrl.length - 1);
                    a = $("a[url='" + url + "']");
                    if (a != undefined && a.length > 0) {
                        break;
                    }
                }
            }
            $(".sidebar-menu li").removeClass("active");
            $(".sidebar-menu ul").removeClass("menu-open");
            $("ul .treeview-menu").css("display", "none");
            if ((a == undefined || a.length == 0)) {
                $(".sidebar-menu li:first ul:first").addClass("menu-open");
                $(".sidebar-menu li:first ul:first").css("display", "block")
                $(".sidebar-menu li:first").addClass("active");
                return;
            }
            $(a).parents("ul .treeview-menu").addClass("menu-open");
            $(a).parents("ul .treeview-menu").css("display", "block");
            $(a).parents("li").addClass("active");
        },
        getMenuList: function () {
            $.getJSON("sys/menu/nav", function (r) {
                vm.menuList = r.menuList;
                vm.router(true);
            });
        },
        getMenuList: function (event) {
            $.getJSON("sys/menu/nav?_" + $.now(), function (r) {
                if (r.code == 0) {
                    $.each(r.menuList, function () {
                        vm.initMenuTodoCount(this);
                    })
                    vm.menuList = r.menuList;
                    vm.router(true);
                    vm.$nextTick(function () {
                        $.each(r.menuList, function () {
                            vm.getMenuTodoCount(this)
                        })
                    })
                }
            });
        },
        //初始化todoCount
        initMenuTodoCount: function (menu) {
            menu.todoCount = 0;
            if (menu.type === 0) {
                $.each(menu.list, function () {
                    vm.initMenuTodoCount(this)
                })
            }
        },
        //通过url获取待办任务个数
        getMenuTodoCountByUrl: function (url) {
            var menu = vm.getMenuByUrl(url, vm.menuList);
            if (menu.url == url) {
                //是一级目录直接更新，
                if (menu.parentId == '-1') {
                    vm.getMenuTodoCount(menu);
                } else {
                    //认为只有两级目录,更新父级目录级所有子级目录
                    for (var i = 0; i < vm.menuList.length; i++) {
                        var parentMenu = vm.menuList[i];
                        if (parentMenu.menuId == menu.parentId) {
                            vm.getMenuTodoCount(parentMenu);
                            return;
                        }
                    }
                    //只有两级目录的情况下，不会执行到这里
                    vm.getMenuTodoCount(menu);
                    console.warn("directory level greater than 2,need check todo count");
                }
            }
        },
        //获取所有菜单待办任务个数
        getAllMenuTodoCount: function () {
            var that=this;
            $.each(this.menuList, function () {
                that.getMenuTodoCount(this)
            })
        },
        //获取待办任务个数
        getMenuTodoCount: function (menu) {
            if (menu.type === 1 && menu.todoUrl != null && menu.todoUrl != '') {
                $.ajax({
                    type: "POST",
                    url: menu.todoUrl,
                    contentType: "application/json",
                    async: false,
                    success: function (r) {
                        if (r.code == 0) {
                            menu.todoCount = r.count;
                        } else {
                            console.log("error:" + r.msg);
                        }
                    }
                });
            } else if (menu.type === 0) {
                menu.todoCount = 0;
                for (var i = 0; i < menu.list.length; i++) {
                    menu.todoCount += vm.getMenuTodoCount(menu.list[i]);
                }
            }
            return menu.todoCount;
        },
        getUser: function () {
            $.getJSON("sys/user/info?_" + $.now(), function (r) {
                vm.user = r.user;
                vm.loginType = r.loginType;
                WebSocketUtils.ws && WebSocketUtils.ws.readyState === 1 && WebSocketUtils.newUserMessage(vm.user.userId);
            });
        },
        updatePassword: function () {
            layer.open({
                type: 1,
                title: "修改密码",
                area: ['550px', '400px'],
                shadeClose: false,
                content: jQuery("#passwordLayer"),
                btn: ['修改', '取消'],
                btn1: function (index) {
                    if (!validateForm("frm")) {
                        return
                    }
                    var data = "password=" + vm.password + "&newPassword=" + vm.newPassword;
                    $.ajax({
                        type: "POST",
                        url: "sys/user/password",
                        data: data,
                        dataType: "json",
                        success: function (result) {
                            if (result.code == 0) {
                                layer.close(index);
                                layer.alert('修改成功', function (index) {
                                    location.reload();
                                });
                            } else {
                                layer.alert(result.msg);
                            }
                        }
                    });
                }
            });
        },
        //根据url获取对应menu对象
        getMenuByUrl: function (url, menuList) {
            for (var i = 0; i < menuList.length; i++) {
                var menu = menuList[i];
                if (menu.type === 1 && menu.url == url) {
                    return menu;
                } else if (menu.type === 0) {
                    var result = vm.getMenuByUrl(url, menu.list);
                    if (result.url == url) {
                        return result;
                    }
                }
            }
            return {};
        },
        router: function (e) {
            NProgress.start();
            if (e && typeof (e) == 'boolean') {
                window.addEventListener("hashchange", vm.router);
            }

            var hash = window.location.hash.replace("#", '');
            if (hash != '') {
                //检查是否已经添加
                for (var i = 0; i < this.tabs.length; i++) {
                    if (hash == this.tabs[i].url) {
                        this.currentTab = this.tabs[i].name;
                        this.openMenu(hash);
                        NProgress.done();
                        return;
                    }
                }
                var menu = this.getMenuByUrl(hash, this.menuList);
                if (menu && menu.url) {
                    this.addTab(menu);
                }
            }
            this.openMenu(hash);
            NProgress.done();
        },
        logout:function () {
            if (this.loginType == 'token') {
                AppUtils.loadingStart("正在关闭...");
                vm.sendExitMsg(function () {
                    // AppUtils.loadingEnd();
                    window.location.href = "logout"
                });
            }else {
                window.location.href = "logout"
            }
        },
        //初始化ws
        initWebSocket: function () {
            vm.webSocket = vm.webSocket || new WebSocket("ws://127.0.0.1:50018");
            vm.webSocket.onopen = vm.webSocket.onopen || function () {
                console.log("Local web socket is ready!!!");
            };
            vm.webSocket.onmessage = vm.webSocket.onmessage || function (evt) {
                console.log(evt);
            };
            vm.webSocket.onerror = vm.webSocket.onerror || function (evt) {
                console.error(evt);
            };
            window.onbeforeunload = function () {
                vm.webSocket.close();
                console.log("active close websocket");
            };
        },
        //发送消息 {"Commond":"ExitSystem","Parmes":{""}}
        sendExitMsg: function (callback) {
            if (!vm.webSocket || vm.webSocket.readyState === 3) {
                if (vm.retryExitTimes < vm.maxRetryTimes) {
                    vm.retryExitTimes++;
                    console.log("ws retry times:" + vm.retryExitTimes);
                    vm.initWebSocket();
                    vm.sendExitMsg(callback);
                }else{
                    callback();
                }
            }
            // 连接中
            else if (vm.webSocket.readyState === 0) {
                if (vm.retryExitTimes < vm.maxRetryTimes) {
                    vm.retryExitTimes++;
                    console.log("ws retry times:" + vm.retryExitTimes);
                    setTimeout(function () {
                        vm.sendExitMsg(callback);
                    }, 200);
                }else{
                    callback();
                }
            } else if (vm.webSocket.readyState === 1) {
                vm.webSocket.send('{"Commond":"ExitSystem","Parmes":{"Commond":"ExitSystem"}}');
                setTimeout(function () {
                    callback();
                }, 200);
            }
        },
        //获取未读消息个数
        notViewCount:function () {
            $.post("sys/siteNotice/notViewCount", function (r) {
                if (r.code == 0) {
                    vm.msgCount = r.count;
                } else {
                    alert(r.msg)
                }
            })
        }
    },
    created: function () {
        this.getMenuList();
        this.getUser();
        this.pp = pp;
        this.addTab({
            name: '',
            url: 'main.html',
            icon: 'fa fa-home font-size-18'
        })
        //注册websocket监听方法
        var that = this;
        WebSocketUtils.registerHandler("updateTodoCount", function (wsMessage) {
            //延迟一秒再更新，防止事物未提交
            setTimeout(function () {
                if (wsMessage.content) {
                    that.getMenuTodoCountByUrl(wsMessage.content);
                } else {
                    that.getAllMenuTodoCount();
                }
            },1000)
        })
        WebSocketUtils.registerHandler("updateMsgCount", function (wsMessage) {
            vm.$message({
                message: "您有新的未读消息，请注意查看！",
                type: 'success'
            })
            console.log(wsMessage);
            vm.msgCount = wsMessage.content;
        })
        //页面加载时获取未读消息个数
        this.notViewCount();
    },
});

$(function () {
    validateInputs("password,newPassword,conPassword");
})
