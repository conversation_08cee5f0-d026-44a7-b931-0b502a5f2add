<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.audit.dao.AuditFlowDao">

    <sql id="BaseSQL">
        ID
        , ISSUE_TYPE, RESPONSIBLE_PERSON, RIVER_NAME, VILLAGE,
    HAS_SAND_PIT, ILLEGAL_EXTRACTION_VOLUME, ADMINISTRATIVE_CASE,
    TRANSFERRED_TO_POLICE, INVOLVED_IN_ORGANIZED_CRIME, REMEDIATION_STATUS,
    NOTES, LON<PERSON>TUDE, LATITUDE, SAND_PIT_COUNT, SAND_PIT_AREA,
    SAND_PIT_DEPTH, SUSPECTS_COUNT, FINE_AMOUNT, ADMINISTRATIVE_CASE_DETAILS,
    CASE_CLOSED, REMEDIATION_MEASURES_DEFINED, RESPONSIBLE_UNIT, CHAR<PERSON>_PERSON,
    COMPLETION_DEADLINE, R<PERSON>TIFICATION_COMPLETED, PERSONNEL_RESPONSIBILITY,
    CREATE_TIME, UPDATE_TIME, CREATE_USER_ID, UPDATE_USER_ID,
    AREA_NAME, AREA_CODE, NEW_MONTH, FLOW_NO,JOINT_LAW_ENFORCEMENT,
        WATER_ENFORCEMENT_PERSON,TOWNSHIP_PERSON,POLICE_PERSON,OTHER_PERSON,ILLEGAL_SAND_MINING,
        CRIMINAL_FILING,SOURCE_CLUES,LIABILITY,LIABILITY_NUMBER_PERSONS,FILING_TIME
    </sql>

    <sql id = "StatisticsSQL">
        ID
        ,JOINT_LAW_ENFORCEMENT,WATER_ENFORCEMENT_PERSON,TOWNSHIP_PERSON,POLICE_PERSON,OTHER_PERSON,ILLEGAL_SAND_MINING,CRIMINAL_FILING,SOURCE_CLUES,ILLEGAL_EXTRACTION_VOLUME,
        FINE_AMOUNT,ADMINISTRATIVE_CASE,SUSPECTS_COUNT,INVOLVED_IN_CRIME_AND_EVIL,TRANSFERRED_TO_POLICE
    </sql>

    <sql id="StatisticsCountSQL">
        LEFT(AREA_NAME, LOCATE('市', AREA_NAME)) AS areaName,
        SUM(JOINT_LAW_ENFORCEMENT) AS jointLawEnforcement,
    SUM(WATER_ENFORCEMENT_PERSON) AS waterEnforcementPerson,
    SUM(TOWNSHIP_PERSON) AS townshipPerson,
    SUM(POLICE_PERSON) AS policePerson,
    SUM(OTHER_PERSON) AS otherPerson,
    SUM(ILLEGAL_SAND_MINING) AS illegalSandMining,
    SUM(CRIMINAL_FILING) AS criminalFiling,
    SUM(SOURCE_CLUES) AS sourceClues,
        SUM(ILLEGAL_EXTRACTION_VOLUME) AS illegalExtractionVolume,
        SUM(FINE_AMOUNT) AS fineAmount,
        SUM(ADMINISTRATIVE_CASE) AS administrativeCase,
        SUM(SUSPECTS_COUNT) AS suspectsCount,
SUM(INVOLVED_IN_CRIME_AND_EVIL) AS involvedInCrimeAndEvil,
    SUM(TRANSFERRED_TO_POLICE) AS transferredToPolice
    </sql>
    <delete id="deleteByMasterId" parameterType="java.lang.String">
        UPDATE sand_disposal_report SET DELETED = 1 where SMM_SAND_DISPOSAL_REPORT_ID = #{id}
    </delete>

    <resultMap id="StatementDoResultMap" type="com.ybkj.smm.modules.statement.entity.StatementDo">
        <result column="SOURCE_CLUES" property="sourceClues" />
        <result column="ADMINISTRATIVE_CASE" property="administrativeCase" />
        <result column="TRANSFERRED_TO_POLICE" property="transferredToPolice" />
        <result column="INVOLVED_IN_CRIME_AND_EVIL" property="involvedInCrimeAndEvil" />
        <result column="SUSPECTS_COUNT" property="suspectsCount" />
        <result column="ILLEGAL_EXTRACTION_VOLUME" property="illegalExtractionVolume" />
        <result column="FINE_AMOUNT" property="fineAmount" />
        <result column="JOINT_LAW_ENFORCEMENT" property="jointLawEnforcement" />
        <result column="WATER_ENFORCEMENT_PERSON" property="waterEnforcementPerson" />
        <result column="TOWNSHIP_PERSON" property="townshipPerson" />
        <result column="POLICE_PERSON" property="policePerson" />
        <result column="OTHER_PERSON" property="otherPerson" />
        <result column="ILLEGAL_SAND_MINING" property="illegalSandMining" />
        <result column="CRIMINAL_FILING" property="criminalFiling" />
    </resultMap>


    <select id="getDeptByUserId" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT sur.USER_ID
        FROM sys_user_role sur
                 JOIN sys_user su ON sur.USER_ID = su.USER_ID
        WHERE su.DEPT_ID = (SELECT su2.DEPT_ID
                            FROM sys_user su2
                            WHERE su2.USER_ID = #{userId})
          AND sur.ROLE_ID = '01JM0ZSN3V3K0MTQ0Q5K2DWWBP'
    </select>
    <select id="getUserByUserId" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT sur.USER_ID
        FROM sys_user_role sur
                 JOIN sys_user su ON sur.USER_ID = su.USER_ID
                 JOIN sys_dept sd ON su.DEPT_ID = sd.DEPT_ID
        WHERE sd.DEPT_ID = (
            SELECT d.PARENT_ID
            FROM sys_dept d
                     JOIN sys_user u ON u.DEPT_ID = d.DEPT_ID
            WHERE u.USER_ID = #{userId}
        )
          AND sur.ROLE_ID = '01JM0ZSN3V3K0MTQ0Q5K2DWWBP';
    </select>
    <select id="selectAuditFlowData" resultType="com.ybkj.smm.modules.statement.entity.StatementDo"
            parameterType="java.lang.String">
        SELECT
        <include refid="BaseSQL"/>
        FROM sand_disposal_report sdr
        WHERE EXISTS (
        SELECT 1
        FROM smm_audit_flow saf
        JOIN smm_audit_flow_detail safd ON saf.FLOW_NO = safd.FLOW_NO
        WHERE saf.APPRO_STATUS = 1
        AND safd.AUDIT_STATUS = 2
        AND safd.AUDIT_USER_ID = #{currentUserId}
        AND saf.FLOW_NO = sdr.FLOW_NO
        )
        AND sdr.FLOW_NO = (SELECT FLOW_NO FROM sand_disposal_report WHERE ID = #{id})
        AND DELETED = 0
    </select>
    <select id="selectByFlowNoList" resultType="com.ybkj.smm.modules.audit.entity.AuditFlowDetailDo"
            parameterType="java.lang.String">
        select ID, FLOW_NO, AUDIT_USER_ID, AUDIT_REMARK, AUDIT_STATUS, CREATE_TIME
        from smm_audit_flow_detail
        where FLOW_NO = #{flowNo}
    </select>
    <select id="getFlowNo" resultType="java.lang.String" parameterType="java.lang.String">
        select FLOW_NO from smm_sand_disposal_report where ID = #{id} AND DELETED = 0
    </select>
    <select id="getByFlowNo" resultType="com.ybkj.smm.modules.audit.entity.AuditFlowDetailDo"
            parameterType="java.lang.String">
        select ID, FLOW_NO, AUDIT_USER_ID, AUDIT_REMARK, AUDIT_STATUS, CREATE_TIME
        from smm_audit_flow_detail
        where FLOW_NO = #{flowNo}
    </select>
    <select id="selectByCurrentUserId" resultType="com.ybkj.smm.modules.statement.entity.StatementDo">
        select *
        from sand_disposal_report where FLOW_NO in (select FLOW_NO from smm_audit_flow_detail where AUDIT_USER_ID =
        #{currentUserId}) AND SMM_SAND_DISPOSAL_REPORT_ID = #{id} AND DELETED = 0 LIMIT #{pageSize} offset #{offset}
    </select>
<!--    <select id="selectCountByUserId" resultType="java.lang.Integer" parameterType="java.lang.String">-->
<!--        select count(*)-->
<!--        from sand_disposal_report-->
<!--        where FLOW_NO in (select FLOW_NO from smm_audit_flow_detail where AUDIT_USER_ID = #{currentUserId})-->
<!--        AND COUNTY_STATUS = 0 AND (STATUS = 0 OR STATUS = 2) AND DELETED = 0-->
<!--    </select>-->
    <select id="selectAuditFlowDetailDoListByCurrentUserId"
            resultType="com.ybkj.smm.modules.audit.entity.AuditFlowDetailDo" parameterType="java.lang.String">
        select ID, FLOW_NO, AUDIT_USER_ID, AUDIT_REMARK, AUDIT_STATUS, CREATE_TIME
        from smm_audit_flow_detail
        where AUDIT_USER_ID = #{currentUserId}
    </select>
    <select id="selectByCurrentUserIdAndCityStatus" resultType="com.ybkj.smm.modules.statement.entity.StatementDo">
        select *
        from sand_disposal_report where FLOW_NO in (select FLOW_NO from smm_audit_flow_detail where AUDIT_USER_ID =
        #{currentUserId}) AND SMM_SAND_DISPOSAL_REPORT_ID = #{id} AND DELETED = 0 LIMIT #{pageSize} offset #{offset}
    </select>
    <select id="selectCountByUserIdAndCityStatus" resultType="java.lang.Integer"
            parameterType="java.lang.String">
        select count(*)
        from smm_sand_disposal_report
        where FLOW_NO in (select FLOW_NO from smm_audit_flow_detail where AUDIT_USER_ID = #{currentUserId})
          AND COUNTY_STATUS IS NOT NULL AND COUNTY_STATUS > 0 AND DELETED = 0
        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' ">
            AND FILING_TIME_ALL BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="status != null">
            AND STATUS = #{status}
        </if>
    </select>
    <select id="selectAllData" resultType="com.ybkj.smm.modules.statement.entity.StatementDo">
        select <include refid="StatisticsSQL"/> from sand_disposal_report where DELETED = 0 limit #{pageSize} offset #{offset}
    </select>
    <select id="selectCountAll" resultType="java.lang.Integer">
        select count(*) from sys_area where ID LIKE '000_013_00_'
    </select>
    <select id="selectAllCountData" resultType="com.ybkj.smm.modules.statement.entity.SandDisposalReportVo">
        SELECT b.AREA_NAME,
        b.AREA_CODE,

        COALESCE(SUM(a.JOINT_LAW_ENFORCEMENT), 0) + COALESCE(SUM(c.JOINT_LAW_ENFORCEMENT), 0)       as JOINT_LAW_ENFORCEMENT, -- 联合执法
        COALESCE(SUM(a.WATER_ENFORCEMENT_PERSON), 0) + COALESCE(SUM(c.WATER_ENFORCEMENT_PERSON), 0) as WATER_ENFORCEMENT_PERSON, -- 水行政执法
        COALESCE(SUM(a.POLICE_PERSON), 0) + COALESCE(SUM(c.POLICE_PERSON), 0)                       as POLICE_PERSON, -- 警务
        COALESCE(SUM(a.OTHER_PERSON), 0) + COALESCE(SUM(c.OTHER_PERSON), 0)                         as OTHER_PERSON, -- 其他行政执法
        COALESCE(SUM(d.ADMINISTRATIVE_CASE), 0)                                                     as ILLEGAL_SAND_MINING, -- 案件外查处非法采砂(处)
        COALESCE(SUM(a.SOURCE_POLICE), 0) + COALESCE(SUM(c.SOURCE_POLICE), 0)                       as SOURCE_POLICE, -- 案件外非法采砂线索来源公安部门（个）
        COALESCE(SUM(a.SOURCE_NATURAL_RESOURCES), 0) + COALESCE(SUM(c.SOURCE_NATURAL_RESOURCES), 0) as SOURCE_NATURAL_RESOURCES, -- 案件外非法采砂线索来源自然资源部门（个）
        COALESCE(SUM(a.SOURCE_TRANSPORT), 0) + COALESCE(SUM(c.SOURCE_TRANSPORT), 0)                 as SOURCE_TRANSPORT, -- 案件外非法采砂线索来源交通运输部门（个）
        COALESCE(SUM(a.PUBLICITY_MATERIALS), 0)                                                     as PUBLICITY_MATERIALS, -- 宣传材料
        COALESCE(SUM(a.MEDIA_REPORTS), 0)                                                           as MEDIA_REPORTS, -- 宣传报道
        COALESCE(SUM(d.ADMINISTRATIVE_CASE), 0)                                                     as ADMINISTRATIVE_CASE, -- 行政立案数量
        COALESCE(SUM(e.TRANSFERRED_TO_POLICE), 0)                                                   as TRANSFERRED_TO_POLICE, -- 移送公安数量
        COALESCE(SUM(f.ADMINISTRATIVE_CASE_PERSON), 0)                                              as ADMINISTRATIVE_CASE_PERSON, -- 行政立案人数
        COALESCE(SUM(g.TRANSFERRED_TO_POLICE_PERSON), 0)                                            as TRANSFERRED_TO_POLICE_PERSON, -- 移送公安人数
        COALESCE(SUM(c.CRIMINAL_FILING), 0)                                                         as CRIMINAL_FILING, -- 刑事立案数
        COALESCE(SUM(c.ILLEGAL_EXTRACTION_VOLUME), 0)                                               as ILLEGAL_EXTRACTION_VOLUME, -- 非法采砂数量
        COALESCE(SUM(c.FINE_AMOUNT), 0)                                                             as FINE_AMOUNT, -- 罚款金额
        COALESCE(SUM(c.INVOLVED_IN_CRIME_AND_EVIL), 0) + COALESCE(SUM(a.TRANSFERRED_TO_POLICE), 0)                                              as INVOLVED_IN_CRIME_AND_EVIL, -- 移交涉黑涉恶线索（条）
        COALESCE(SUM(c.CONFISCATION_ILLEGAL_GAINS),0)                                               as CONFISCATION_ILLEGAL_GAINS -- 没收违法所得

        FROM smm_sand_disposal_report a
        LEFT JOIN
        (SELECT b.AREA_NAME, a.ID, b.AREA_CODE as AREA_CODE
        FROM smm_sand_disposal_report a
        LEFT JOIN (SELECT AREA_CODE, AREA_NAME
        FROM sys_area
        WHERE AREA_CODE LIKE '000_013_0__' OR AREA_CODE LIKE '000_013_00_') b
        ON
        a.AREA_CODE = b.AREA_CODE
        OR SUBSTRING(a.AREA_CODE, 1, LENGTH(a.AREA_CODE) -
        LENGTH(SUBSTRING_INDEX(a.AREA_CODE, '_', -1)) -
        1) = b.AREA_CODE
        WHERE a.DELETED = '0'
        ) b
        ON
        a.ID = b.ID
        LEFT JOIN (SELECT SMM_SAND_DISPOSAL_REPORT_ID,
        SUM(JOINT_LAW_ENFORCEMENT)      as JOINT_LAW_ENFORCEMENT,
        SUM(WATER_ENFORCEMENT_PERSON)   as WATER_ENFORCEMENT_PERSON,
        SUM(POLICE_PERSON)              as POLICE_PERSON,
        SUM(OTHER_PERSON)               as OTHER_PERSON,
        SUM(ILLEGAL_SAND_MINING)        as ILLEGAL_SAND_MINING,
        SUM(SOURCE_CLUES = '1')         as SOURCE_POLICE,
        SUM(SOURCE_CLUES = '2')         as SOURCE_NATURAL_RESOURCES,
        SUM(SOURCE_CLUES = '3')         as SOURCE_TRANSPORT,
        SUM(TRANSFERRED_TO_POLICE)      as TRANSFERRED_TO_POLICE,
        SUM(ILLEGAL_EXTRACTION_VOLUME)  as ILLEGAL_EXTRACTION_VOLUME,
        SUM(FINE_AMOUNT)                as FINE_AMOUNT,
        SUM(CONFISCATION_ILLEGAL_GAINS) as CONFISCATION_ILLEGAL_GAINS,
        SUM(ADMINISTRATIVE_CASE)        as ADMINISTRATIVE_CASE,
        SUM(SUSPECTS_COUNT)             as SUSPECTS_COUNT,
        SUM(INVOLVED_IN_CRIME_AND_EVIL) as INVOLVED_IN_CRIME_AND_EVIL,
        SUM(CRIMINAL_FILING)            as CRIMINAL_FILING

        FROM sand_disposal_report
        WHERE DELETED = '0'

<!--        <if test="year != null and year != ''">-->
<!--            AND SUBSTRING(FILING_TIME, 1, 4) = #{year}-->
<!--        </if>-->
<!--        <if test="month != null and month != ''">-->
<!--            AND SUBSTRING(FILING_TIME, 6, 2) = #{month}-->
<!--        </if>-->
<!--        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' ">-->
<!--            AND FILING_TIME BETWEEN #{startDate} AND #{endDate}-->
<!--        </if>-->
<!--        <if test="(startDate == null or startDate == '') and (endDate == null or endDate == '')">-->
<!--            AND LEFT(FILING_TIME, 4) = LEFT(#{startDate},4)-->
<!--        </if>-->

        GROUP BY SMM_SAND_DISPOSAL_REPORT_ID) c
        ON
        a.ID = c.SMM_SAND_DISPOSAL_REPORT_ID

        LEFT JOIN
        (SELECT SMM_SAND_DISPOSAL_REPORT_ID, COUNT(ADMINISTRATIVE_CASE) as ADMINISTRATIVE_CASE
        FROM sand_disposal_report
        WHERE ADMINISTRATIVE_CASE = '1' AND DELETED = '0'

    <!--    <if test="year != null and year != ''">
            AND SUBSTRING(FILING_TIME, 1, 4) = #{year}
        </if>
        <if test="month != null and month != ''">
            AND SUBSTRING(FILING_TIME, 6, 2) = #{month}
        </if>-->
<!--        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' ">-->
<!--            AND FILING_TIME BETWEEN #{startDate} AND #{endDate}-->
<!--        </if>-->
<!--        <if test="(startDate == null or startDate == '') and (endDate == null or endDate == '')">-->
<!--            AND LEFT(FILING_TIME, 4) = LEFT(#{startDate},4)-->
<!--        </if>-->

        GROUP BY SMM_SAND_DISPOSAL_REPORT_ID) d
        ON
        a.ID = d.SMM_SAND_DISPOSAL_REPORT_ID

        LEFT JOIN
        (SELECT SMM_SAND_DISPOSAL_REPORT_ID, COUNT(TRANSFERRED_TO_POLICE) as TRANSFERRED_TO_POLICE
        FROM sand_disposal_report
        WHERE TRANSFERRED_TO_POLICE = '1' AND DELETED = '0'

  <!--      <if test="year != null and year != ''">
            AND SUBSTRING(FILING_TIME, 1, 4) = #{year}
        </if>
        <if test="month != null and month != ''">
            AND SUBSTRING(FILING_TIME, 6, 2) = #{month}
        </if>-->
<!--        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' ">-->
<!--            AND FILING_TIME BETWEEN #{startDate} AND #{endDate}-->
<!--        </if>-->
<!--        <if test="(startDate == null or startDate == '') and (endDate == null or endDate == '')">-->
<!--            AND LEFT(FILING_TIME, 4) = LEFT(#{startDate},4)-->
<!--        </if>-->

        GROUP BY SMM_SAND_DISPOSAL_REPORT_ID) e
        ON
        a.ID = e.SMM_SAND_DISPOSAL_REPORT_ID

        LEFT JOIN
        (SELECT SMM_SAND_DISPOSAL_REPORT_ID, SUM(SUSPECTS_COUNT) as ADMINISTRATIVE_CASE_PERSON
        FROM sand_disposal_report
        WHERE ADMINISTRATIVE_CASE = '1' AND DELETED = '0'

      <!--  <if test="year != null and year != ''">
            AND SUBSTRING(FILING_TIME, 1, 4) = #{year}
        </if>
        <if test="month != null and month != ''">
            AND SUBSTRING(FILING_TIME, 6, 2) = #{month}
        </if>-->
<!--        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' ">-->
<!--            AND FILING_TIME BETWEEN #{startDate} AND #{endDate}-->
<!--        </if>-->
<!--        <if test="(startDate == null or startDate == '') and (endDate == null or endDate == '')">-->
<!--            AND LEFT(FILING_TIME, 4) = LEFT(#{startDate},4)-->
<!--        </if>-->


        GROUP BY SMM_SAND_DISPOSAL_REPORT_ID) f
        ON
        a.ID = f.SMM_SAND_DISPOSAL_REPORT_ID

        LEFT JOIN
        (SELECT SMM_SAND_DISPOSAL_REPORT_ID, SUM(SUSPECTS_COUNT) as TRANSFERRED_TO_POLICE_PERSON
        FROM sand_disposal_report
        WHERE TRANSFERRED_TO_POLICE = '1' AND DELETED = '0'

       <!-- <if test="year != null and year != ''">
            AND SUBSTRING(FILING_TIME, 1, 4) = #{year}
        </if>
        <if test="month != null and month != ''">
            AND SUBSTRING(FILING_TIME, 6, 2) = #{month}
        </if>-->
<!--        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' ">-->
<!--            AND FILING_TIME BETWEEN #{startDate} AND #{endDate}-->
<!--        </if>-->
<!--        <if test="(startDate == null or startDate == '') and (endDate == null or endDate == '')">-->
<!--            AND LEFT(FILING_TIME, 4) = DATE_FORMAT(NOW(), '%Y')-->
<!--        </if>-->


        GROUP BY SMM_SAND_DISPOSAL_REPORT_ID) g
        ON
        a.ID = g.SMM_SAND_DISPOSAL_REPORT_ID

        WHERE a.DELETED = '0' AND a.STATUS = '1'
        /*AND a.ID IN (
        SELECT DISTINCT SMM_SAND_DISPOSAL_REPORT_ID
        FROM sand_disposal_report
        WHERE DELETED = '0'*/
      <!--  <if test="year != null and year != ''">
        AND SUBSTRING(FILING_TIME, 1, 4) = #{year}
        </if>
        <if test="month != null and month != ''">
        AND SUBSTRING(FILING_TIME, 6, 2) = #{month}
        </if>-->
        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' ">
            AND FILING_TIME_ALL BETWEEN #{startDate} AND #{endDate}
        </if>
<!--        <if test="(startDate == null or startDate == '') and (endDate == null or endDate == '')">-->
<!--            AND LEFT(FILING_TIME_ALL, 4) = DATE_FORMAT(NOW(), '%Y')-->
<!--        </if>-->
#         )
        <if test="deptCode != null and deptCode != ''">
            AND b.AREA_CODE = (SELECT AREA_CODE FROM `sys_dept`
            WHERE DEPT_CODE = #{deptCode})
        </if>
        GROUP BY b.AREA_NAME, b.AREA_CODE
    </select>
    <select id="pageStatement" resultType="com.ybkj.smm.modules.statement.entity.MasterStatementVo">
        SELECT *
        FROM(
        SELECT a.NAME AS deptName, a.AREA_NAME, a.AREA_CODE,b.fillTime
        FROM
        (SELECT sd.NAME,sa.AREA_NAME,sd.AREA_CODE FROM `sys_dept` sd
        LEFT JOIN sys_area sa ON sd.AREA_CODE = sa.AREA_CODE
        WHERE sd.TYPE='dept' AND sd.DEL_FLAG = '0'
        <if test="areaCode != null and areaCode != ''">
            AND sd.AREA_CODE LIKE CONCAT (#{areaCode},'%')
        </if>
        <if test="sqlFilter != null and sqlFilter != ''">
            AND ${sqlFilter}
        </if>) a,
        (  select DATE_FORMAT(CONCAT(#{startTime},'-01') + INTERVAL n MONTH, '%Y-%m') AS fillTime
        FROM (
        SELECT a.n + b.n * 10 + c.n * 100 AS n
        FROM (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a,
        (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b,
        (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c
        ) numbers
        <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
            WHERE CONCAT(#{endTime},'-31') >= CONCAT(#{startTime},'-01') + INTERVAL n MONTH
        </if>
        ) b
        ) e
        LEFT JOIN smm_sand_disposal_report c
        ON e.fillTime = c.FILING_TIME_ALL AND e.AREA_CODE = c.AREA_CODE AND (c.DELETED = 0 OR c.DELETED IS NULL)
            <where>
                <if test="fillStatus != null and fillStatus != ''">
                    <choose>
                        <when test="fillStatus == 'filled'">
                            AND c.FILING_TIME_ALL IS NOT NULL
                        </when>
                        <when test="fillStatus == 'notFill'">
                            AND c.FILING_TIME_ALL IS NULL
                        </when>
                    </choose>
                </if>
                <if test="status != null and status != ''">
                    AND c.STATUS = #{status}
                </if>
                <if test="countyStatus != null and countyStatus != ''">
                    AND c.COUNTY_STATUS = #{countyStatus}
                </if>
                <if test="cityStatus != null and cityStatus != ''">
                    AND c.CITY_STATUS = #{cityStatus}
                </if>
            </where>
        ORDER BY c.FILING_TIME_ALL DESC,c.DEPT_CODE DESC,e.fillTime DESC,e.deptName DESC
    </select>

    <update id="deleteByMasterIds">
        UPDATE sand_disposal_report
        SET DELETED = 1
        WHERE SMM_SAND_DISPOSAL_REPORT_ID IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectAllCountDataByYear"
            resultType="com.ybkj.smm.modules.statement.entity.SandDisposalReportVo">
        SELECT
        SUBSTRING(a.FILING_TIME, 1, 4) as date, -- 月份
        aa.month as filingTime,
        SUBSTRING(a.FILING_TIME, 6, 2) AS subStringMonth,
        COALESCE(a.JOINT_LAW_ENFORCEMENT, 0) + COALESCE(b.JOINT_LAW_ENFORCEMENT, 0) as jointLawEnforcement, -- 联合执法(次)
        COALESCE(a.WATER_ENFORCEMENT_PERSON, 0) + COALESCE(b.WATER_ENFORCEMENT_PERSON, 0) as waterEnforcementPerson, -- 水行政执法，
        COALESCE(a.TOWNSHIP_PERSON, 0)                                             as townshipPerson,        -- 乡镇行政执法人员
        COALESCE(a.POLICE_PERSON, 0) + COALESCE(b.POLICE_PERSON, 0)               as policePerson,          -- 警务人员
        COALESCE(a.OTHER_PERSON, 0) + COALESCE(b.OTHER_PERSON, 0)                 as otherPerson,           -- 其它行政执法人员(自然资源/交通运输)
        COALESCE(b.PUBLICITY_MATERIALS, 0)                                        as publicityMaterials,    -- 印发宣传材料(册)
        COALESCE(b.MEDIA_REPORTS, 0)                                              as mediaReports,          -- 媒体宣传报道(次)
        COALESCE(a.ILLEGAL_SAND_MINING, 0) + COALESCE(b.ILLEGAL_SAND_MINING, 0)   as illegalSandMining,     -- 查处非法采砂(处)
        COALESCE(a.ILLEGAL_EXTRACTION_VOLUME, 0)                                  as illegalExtractionVolume, -- 盗采土方(m³)
        COALESCE(a.FINE_AMOUNT, 0)                                                as fineAmount,            -- 罚款(万元)
        COALESCE(c.ADMINISTRATIVE_CASE, 0)                                        as administrativeCase,    -- 行政立案 - 数量(起)
        COALESCE(e.ADMINISTRATIVE_CASE_PERSON, 0)                                 as administrativeCasePerson, -- 行政立案 - 涉案人数
        COALESCE(a.INVOLVED_IN_CRIME_AND_EVIL, 0) + COALESCE(b.TRANSFERRED_TO_POLICE, 0) as involvedInCrimeAndEvil, -- 移交涉黑涉恶线索(条)
        COALESCE(d.TRANSFERRED_TO_POLICE, 0)                                      as transferredToPolice,   -- 移送公安机关案件 - 数量(起)
        COALESCE(f.TRANSFERRED_TO_POLICE_PERSON, 0)                               as transferredToPolicePerson, -- 移送公安机关案件 - 涉案人数
        COALESCE(a.CRIMINAL_FILING, 0)                                            as criminalFiling,        -- 移送公安机关案件 - 刑事立案数(起)
        COALESCE(a.SOURCE_POLICE, 0) + COALESCE(b.SOURCE_POLICE, 0)               as sourcePolice,          -- 非法采砂问题线索来源(个) - 公安部门
        COALESCE(a.SOURCE_NATURAL_RESOURCES, 0) + COALESCE(b.SOURCE_NATURAL_RESOURCES, 0) as sourceNaturalResources, -- 非法采砂问题线索来源(个) - 自然资源部门
        COALESCE(a.SOURCE_TRANSPORT, 0) + COALESCE(b.SOURCE_TRANSPORT, 0)         as sourceTransport        -- 非法采砂问题线索来源(个) - 交通运输部门
        FROM
        (
        SELECT '01' AS month UNION ALL
        SELECT '02' UNION ALL
        SELECT '03' UNION ALL
        SELECT '04' UNION ALL
        SELECT '05' UNION ALL
        SELECT '06' UNION ALL
        SELECT '07' UNION ALL
        SELECT '08' UNION ALL
        SELECT '09' UNION ALL
        SELECT '10' UNION ALL
        SELECT '11' UNION ALL
        SELECT '12'
        ) aa
        LEFT JOIN
        (
        SELECT b.FILING_TIME,
        SUM(a.JOINT_LAW_ENFORCEMENT) as JOINT_LAW_ENFORCEMENT,
        SUM(a.WATER_ENFORCEMENT_PERSON) as WATER_ENFORCEMENT_PERSON,
        SUM(a.POLICE_PERSON) as POLICE_PERSON,
        SUM(a.OTHER_PERSON) as OTHER_PERSON,
        SUM(a.PUBLICITY_MATERIALS) as PUBLICITY_MATERIALS,
        SUM(a.MEDIA_REPORTS) as MEDIA_REPORTS,
        SUM(a.ILLEGAL_SAND_MINING) as ILLEGAL_SAND_MINING,
        SUM(a.TRANSFERRED_TO_POLICE) as TRANSFERRED_TO_POLICE,
        SUM(a.SOURCE_POLICE) as SOURCE_POLICE,
        SUM(a.SOURCE_NATURAL_RESOURCES) as SOURCE_NATURAL_RESOURCES,
        SUM(a.SOURCE_TRANSPORT) as SOURCE_TRANSPORT
        FROM smm_sand_disposal_report a
        LEFT JOIN sand_disposal_report b
        ON a.ID = b.SMM_SAND_DISPOSAL_REPORT_ID
        WHERE a.DELETED = '0' AND b.DELETED = '0' AND a.STATUS = '1'
        GROUP BY b.FILING_TIME
        ) b

        ON aa.month = SUBSTRING(b.FILING_TIME, 6, 2)
        LEFT JOIN
        (
        SELECT FILING_TIME,
        SUM(JOINT_LAW_ENFORCEMENT) as JOINT_LAW_ENFORCEMENT,
        SUM(WATER_ENFORCEMENT_PERSON) as WATER_ENFORCEMENT_PERSON,
        SUM(TOWNSHIP_PERSON) as TOWNSHIP_PERSON,
        SUM(POLICE_PERSON) as POLICE_PERSON,
        SUM(OTHER_PERSON) as OTHER_PERSON,
        SUM(ILLEGAL_SAND_MINING) as ILLEGAL_SAND_MINING,
        SUM(ILLEGAL_EXTRACTION_VOLUME) as ILLEGAL_EXTRACTION_VOLUME,
        SUM(FINE_AMOUNT) as FINE_AMOUNT,
        SUM(INVOLVED_IN_CRIME_AND_EVIL) as INVOLVED_IN_CRIME_AND_EVIL,
        SUM(CRIMINAL_FILING) as CRIMINAL_FILING,
        SUM(SOURCE_CLUES = 1) as SOURCE_POLICE,  -- 公安部门
        SUM(SOURCE_CLUES = 2) as SOURCE_NATURAL_RESOURCES, -- 自然资源部门
        SUM(SOURCE_CLUES = 3) as SOURCE_TRANSPORT -- 交通运输部门
        FROM sand_disposal_report
        WHERE DELETED = '0'
        GROUP BY FILING_TIME
        ) a
        ON a.FILING_TIME = b.FILING_TIME

        LEFT JOIN
        (
        SELECT FILING_TIME, COUNT(ADMINISTRATIVE_CASE) as ADMINISTRATIVE_CASE
        FROM sand_disposal_report
        WHERE ADMINISTRATIVE_CASE = '1' AND DELETED = '0'
        GROUP BY FILING_TIME
        ) c
        ON a.FILING_TIME = c.FILING_TIME
        LEFT JOIN
        (
        SELECT FILING_TIME, COUNT(TRANSFERRED_TO_POLICE) as TRANSFERRED_TO_POLICE
        FROM sand_disposal_report
        WHERE TRANSFERRED_TO_POLICE = '1' AND DELETED = '0'
        GROUP BY FILING_TIME
        ) d
        ON a.FILING_TIME = d.FILING_TIME
        LEFT JOIN
        (
        SELECT FILING_TIME, SUM(SUSPECTS_COUNT) as ADMINISTRATIVE_CASE_PERSON
        FROM sand_disposal_report
        WHERE ADMINISTRATIVE_CASE = '1' AND DELETED = '0'
        GROUP BY FILING_TIME
        ) e
        ON a.FILING_TIME = e.FILING_TIME
        LEFT JOIN
        (
        SELECT FILING_TIME, SUM(SUSPECTS_COUNT) as TRANSFERRED_TO_POLICE_PERSON
        FROM sand_disposal_report
        WHERE TRANSFERRED_TO_POLICE = '1' AND DELETED = '0'
        GROUP BY FILING_TIME
        ) f
        ON a.FILING_TIME = f.FILING_TIME
        WHERE 1=1
        <if test="year != null and year != ''">
            AND SUBSTRING(a.FILING_TIME, 1, 4) = #{year}
        </if>
        <if test="month != null and month != ''">
            AND SUBSTRING(a.FILING_TIME, 6, 2) = #{month}
        </if>
        ORDER BY aa.month ASC
    </select>

    <resultMap id="selectResultMap" type="com.ybkj.smm.modules.statement.entity.StatementDo">
        <id column="ID" property="id"/>
        <collection property="reportPersonList" ofType="com.ybkj.smm.modules.statement.entity.ReportPersonVo"
            select="com.ybkj.smm.modules.statement.dao.MasterStatementDao.queryAllByReportId" column="id"/>
    </resultMap>

    <select id="selectPageList" resultMap="selectResultMap">
        SELECT a.* FROM sand_disposal_report a LEFT JOIN smm_sand_disposal_report b ON a.SMM_SAND_DISPOSAL_REPORT_ID = b.ID
        <where>
            <if test="newMonth != null and newMonth != ''">
                AND a.NEW_MONTH = #{newMonth}
            </if>
            <if test="areaName != null and areaName != ''">
                AND a.AREA_NAME =#{areaName}
            </if>
            <if test="riverName != null and riverName != ''">
                AND a.RIVER_NAME =#{riverName}
            </if>
            <if test="issueType != null and issueType != ''">
                AND a.ISSUE_TYPE =#{issueType}
            </if>
            <!--  <if test="year != null and year != ''">
                AND SUBSTRING(a.FILING_TIME, 1, 4) = #{year}
            </if>
            <if test="month != null and month != ''">
                AND SUBSTRING(a.FILING_TIME, 6, 2) = #{month}
            </if> -->
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                AND a.FILING_TIME BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="areaCode != null and areaCode != ''">
                AND a.AREA_CODE like CONCAT(#{areaCode} , '%')
            </if>
            <if test="status != null and status != ''">
                AND b.STATUS = #{status}
            </if>
                AND b.STATUS != '3'
                AND a.DEPT_ID in (select DEPT_ID from sys_dept where DEL_FLAG=0 and DEPT_CODE like concat(#{deptCode},'%'))
        AND a.DELETED = '0' AND b.DELETED = '0'
        </where>
        ORDER BY a.FILING_TIME,a.DEPT_ID DESC
    </select>
    <select id="selectAreaCodeByDeptId" resultType="java.util.Map" parameterType="java.lang.String">
        SELECT a.DEPT_CODE,a.AREA_CODE,b.AREA_NAME FROM sys_dept a INNER JOIN sys_area b ON a.AREA_CODE = b.AREA_CODE WHERE a.DEPT_ID = #{deptId}
    </select>
    <sql id="selectStatementDataSQL">
        ID,
          AREA_NAME,
          VILLAGE,
          LONGITUDE,
          LATITUDE,
          ISSUE_TYPE,
          RIVER_NAME,
          CASE WHEN SOURCE_CLUES = 1 THEN '日常巡查'
          WHEN SOURCE_CLUES = 2 THEN '群众举报'
          WHEN SOURCE_CLUES = 3 THEN '其它部门移交'
          ELSE '未知' END AS SOURCE_CLUES,
          SAND_PIT_AREA,SAND_PIT_DEPTH,
          ILLEGAL_EXTRACTION_VOLUME,
          CASE WHEN ADMINISTRATIVE_CASE = 1 THEN '是' ELSE '否' END AS ADMINISTRATIVE_CASE,
          SUSPECTS_COUNT,
          FINE_AMOUNT,
          ADMINISTRATIVE_CASE_DETAILS,
          CASE WHEN CASE_CLOSED = 1 THEN '是' ELSE '否' END AS CASE_CLOSED,
          CASE WHEN TRANSFERRED_TO_POLICE = 1 THEN '是' ELSE '否' END AS TRANSFERRED_TO_POLICE,
          CASE WHEN INVOLVED_IN_ORGANIZED_CRIME = 1 THEN '是' ELSE '否' END AS INVOLVED_IN_ORGANIZED_CRIME,
          CASE WHEN REMEDIATION_MEASURES_DEFINED = 1 THEN '是' ELSE '否' END AS REMEDIATION_MEASURES_DEFINED,
          RESPONSIBLE_UNIT,
          CHARGE_PERSON,
          COMPLETION_DEADLINE,
          CASE WHEN RECTIFICATION_COMPLETED = 1 THEN '是' ELSE '否' END AS RECTIFICATION_COMPLETED,
          CASE WHEN PERSONNEL_RESPONSIBILITY = 1 THEN '是' ELSE '否' END AS PERSONNEL_RESPONSIBILITY,
          CASE WHEN LIABILITY = 1 THEN '是' ELSE '否' END AS LIABILITY,
          REGULATION_MEASURE,
          NOTES,
          FILING_TIME,
          CLOSING_TIME,
          CLOSING_INFORMATION
    </sql>
<!--    <select id="selectStatementData" resultType="com.ybkj.smm.modules.statement.entity.StatementToExcel">-->
<!--        SELECT <include refid="selectStatementDataSQL"/> FROM sand_disposal_report WHERE ID IN <foreach collection="ids" item="id" open="(" separator="," close=")">-->
<!--        #{id}-->
<!--    </foreach>-->
<!--    </select>-->
    <select id="selectStatementData" resultType="com.ybkj.smm.modules.statement.entity.StatementDo">
        SELECT <include refid="selectStatementDataSQL"/> FROM sand_disposal_report
        <where>
            <if test="year != null and year != ''">
                AND SUBSTRING(FILING_TIME, 1, 4) = #{year}
            </if>
            <if test="month != null and month != ''">
                AND SUBSTRING(FILING_TIME, 6, 2) = #{month}
            </if>
            AND DEPT_ID in (select DEPT_ID from sys_dept where DEL_FLAG=0 and DEPT_CODE like concat(#{deptCode},'%'))
        AND DELETED = '0'
        ORDER BY FILING_TIME DESC
        </where>
    </select>
    <update id="deleteByReportId">
        update smm_report_person set DELETED = 1 where REPORT_ID = #{reportId}
    </update>
    <select id="getParentDeptId" resultType="java.util.Map" parameterType="java.lang.String">
        SELECT
            u.DEPT_ID AS currentDeptId,
            d1.PARENT_ID AS parentDeptId,
            d2.PARENT_ID AS grandParentDeptId
        FROM sys_user u
                 LEFT JOIN sys_dept d1 ON u.DEPT_ID = d1.DEPT_ID
                 LEFT JOIN sys_dept d2 ON d1.PARENT_ID = d2.DEPT_ID
        WHERE u.USER_ID = #{userId}
    </select>
    <select id="selectStatement" resultType="com.ybkj.smm.modules.statement.entity.MasterStatementVo">
        select * from smm_sand_disposal_report
                 where FLOW_NO in
                       (select FLOW_NO from smm_audit_flow_detail where AUDIT_USER_ID = #{currentUserId}) AND COUNTY_STATUS = 0
                   AND DELETED = 0
        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' ">
            AND FILING_TIME_ALL BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="status != null">
            AND STATUS = #{status}
        </if>
        AND STATUS != 3
        ORDER BY CREATE_TIME DESC
                 limit #{pageSize} offset #{offset}
    </select>
    <select id="selectStatementByCityStatus"
            resultType="com.ybkj.smm.modules.statement.entity.MasterStatementVo">
        select * from smm_sand_disposal_report
                 where FLOW_NO in
                       (select FLOW_NO from smm_audit_flow_detail where AUDIT_USER_ID = #{currentUserId}) AND COUNTY_STATUS IS NOT NULL AND COUNTY_STATUS > 0
                   AND DELETED = 0
        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' ">
            AND FILING_TIME_ALL BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="status != null">
            AND STATUS = #{status}
        </if>
        AND STATUS != 3
        ORDER BY CREATE_TIME DESC
                 limit #{pageSize} offset #{offset}
    </select>
    <select id="selectDeptName" resultType="java.lang.String" parameterType="java.lang.String">
        select Name from sys_dept where DEPT_ID = #{deptId}
    </select>
    <select id="selectCountByUserId" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(*) from smm_sand_disposal_report
                        where FLOW_NO in
                              (select FLOW_NO from smm_audit_flow_detail where AUDIT_USER_ID = #{currentUserId}) AND COUNTY_STATUS = 0 AND (STATUS = 0 OR STATUS = 2)
                          AND DELETED = 0
    </select>
    <select id="getById" resultType="com.ybkj.smm.modules.statement.entity.StatementDo"
            parameterType="java.lang.String">
        SELECT * FROM sand_disposal_report where SMM_SAND_DISPOSAL_REPORT_ID = #{id} AND DELETED = 0
    </select>
    <select id="selectAllCountDataByYear2"
            resultType="com.ybkj.smm.modules.statement.entity.SandDisposalReportVo">
        SELECT
            SUBSTRING(aa.month, 1, 4) as date, -- 月份
        SUBSTRING(aa.month, 6, 2) as filingTime,
        SUBSTRING(a.FILING_TIME, 6, 2) AS subStringMonth,
        COALESCE(a.JOINT_LAW_ENFORCEMENT, 0) + COALESCE(b.JOINT_LAW_ENFORCEMENT, 0) as jointLawEnforcement, -- 联合执法(次)
        COALESCE(a.WATER_ENFORCEMENT_PERSON, 0) + COALESCE(b.WATER_ENFORCEMENT_PERSON, 0) as waterEnforcementPerson, -- 水行政执法
        COALESCE(a.TOWNSHIP_PERSON, 0)                                             as townshipPerson,        -- 乡镇行政执法人员
        COALESCE(a.POLICE_PERSON, 0) + COALESCE(b.POLICE_PERSON, 0)               as policePerson,          -- 警务人员
        COALESCE(a.OTHER_PERSON, 0) + COALESCE(b.OTHER_PERSON, 0)                 as otherPerson,           -- 其它行政执法人员(自然资源/交通运输)
        COALESCE(b.PUBLICITY_MATERIALS, 0)                                        as publicityMaterials,    -- 印发宣传材料(册)
        COALESCE(b.MEDIA_REPORTS, 0)                                              as mediaReports,          -- 媒体宣传报道(次)
        COALESCE(c.ADMINISTRATIVE_CASE, 0)   as illegalSandMining,     -- 查处非法采砂(处)
        COALESCE(a.ILLEGAL_EXTRACTION_VOLUME, 0)                                  as illegalExtractionVolume, -- 盗采土方(m³)
        COALESCE(a.FINE_AMOUNT, 0)                                                as fineAmount,            -- 罚款(万元)
        COALESCE(c.ADMINISTRATIVE_CASE, 0)                                        as administrativeCase,    -- 行政立案 - 数量(起)
        COALESCE(e.ADMINISTRATIVE_CASE_PERSON, 0)                                 as administrativeCasePerson, -- 行政立案 - 涉案人数
        COALESCE(a.INVOLVED_IN_CRIME_AND_EVIL, 0) + COALESCE(b.TRANSFERRED_TO_POLICE, 0) as involvedInCrimeAndEvil, -- 移交涉黑涉恶线索(条)
        COALESCE(d.TRANSFERRED_TO_POLICE, 0)                                      as transferredToPolice,   -- 移送公安机关案件 - 数量(起)
        COALESCE(f.TRANSFERRED_TO_POLICE_PERSON, 0)                               as transferredToPolicePerson, -- 移送公安机关案件 - 涉案人数
        COALESCE(a.CRIMINAL_FILING, 0)                                            as criminalFiling,        -- 移送公安机关案件 - 刑事立案数(起)
        COALESCE(a.SOURCE_POLICE, 0) + COALESCE(b.SOURCE_POLICE, 0)               as sourcePolice,          -- 非法采砂问题线索来源(个) - 日常巡查
        COALESCE(a.SOURCE_NATURAL_RESOURCES, 0) + COALESCE(b.SOURCE_NATURAL_RESOURCES, 0) as sourceNaturalResources, -- 非法采砂问题线索来源(个) - 群众举报
        COALESCE(a.SOURCE_TRANSPORT, 0) + COALESCE(b.SOURCE_TRANSPORT, 0)         as sourceTransport,        -- 非法采砂问题线索来源(个) - 其他部门移交
        COALESCE(a.CONFISCATION_ILLEGAL_GAINS, 0)                                 as confiscationIllegalGains -- 没收违法所得(万元)
        FROM
            (
            SELECT CONCAT(#{year} , '-' ,'01') AS month UNION ALL
            SELECT CONCAT(#{year} , '-' ,'02') UNION ALL
            SELECT CONCAT(#{year} , '-' ,'03') UNION ALL
            SELECT CONCAT(#{year} , '-' ,'04') UNION ALL
            SELECT CONCAT(#{year} , '-' ,'05') UNION ALL
            SELECT CONCAT(#{year} , '-' ,'06') UNION ALL
            SELECT CONCAT(#{year} , '-' ,'07') UNION ALL
            SELECT CONCAT(#{year} , '-' ,'08') UNION ALL
            SELECT CONCAT(#{year} , '-' ,'09') UNION ALL
            SELECT CONCAT(#{year} , '-' ,'10') UNION ALL
            SELECT CONCAT(#{year} , '-' ,'11') UNION ALL
            SELECT CONCAT(#{year} , '-' ,'12')
            ) aa
            LEFT JOIN
            (
            SELECT a.FILING_TIME_ALL,
            SUM(a.JOINT_LAW_ENFORCEMENT) as JOINT_LAW_ENFORCEMENT,
            SUM(a.WATER_ENFORCEMENT_PERSON) as WATER_ENFORCEMENT_PERSON,
            SUM(a.POLICE_PERSON) as POLICE_PERSON,
            SUM(a.OTHER_PERSON) as OTHER_PERSON,
            SUM(a.PUBLICITY_MATERIALS) as PUBLICITY_MATERIALS,
            SUM(a.MEDIA_REPORTS) as MEDIA_REPORTS,
            SUM(a.ILLEGAL_SAND_MINING) as ILLEGAL_SAND_MINING,
            SUM(a.TRANSFERRED_TO_POLICE) as TRANSFERRED_TO_POLICE,
            SUM(a.SOURCE_POLICE) as SOURCE_POLICE,
            SUM(a.SOURCE_NATURAL_RESOURCES) as SOURCE_NATURAL_RESOURCES,
            SUM(a.SOURCE_TRANSPORT) as SOURCE_TRANSPORT
            FROM smm_sand_disposal_report a
            WHERE a.DELETED = '0' AND a.STATUS = '1'
            AND a.AREA_CODE LIKE
            (SELECT CONCAT(AREA_CODE,'%')
            FROM sys_dept
            WHERE DEPT_CODE = #{deptCode})
            <if test="areaCode != null and areaCode != ''">
                AND a.AREA_CODE LIKE CONCAT(#{areaCode},'%')
            </if>
            GROUP BY a.FILING_TIME_ALL
            ) b
        ON SUBSTRING(aa.month, 6, 2) = SUBSTRING(b.FILING_TIME_ALL, 6, 2)
        AND SUBSTRING(aa.month, 1, 4) = SUBSTRING(b.FILING_TIME_ALL, 1, 4)
            LEFT JOIN
            (
            SELECT b.FILING_TIME,
            SUM(b.JOINT_LAW_ENFORCEMENT) as JOINT_LAW_ENFORCEMENT,
            SUM(b.WATER_ENFORCEMENT_PERSON) as WATER_ENFORCEMENT_PERSON,
            SUM(b.TOWNSHIP_PERSON) as TOWNSHIP_PERSON,
            SUM(b.POLICE_PERSON) as POLICE_PERSON,
            SUM(b.OTHER_PERSON) as OTHER_PERSON,
            SUM(b.ILLEGAL_SAND_MINING) as ILLEGAL_SAND_MINING,
            SUM(b.ILLEGAL_EXTRACTION_VOLUME) as ILLEGAL_EXTRACTION_VOLUME,
            SUM(b.FINE_AMOUNT) as FINE_AMOUNT,
            SUM(b.INVOLVED_IN_CRIME_AND_EVIL) as INVOLVED_IN_CRIME_AND_EVIL,
            SUM(b.CRIMINAL_FILING) as CRIMINAL_FILING,
            SUM(b.SOURCE_CLUES = 1) as SOURCE_POLICE,  -- 公安部门
            SUM(b.SOURCE_CLUES = 2) as SOURCE_NATURAL_RESOURCES, -- 自然资源部门
            SUM(b.SOURCE_CLUES = 3) as SOURCE_TRANSPORT, -- 交通运输部门
            SUM(b.CONFISCATION_ILLEGAL_GAINS) as CONFISCATION_ILLEGAL_GAINS -- 没收非法所得(万元)
            FROM sand_disposal_report b LEFT JOIN smm_sand_disposal_report a
            ON a.ID = b.SMM_SAND_DISPOSAL_REPORT_ID
            WHERE b.DELETED = '0' AND a.DELETED = '0' AND a.STATUS = '1'
            AND b.AREA_CODE LIKE
            (SELECT CONCAT(AREA_CODE,'%')
            FROM sys_dept
            WHERE DEPT_CODE = #{deptCode})
        <if test="areaCode != null and areaCode != ''">
            AND a.AREA_CODE LIKE CONCAT(#{areaCode},'%')
        </if>
        GROUP BY FILING_TIME
            ) a
            ON a.FILING_TIME = b.FILING_TIME_ALL

            LEFT JOIN
            (
            SELECT b.FILING_TIME, COUNT(b.ADMINISTRATIVE_CASE) as ADMINISTRATIVE_CASE
            FROM sand_disposal_report b LEFT JOIN smm_sand_disposal_report a
            ON a.ID = b.SMM_SAND_DISPOSAL_REPORT_ID
            WHERE b.ADMINISTRATIVE_CASE = '1' AND b.DELETED = '0' AND a.DELETED = '0' AND a.STATUS = '1'
            AND b.AREA_CODE LIKE
            (SELECT CONCAT(AREA_CODE,'%')
            FROM sys_dept
            WHERE DEPT_CODE = #{deptCode})
        <if test="areaCode != null and areaCode != ''">
            AND a.AREA_CODE LIKE CONCAT(#{areaCode},'%')
        </if>
        GROUP BY FILING_TIME
            ) c
            ON a.FILING_TIME = c.FILING_TIME
            LEFT JOIN
            (
            SELECT b.FILING_TIME, COUNT(b.TRANSFERRED_TO_POLICE) as TRANSFERRED_TO_POLICE
            FROM sand_disposal_report b LEFT JOIN smm_sand_disposal_report a
            ON a.ID = b.SMM_SAND_DISPOSAL_REPORT_ID
            WHERE b.TRANSFERRED_TO_POLICE = '1' AND b.DELETED = '0' AND a.DELETED = '0' AND a.STATUS = '1'
            AND b.AREA_CODE LIKE
            (SELECT CONCAT(AREA_CODE,'%')
            FROM sys_dept
            WHERE DEPT_CODE = #{deptCode})
        <if test="areaCode != null and areaCode != ''">
            AND a.AREA_CODE LIKE CONCAT(#{areaCode},'%')
        </if>
        GROUP BY FILING_TIME
            ) d
            ON a.FILING_TIME = d.FILING_TIME
            LEFT JOIN
            (
            SELECT b.FILING_TIME, SUM(b.SUSPECTS_COUNT) as ADMINISTRATIVE_CASE_PERSON
            FROM sand_disposal_report b LEFT JOIN smm_sand_disposal_report a
            ON a.ID = b.SMM_SAND_DISPOSAL_REPORT_ID
            WHERE b.ADMINISTRATIVE_CASE = '1' AND b.DELETED = '0' AND a.DELETED = '0' AND a.STATUS = '1'
            AND b.AREA_CODE LIKE
            (SELECT CONCAT(AREA_CODE,'%')
            FROM sys_dept
            WHERE DEPT_CODE = #{deptCode})
        <if test="areaCode != null and areaCode != ''">
            AND a.AREA_CODE LIKE CONCAT(#{areaCode},'%')
        </if>
        GROUP BY FILING_TIME
            ) e
            ON a.FILING_TIME = e.FILING_TIME
            LEFT JOIN
            (
            SELECT b.FILING_TIME, SUM(b.SUSPECTS_COUNT) as TRANSFERRED_TO_POLICE_PERSON
            FROM sand_disposal_report b LEFT JOIN smm_sand_disposal_report a
            ON a.ID = b.SMM_SAND_DISPOSAL_REPORT_ID
            WHERE b.TRANSFERRED_TO_POLICE = '1' AND b.DELETED = '0' AND a.DELETED = '0' AND a.STATUS = '1'
            AND b.AREA_CODE LIKE
            (SELECT CONCAT(AREA_CODE,'%')
            FROM sys_dept
            WHERE DEPT_CODE = #{deptCode})
        <if test="areaCode != null and areaCode != ''">
            AND a.AREA_CODE LIKE CONCAT(#{areaCode},'%')
        </if>
        GROUP BY FILING_TIME
            ) f
            ON a.FILING_TIME = f.FILING_TIME
        WHERE 1=1
        <if test="year != null and year != ''">
            AND SUBSTRING(aa.month, 1, 4) = #{year}
        </if>
        ORDER BY aa.month ASC
    </select>
    <select id="selectUploadFileById" resultType="com.sloth.modules.com.entity.UploadFile"
            parameterType="java.lang.String">
        SELECT a.*
        FROM com_upload_file a
                 LEFT JOIN sand_disposal_report b ON a.ID = b.UPLOAD_FILE_ID
        WHERE b.FILING_TIME = #{filingTime}
          AND b.AREA_CODE = #{areaCode}
        GROUP BY a.ID
        ORDER BY CREATE_TIME DESC
        LIMIT 1
    </select>
    <select id="getByAreaName2" resultType="com.ybkj.smm.modules.statement.entity.SandDisposalReportVo">
        SELECT b.AREA_NAME,
        b.AREA_CODE,

        COALESCE(SUM(a.JOINT_LAW_ENFORCEMENT), 0) + COALESCE(SUM(c.JOINT_LAW_ENFORCEMENT), 0)       as JOINT_LAW_ENFORCEMENT, -- 联合执法
        COALESCE(SUM(a.WATER_ENFORCEMENT_PERSON), 0) + COALESCE(SUM(c.WATER_ENFORCEMENT_PERSON), 0) as WATER_ENFORCEMENT_PERSON, -- 水行政执法
        COALESCE(SUM(a.POLICE_PERSON), 0) + COALESCE(SUM(c.POLICE_PERSON), 0)                       as POLICE_PERSON, -- 警务
        COALESCE(SUM(a.OTHER_PERSON), 0) + COALESCE(SUM(c.OTHER_PERSON), 0)                         as OTHER_PERSON, -- 其他行政执法
        COALESCE(SUM(d.ADMINISTRATIVE_CASE), 0)         as ILLEGAL_SAND_MINING, -- 案件外查处非法采砂(处)
        COALESCE(SUM(a.SOURCE_POLICE), 0) + COALESCE(SUM(c.SOURCE_POLICE), 0)                       as SOURCE_POLICE, -- 案件外非法采砂线索来源公安部门（个）
        COALESCE(SUM(a.SOURCE_NATURAL_RESOURCES), 0) + COALESCE(SUM(c.SOURCE_NATURAL_RESOURCES), 0) as SOURCE_NATURAL_RESOURCES, -- 案件外非法采砂线索来源自然资源部门（个）
        COALESCE(SUM(a.SOURCE_TRANSPORT), 0) + COALESCE(SUM(c.SOURCE_TRANSPORT), 0)                 as SOURCE_TRANSPORT, -- 案件外非法采砂线索来源交通运输部门（个）
        COALESCE(SUM(a.PUBLICITY_MATERIALS), 0)                                                     as PUBLICITY_MATERIALS, -- 宣传材料
        COALESCE(SUM(a.MEDIA_REPORTS), 0)                                                           as MEDIA_REPORTS, -- 宣传报道
        COALESCE(SUM(d.ADMINISTRATIVE_CASE), 0)                                                     as ADMINISTRATIVE_CASE, -- 行政立案数量
        COALESCE(SUM(e.TRANSFERRED_TO_POLICE), 0)                                                   as TRANSFERRED_TO_POLICE, -- 移送公安数量
        COALESCE(SUM(f.ADMINISTRATIVE_CASE_PERSON), 0)                                              as ADMINISTRATIVE_CASE_PERSON, -- 行政立案人数
        COALESCE(SUM(g.TRANSFERRED_TO_POLICE_PERSON), 0)                                            as TRANSFERRED_TO_POLICE_PERSON, -- 移送公安人数
        COALESCE(SUM(c.CRIMINAL_FILING), 0)                                                         as CRIMINAL_FILING, -- 刑事立案数
        COALESCE(SUM(c.ILLEGAL_EXTRACTION_VOLUME), 0)                                               as ILLEGAL_EXTRACTION_VOLUME, -- 非法采砂数量
        COALESCE(SUM(c.FINE_AMOUNT), 0)                                                             as FINE_AMOUNT, -- 罚款金额
        COALESCE(SUM(c.INVOLVED_IN_CRIME_AND_EVIL), 0) + COALESCE(SUM(a.TRANSFERRED_TO_POLICE), 0)                                              as INVOLVED_IN_CRIME_AND_EVIL, -- 移交涉黑涉恶线索（条）
        COALESCE(SUM(c.CONFISCATION_ILLEGAL_GAINS),0)                                               as CONFISCATION_ILLEGAL_GAINS -- 没收违法所得

        FROM smm_sand_disposal_report a
        RIGHT JOIN
        (
        SELECT b.AREA_NAME, a.ID, b.AREA_CODE as AREA_CODE
        FROM smm_sand_disposal_report a
        LEFT JOIN (SELECT AREA_CODE, AREA_NAME FROM sys_area WHERE AREA_CODE LIKE CONCAT((SELECT AREA_CODE FROM sys_area WHERE AREA_NAME = #{areaName} ), '____')) b
        ON
        a.AREA_CODE = b.AREA_CODE
        OR SUBSTRING(a.AREA_CODE, 1, LENGTH(a.AREA_CODE) -
        LENGTH(SUBSTRING_INDEX(a.AREA_CODE, '_', -1)) -
        1) = b.AREA_CODE
        WHERE a.DELETED = '0' AND b.AREA_NAME IS NOT NULL
        ) b
        ON
        a.ID = b.ID
        LEFT JOIN (SELECT SMM_SAND_DISPOSAL_REPORT_ID,
        SUM(JOINT_LAW_ENFORCEMENT)      as JOINT_LAW_ENFORCEMENT,
        SUM(WATER_ENFORCEMENT_PERSON)   as WATER_ENFORCEMENT_PERSON,
        SUM(POLICE_PERSON)              as POLICE_PERSON,
        SUM(OTHER_PERSON)               as OTHER_PERSON,
        SUM(ILLEGAL_SAND_MINING)        as ILLEGAL_SAND_MINING,
        SUM(SOURCE_CLUES = '1')         as SOURCE_POLICE,
        SUM(SOURCE_CLUES = '2')         as SOURCE_NATURAL_RESOURCES,
        SUM(SOURCE_CLUES = '3')         as SOURCE_TRANSPORT,
        SUM(TRANSFERRED_TO_POLICE)      as TRANSFERRED_TO_POLICE,
        SUM(ILLEGAL_EXTRACTION_VOLUME)  as ILLEGAL_EXTRACTION_VOLUME,
        SUM(FINE_AMOUNT)                as FINE_AMOUNT,
        SUM(CONFISCATION_ILLEGAL_GAINS) as CONFISCATION_ILLEGAL_GAINS,
        SUM(ADMINISTRATIVE_CASE)        as ADMINISTRATIVE_CASE,
        SUM(SUSPECTS_COUNT)             as SUSPECTS_COUNT,
        SUM(INVOLVED_IN_CRIME_AND_EVIL) as INVOLVED_IN_CRIME_AND_EVIL,
        SUM(CRIMINAL_FILING)            as CRIMINAL_FILING

        FROM sand_disposal_report
        WHERE DELETED = '0'


 <!--       <if test="year != null and year != ''">
            AND SUBSTRING(FILING_TIME, 1, 4) = #{year}
        </if>
        <if test="month != null and month != ''">
            AND SUBSTRING(FILING_TIME, 6, 2) = #{month}
        </if>-->

<!--        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' ">-->
<!--            AND FILING_TIME BETWEEN #{startDate} AND #{endDate}-->
<!--        </if>-->
<!--        <if test="(startDate == null or startDate == '') and (endDate == null or endDate == '')">-->
<!--            AND LEFT(FILING_TIME, 4) = LEFT(#{startDate},4)-->
<!--        </if>-->



        GROUP BY SMM_SAND_DISPOSAL_REPORT_ID) c
        ON
        a.ID = c.SMM_SAND_DISPOSAL_REPORT_ID

        LEFT JOIN
        (SELECT SMM_SAND_DISPOSAL_REPORT_ID, COUNT(ADMINISTRATIVE_CASE) as ADMINISTRATIVE_CASE
        FROM sand_disposal_report
        WHERE ADMINISTRATIVE_CASE = '1' AND DELETED = '0'


 <!--       <if test="year != null and year != ''">
            AND SUBSTRING(FILING_TIME, 1, 4) = #{year}
        </if>
        <if test="month != null and month != ''">
            AND SUBSTRING(FILING_TIME, 6, 2) = #{month}
        </if>-->
<!--        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' ">-->
<!--            AND FILING_TIME BETWEEN #{startDate} AND #{endDate}-->
<!--        </if>-->
<!--        <if test="(startDate == null or startDate == '') and (endDate == null or endDate == '')">-->
<!--            AND LEFT(FILING_TIME, 4) = LEFT(#{startDate},4)-->
<!--        </if>-->


        GROUP BY SMM_SAND_DISPOSAL_REPORT_ID) d
        ON
        a.ID = d.SMM_SAND_DISPOSAL_REPORT_ID

        LEFT JOIN
        (SELECT SMM_SAND_DISPOSAL_REPORT_ID, COUNT(TRANSFERRED_TO_POLICE) as TRANSFERRED_TO_POLICE
        FROM sand_disposal_report
        WHERE TRANSFERRED_TO_POLICE = '1' AND DELETED = '0'


 <!--       <if test="year != null and year != ''">
            AND SUBSTRING(FILING_TIME, 1, 4) = #{year}
        </if>
        <if test="month != null and month != ''">
            AND SUBSTRING(FILING_TIME, 6, 2) = #{month}
        </if>-->
<!--        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' ">-->
<!--            AND FILING_TIME BETWEEN #{startDate} AND #{endDate}-->
<!--        </if>-->
        <if test="(startDate == null or startDate == '') and (endDate == null or endDate == '')">
            AND LEFT(FILING_TIME, 4) = LEFT(#{startDate},4)
        </if>


        GROUP BY SMM_SAND_DISPOSAL_REPORT_ID) e
        ON
        a.ID = e.SMM_SAND_DISPOSAL_REPORT_ID

        LEFT JOIN
        (SELECT SMM_SAND_DISPOSAL_REPORT_ID, SUM(SUSPECTS_COUNT) as ADMINISTRATIVE_CASE_PERSON
        FROM sand_disposal_report
        WHERE ADMINISTRATIVE_CASE = '1' AND DELETED = '0'


  <!--      <if test="year != null and year != ''">
            AND SUBSTRING(FILING_TIME, 1, 4) = #{year}
        </if>
        <if test="month != null and month != ''">
            AND SUBSTRING(FILING_TIME, 6, 2) = #{month}
        </if>-->
<!--        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' ">-->
<!--            AND FILING_TIME BETWEEN #{startDate} AND #{endDate}-->
<!--        </if>-->
<!--        <if test="(startDate == null or startDate == '') and (endDate == null or endDate == '')">-->
<!--            AND LEFT(FILING_TIME, 4) = LEFT(#{startDate},4)-->
<!--        </if>-->


        GROUP BY SMM_SAND_DISPOSAL_REPORT_ID) f
        ON
        a.ID = f.SMM_SAND_DISPOSAL_REPORT_ID

        LEFT JOIN
        (SELECT SMM_SAND_DISPOSAL_REPORT_ID, SUM(SUSPECTS_COUNT) as TRANSFERRED_TO_POLICE_PERSON
        FROM sand_disposal_report
        WHERE TRANSFERRED_TO_POLICE = '1' AND DELETED = '0'


  <!--      <if test="year != null and year != ''">
            AND SUBSTRING(FILING_TIME, 1, 4) = #{year}
        </if>
        <if test="month != null and month != ''">
            AND SUBSTRING(FILING_TIME, 6, 2) = #{month}
        </if>-->
<!--        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' ">-->
<!--            AND FILING_TIME BETWEEN #{startDate} AND #{endDate}-->
<!--        </if>-->
<!--        <if test="(startDate == null or startDate == '') and (endDate == null or endDate == '')">-->
<!--            AND LEFT(FILING_TIME, 4) = LEFT(#{startDate},4)-->
<!--        </if>-->



        GROUP BY SMM_SAND_DISPOSAL_REPORT_ID) g
        ON
        a.ID = g.SMM_SAND_DISPOSAL_REPORT_ID

        WHERE a.DELETED = '0' AND a.STATUS = '1'
        /*AND a.ID IN (
        SELECT DISTINCT SMM_SAND_DISPOSAL_REPORT_ID
        FROM sand_disposal_report
        WHERE DELETED = '0'*/


  <!--      <if test="year != null and year != ''">
            AND SUBSTRING(FILING_TIME, 1, 4) = #{year}
        </if>
        <if test="month != null and month != ''">
            AND SUBSTRING(FILING_TIME, 6, 2) = #{month}
        </if>-->
        <if test="startDate != null and startDate != '' and endDate != null and endDate != '' ">
            AND FILING_TIME_ALL BETWEEN #{startDate} AND #{endDate}
        </if>
<!--        <if test="(startDate == null or startDate == '') and (endDate == null or endDate == '')">-->
<!--            AND LEFT(FILING_TIME_ALL, 4) = LEFT(#{startDate},4)-->
<!--        </if>-->



#         )

        GROUP BY b.AREA_NAME, b.AREA_CODE
    </select>
    <select id="getAreaNameByDeptCode" resultType="java.lang.String">
        SELECT AREA_NAME
        FROM sys_area
        WHERE AREA_CODE LIKE(
            SELECT AREA_CODE FROM `sys_dept`
            WHERE DEPT_CODE = #{deptCode})
    </select>
    <select id="getAreaNameByAreaCode" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT AREA_NAME FROM sys_area WHERE AREA_CODE = #{areaCode}
    </select>
    <select id="selectPageListDC" resultType="com.ybkj.smm.modules.statement.entity.StatementDo">
        SELECT a.ID,
        a.ISSUE_TYPE,
        a.RESPONSIBLE_PERSON,
        a.RIVER_NAME,
        a.VILLAGE,
        CASE WHEN a.HAS_SAND_PIT = 1 THEN '是'
        WHEN a.HAS_SAND_PIT = 0 THEN '否'
        ELSE '未知' END AS HAS_SAND_PIT,
        a.ILLEGAL_EXTRACTION_VOLUME,
        CASE WHEN a.SOURCE_CLUES = 1 THEN '日常巡查'
        WHEN a.SOURCE_CLUES = 2 THEN '群众举报'
        WHEN a.SOURCE_CLUES = 3 THEN '其它部门移交'
        ELSE '未知' END AS SOURCE_CLUES,
        CASE WHEN a.ADMINISTRATIVE_CASE = 1 THEN '是'
        WHEN a.ADMINISTRATIVE_CASE = 0 THEN '否'
        ELSE '未知' END AS ADMINISTRATIVE_CASE,
        CASE WHEN a.CASE_CLOSED = 1 THEN '是'
        WHEN a.CASE_CLOSED = 0 THEN '否'
        ELSE '未知' END AS CASE_CLOSED,
        CASE WHEN a.TRANSFERRED_TO_POLICE = 1 THEN '是'
        WHEN a.TRANSFERRED_TO_POLICE = 0 THEN '否'
        ELSE '未知' END AS TRANSFERRED_TO_POLICE,
        CASE WHEN a.INVOLVED_IN_ORGANIZED_CRIME = 1 THEN '是'
        WHEN a.INVOLVED_IN_ORGANIZED_CRIME = 0 THEN '否'
        ELSE '未知' END AS INVOLVED_IN_ORGANIZED_CRIME,
        CASE WHEN a.REMEDIATION_MEASURES_DEFINED = 1 THEN '是'
        WHEN a.REMEDIATION_MEASURES_DEFINED = 0 THEN '否'
        ELSE '未知' END AS REMEDIATION_MEASURES_DEFINED,
        a.REMEDIATION_STATUS,
        a.NOTES,
        a.LONGITUDE,
        a.LATITUDE,
        a.SAND_PIT_COUNT,
        a.SAND_PIT_AREA,
        a.SAND_PIT_DEPTH,
        a.SUSPECTS_COUNT,
        a.FINE_AMOUNT,
        a.ADMINISTRATIVE_CASE_DETAILS,
        a.RESPONSIBLE_UNIT,
        a.CHARGE_PERSON,
        a.COMPLETION_DEADLINE,
        CASE WHEN a.RECTIFICATION_COMPLETED = 1 THEN '是'
        WHEN a.RECTIFICATION_COMPLETED = 0 THEN '否'
        ELSE '未知' END AS RECTIFICATION_COMPLETED,
        CASE WHEN a.PERSONNEL_RESPONSIBILITY = 1 THEN '是'
        WHEN a.PERSONNEL_RESPONSIBILITY = 0 THEN '否'
        ELSE '未知' END AS PERSONNEL_RESPONSIBILITY,
        a.CREATE_TIME,
        a.UPDATE_TIME,
        a.CREATE_USER_ID,
        a.UPDATE_USER_ID,
        a.AREA_NAME,
        a.AREA_CODE,
        a.NEW_MONTH,
        a.FLOW_NO,
        a.JOINT_LAW_ENFORCEMENT,
        a.WATER_ENFORCEMENT_PERSON,
        a.POLICE_PERSON,
        a.OTHER_PERSON,
        a.ILLEGAL_SAND_MINING,
        a.CRIMINAL_FILING,
        a.DEPT_ID,
        a.INVOLVED_IN_CRIME_AND_EVIL,
        a.SMM_SAND_DISPOSAL_REPORT_ID,
        a.DELETED,
        a.TOWNSHIP_PERSON,
        a.REGULATION_MEASURE,
        a.WHETHER_COMPENSATION,
        a.NOT_CAUSE,
        a.LIABILITY_NUMBER_PERSONS,
        CASE WHEN a.LIABILITY = 1 THEN '是'
        WHEN a.LIABILITY = 0 THEN '否'
        ELSE '未知' END AS LIABILITY,
        a.FILING_TIME,
        a.UPLOAD_FILE_ID,
        a.CLOSING_TIME,
        a.CLOSING_INFORMATION,
        a.LONGITUDE_TYPE,
        a.CONFISCATION_ILLEGAL_GAINS
        FROM sand_disposal_report a LEFT JOIN smm_sand_disposal_report b ON a.SMM_SAND_DISPOSAL_REPORT_ID = b.ID
        <where>
            <if test="newMonth != null and newMonth != ''">
                AND a.NEW_MONTH = #{newMonth}
            </if>
            <if test="areaName != null and areaName != ''">
                AND a.AREA_NAME =#{areaName}
            </if>
            <if test="riverName != null and riverName != ''">
                AND a.RIVER_NAME =#{riverName}
            </if>
            <if test="issueType != null and issueType != ''">
                AND a.ISSUE_TYPE =#{issueType}
            </if>
            <!--  <if test="year != null and year != ''">
                AND SUBSTRING(a.FILING_TIME, 1, 4) = #{year}
            </if>
            <if test="month != null and month != ''">
                AND SUBSTRING(a.FILING_TIME, 6, 2) = #{month}
            </if> -->
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                AND a.FILING_TIME BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="areaCode != null and areaCode != ''">
                AND a.AREA_CODE like CONCAT(#{areaCode} , '%')
            </if>
            <if test="status != null and status != ''">
                AND b.STATUS = #{status}
            </if>
            AND b.STATUS != '3'
            AND a.DEPT_ID in (select DEPT_ID from sys_dept where DEL_FLAG=0 and DEPT_CODE like concat(#{deptCode},'%'))
            AND a.DELETED = '0' AND b.DELETED = '0'
        </where>
        ORDER BY a.FILING_TIME,a.DEPT_ID DESC
    </select>
    <select id="getHistoryData" resultType="com.ybkj.smm.modules.statement.entity.StatementDo">
        SELECT
             *
        FROM
            smm_sand_disposal_report
        WHERE
            FILING_TIME_ALL = #{filingTimeAll} AND AREA_CODE = #{areaCode} AND DELETED = '0'
    </select>

</mapper>
