# Tomcat
server:
  undertow:
    io-threads: 8 #IO线程数，默认设置为CPU核心数
    worker-threads: 64 # 工作线程数，默认设置为io-threads * 8。如果你的应用程序有很多同步阻塞操作，可以适当增加这个值
  port: 8080
  servlet:
    context-path: /
    # 启用ajp连接方式，用于appche转发。
    #ajp:
    #port: 9080
    #redirect-port: 9443
    # tomcat8.5.51 必须配置ajp链接的密钥的本机ip，防止ajp漏洞攻击
    #secret: ybtest
    #address: 127.0.0.1

spring:
  redis:
    database: 1
    host: 127.0.0.1
    port: 6379
#    password: 123456   # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      first:  #数据源1
        url: jdbc:mysql://*************:3306/sandmining_dev?allowMultiQueries=true&rewriteBatchedStatements=true&useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai
        username: root
        password: hbxd@2024
        initial-size: 10
        max-active: 50
        min-idle: 10
        max-wait: 60000
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
      second:  #数据源2
        url: jdbc:mysql://*************:3306/sandmining_dev?allowMultiQueries=true&rewriteBatchedStatements=true&useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai
        username: root
        password: hbxd@2024
        initial-size: 10
        max-active: 50
        min-idle: 10
        max-wait: 60000
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60
      min-evictable-idle-time-millis: 300
      #validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      time-between-log-stats-millis: 3600000 # 1小时输出一次统计日志
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: 123456
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
          enabled: true
        wall:
          config:
            multi-statement-allow: true
#通过设置日志级别，控制台打印sql语句
logging:
  level:
    com.sloth: debug
    com.ybkj: debug
sloth:
  uploadFile:
    path: D:\uploadFile\
  swagger:
    enable: true
    title: 猿本科技
    description: api接口文档
    service-url: http://127.0.0.1
    version: 2.1.0
  mqtt:
    enable: true
    port: 1883
    proxy: false
  sms:
    initPuller: false

minio:
  endpoint: http://127.0.0.1:9000
  accessKey: admin
  secretKey: 123456




