<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybkj.smm.modules.rlc.mapper.RlcProjectMapper">

    <resultMap type="RlcProject" id="RlcProjectResult">
        <result property="id"    column="id"    />
        <result property="licenceId"    column="licence_id" />
        <result property="licCode"    column="lic_code"  />
        <result property="licDate"    column="lic_date"  />
        <result property="licDept"    column="lic_dept"  />
        <result property="projName"   column="proj_name" />
        <result property="geojson"    column="geojson" />
        <result property="type"    column="type"    />
        <result property="subType"    column="sub_type"  />
        <result property="mainType"    column="main_type"  />
        <result property="rvName"    column="rv_name"  />
        <result property="rvCode"    column="rv_code"  />
        <result property="fsdaName"    column="fsda_name"  />
        <result property="fsdaCode"    column="fsda_code"  />
        <result property="basinName"    column="basin_name"  />
        <result property="basinCode"    column="basin_code"  />
        <result property="adminName"    column="admin_name"  />
        <result property="adminCode"    column="admin_code"  />
        <result property="crossLoc"    column="cross_loc"    />
        <result property="dfcSta"    column="dfc_sta"    />
        <result property="desFlow"    column="des_flow"    />
        <result property="desLevel"    column="des_level"    />
        <result property="coords"    column="coords"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptIdLic"    column="dept_id_lic"    />

    </resultMap>

    <select id="selectRlcProjectList" parameterType="RlcProject" resultMap="RlcProjectResult">
        select aa.id, aa.licence_id, aa.geojson, aa.type, aa.sub_type, aa.main_type, aa.rv_name, aa.basin_name, aa.admin_name,
        aa.cross_loc, aa.dept_id,
        bb.lic_code, bb.lic_date, bb.lic_dept, bb.proj_name, bb.dept_id AS dept_id_lic
        from
        (
        select id, licence_id, geojson, 'park' as type, sub_type, main_type, rv_name, basin_name, admin_name, cross_loc, dept_id
        from rlc_proj_park
        where 1=1<if test="licenceId != null "> and licence_id = #{licenceId}</if>
        <if test="deptId != null ">and dept_id LIKE CONCAT( #{deptId}, '%')</if>
        union all
        select id, licence_id, geojson, 'wire' as type, sub_type, main_type, rv_name, basin_name, admin_name, cross_loc, dept_id
        from
        rlc_proj_wire
        where 1=1
        <if test="licenceId != null ">and licence_id = #{licenceId}</if>
        <if test="deptId != null ">and dept_id LIKE CONCAT( #{deptId}, '%')</if>
        union all
        select id, licence_id, geojson, 'pipe' as type, sub_type, main_type, rv_name, basin_name, admin_name, cross_loc, dept_id
        from
        rlc_proj_pipe
        where 1=1
        <if test="licenceId != null ">and licence_id = #{licenceId}</if>
        <if test="deptId != null ">and dept_id LIKE CONCAT( #{deptId}, '%')</if>
        union all
        select id, licence_id, geojson, 'road' as type, sub_type, main_type, rv_name, basin_name, admin_name, cross_loc, dept_id
        from
        rlc_proj_road
        where 1=1
        <if test="licenceId != null ">and licence_id = #{licenceId}</if>
        <if test="deptId != null ">and dept_id LIKE CONCAT( #{deptId}, '%')</if>
        ) as aa LEFT JOIN rlc_licence as bb
        on aa.licence_id = bb.id
    </select>

    <select id="getProjectListForInspection" parameterType="RlcProject" resultMap="RlcProjectResult">
        SELECT
        aa.id, aa.licence_id, aa.geojson, aa.type, aa.sub_type,
        aa.main_type, aa.rv_name, aa.basin_name, aa.admin_name,
        aa.cross_loc, aa.dept_id,
        bb.lic_code, bb.lic_date, bb.lic_dept, bb.proj_name,
        cc.name  -- 新增的部门名称字段
        FROM
        (
        SELECT id, licence_id, geojson, 'park' AS type, sub_type, main_type,
        rv_name, basin_name, admin_name, cross_loc, dept_id,
        create_time, update_time
        FROM rlc_proj_park
        UNION ALL
        SELECT id, licence_id, geojson, 'wire' AS type, sub_type, main_type,
        rv_name, basin_name, admin_name, cross_loc, dept_id,
        create_time, update_time
        FROM rlc_proj_wire
        UNION ALL
        SELECT id, licence_id, geojson, 'pipe' AS type, sub_type, main_type,
        rv_name, basin_name, admin_name, cross_loc, dept_id,
        create_time, update_time
        FROM rlc_proj_pipe
        UNION ALL
        SELECT id, licence_id, geojson, 'road' AS type, sub_type, main_type,
        rv_name, basin_name, admin_name, cross_loc, dept_id,
        create_time, update_time
        FROM rlc_proj_road
        ) AS aa
        LEFT JOIN rlc_licence AS bb ON aa.licence_id = bb.id
        -- 新增关联sys_dept表
        LEFT JOIN sys_dept AS cc ON aa.dept_id = cc.dept_id
        <where>
            bb.id IS NOT NULL
            <if test="deptId != null "> AND CONCAT(',', aa.dept_id) LIKE CONCAT('%,', #{deptId}, '%')</if>
            <if test="type != null and type != ''"> AND aa.type = #{type}</if>
            <if test="rvName != null and rvName != ''"> AND aa.rv_name LIKE CONCAT('%', #{rvName}, '%')</if>
            <if test="licCode != null and licCode != ''"> AND bb.lic_code LIKE CONCAT('%', #{licCode}, '%')</if>
            <if test="licDept != null and licDept != ''"> AND bb.lic_dept LIKE CONCAT('%', #{licDept}, '%')</if>
            <if test="projName != null and projName != ''"> AND bb.proj_name LIKE CONCAT('%', #{projName}, '%')</if>
            <if test="basinName != null  and basinName != ''"> AND aa.basin_name LIKE CONCAT('%', LEFT(#{basinName},2), '%')</if>
            <if test="licDateBegin != null "> AND bb.lic_date &gt;= #{licDateBegin} AND bb.lic_date &lt;= #{licDateEnd}</if>
        </where>
        ORDER BY aa.create_time DESC, aa.update_time DESC
    </select>

    <select id="getRoadInfoWithLicence" parameterType="String" resultType="java.util.Map">
        SELECT
            aa.id,
            aa.lic_code,
            aa.lic_date,
            aa.lic_dept,
            aa.lic_filename,
            aa.proj_name,
            '公路铁路' AS proj_type,
            aa.con_unit,
            aa.con_unit_person,
            aa.con_unit_tel,
            aa.eval_unit,
            aa.eval_unit_person,
            aa.eval_unit_tel,
            bb.sub_type,
            bb.main_type,
            bb.rv_name,
            bb.rv_code,
            bb.basin_name,
            bb.basin_code,
            bb.admin_name,
            bb.admin_code,
            bb.cross_loc,
            bb.dfc_sta,
            bb.des_flow,
            bb.des_level,
            bb.coords,
            bb.a_lng,
            bb.a_lat,
            bb.b_lng,
            bb.b_lat,
            bb.cross_angle,
            bb.cross_len,
            bb.min_bottom_elev,
            bb.min_bptop_elev,
            bb.ldike_top_headroom,
            bb.rdike_top_headroom,
            bb.fsda_rbed_len,
            bb.fsda_rbed_sta_lng,
            bb.fsda_rbed_sta_lat,
            bb.fsda_rbed_end_lng,
            bb.fsda_rbed_end_lat,
            bb.fsda_rbed_avg_elev,
            (SELECT GROUP_CONCAT(name) FROM sys_dept AS ss WHERE CONCAT(',', bb.dept_id, ',') LIKE CONCAT('%,', ss.division_code, ',%')) AS dept_name,
            aa.remark || bb.remark AS remak
            FROM (
                SELECT * FROM rlc_licence
            ) AS aa RIGHT JOIN (
                SELECT * FROM rlc_proj_road WHERE CONCAT(',', dept_id, ',') LIKE CONCAT('%,', #{id}, '%')
            ) AS bb
            ON aa.id = bb.licence_id
            WHERE aa.id IS NOT NULL
            ORDER BY aa.id;
    </select>

    <select id="getPipeInfoWithLicence" parameterType="String" resultType="java.util.Map">
        SELECT
            aa.id,
            aa.lic_code,
            aa.lic_date,
            aa.lic_dept,
            aa.lic_filename,
            aa.proj_name,
            '管道' AS proj_type,
            aa.con_unit,
            aa.con_unit_person,
            aa.con_unit_tel,
            aa.eval_unit,
            aa.eval_unit_person,
            aa.eval_unit_tel,
            bb.sub_type,
            bb.main_type,
            bb.rv_name,
            bb.rv_code,
            bb.basin_name,
            bb.basin_code,
            bb.admin_name,
            bb.admin_code,
            bb.cross_loc,
            bb.dfc_sta,
            bb.des_flow,
            bb.des_level,
            bb.coords,
            bb.a_lng,
            bb.a_lat,
            bb.b_lng,
            bb.b_lat,
            bb.cross_type,
            bb.cross_angle,
            bb.cross_len,
            bb.ldft_vd,
            bb.rdft_vd,
            bb.lbs_vd,
            bb.rbs_vd,
            bb.left_pt_mindepth,
            bb.right_pt_mindepth,
            bb.mt_pt_mindepth,
            bb.bl_pt_mindepth,
            (SELECT GROUP_CONCAT(name) FROM sys_dept AS ss WHERE CONCAT(',', bb.dept_id, ',') LIKE CONCAT('%,', ss.dept_id, ',%')) AS dept_name,
            aa.remark || bb.remark AS remak
            FROM (
                SELECT * FROM rlc_licence
            ) AS aa RIGHT JOIN (
                SELECT * FROM rlc_proj_pipe WHERE CONCAT(',', dept_id, ',') LIKE CONCAT('%,', #{id}, '%')
            ) AS bb
            ON aa.id = bb.licence_id
            WHERE aa.id IS NOT NULL
            ORDER BY aa.id;
    </select>

    <select id="getWireInfoWithLicence" parameterType="String" resultType="java.util.Map">
        SELECT
            aa.id,
            aa.lic_code,
            aa.lic_date,
            aa.lic_dept,
            aa.lic_filename,
            aa.proj_name,
            '线路' AS proj_type,
            aa.con_unit,
            aa.con_unit_person,
            aa.con_unit_tel,
            aa.eval_unit,
            aa.eval_unit_person,
            aa.eval_unit_tel,
            bb.sub_type,
            bb.main_type,
            bb.rv_name,
            bb.rv_code,
            bb.basin_name,
            bb.basin_code,
            bb.admin_name,
            bb.admin_code,
            bb.cross_loc,
            bb.dfc_sta,
            bb.des_flow,
            bb.des_level,
            bb.coords,
            bb.a_lng,
            bb.a_lat,
            bb.b_lng,
            bb.b_lat,
            bb.cross_angle,
            bb.main_t,
            bb.rel_pos,
            bb.foot_left_elev,
            bb.foot_right_elev,
            bb.dxhcjk_left,
            bb.dxhcjk_right,
            bb.dxhcjk_level,
            bb.dxhcjk_ice,
            (SELECT GROUP_CONCAT(name) FROM sys_dept AS ss WHERE CONCAT(',', bb.dept_id, ',') LIKE CONCAT('%,', ss.dept_id, ',%')) AS dept_name,
            aa.remark || bb.remark AS remak
            FROM (
                SELECT * FROM rlc_licence
            ) AS aa RIGHT JOIN (
                SELECT * FROM rlc_proj_wire WHERE CONCAT(',', dept_id, ',') LIKE CONCAT('%,', #{id}, '%')
            ) AS bb
            ON aa.id = bb.licence_id
            WHERE aa.id IS NOT NULL
            ORDER BY aa.id;
    </select>

    <select id="getParkInfoWithLicence" parameterType="String" resultType="java.util.Map">
        SELECT
            aa.id,
            aa.lic_code,
            aa.lic_date,
            aa.lic_dept,
            aa.lic_filename,
            aa.proj_name,
            '公园' AS proj_type,
            aa.con_unit,
            aa.con_unit_person,
            aa.con_unit_tel,
            aa.eval_unit,
            aa.eval_unit_person,
            aa.eval_unit_tel,
            bb.sub_type,
            bb.main_type,
            bb.rv_name,
            bb.rv_code,
            bb.basin_name,
            bb.basin_code,
            bb.admin_name,
            bb.admin_code,
            bb.cross_loc,
            bb.dfc_sta,
            bb.des_flow,
            bb.des_level,
            bb.coords,
            bb.bl_avg_elev,
            bb.move_sf_count,
            bb.plants_type,
            bb.wharf_bottom_elev,
            bb.is_occupy_fl_section,
            (SELECT GROUP_CONCAT(name) FROM sys_dept AS ss WHERE CONCAT(',', bb.dept_id, ',') LIKE CONCAT('%,', ss.dept_id, ',%')) AS dept_name,
            aa.remark || bb.remark AS remak
            FROM (
                SELECT * FROM rlc_licence
            ) AS aa RIGHT JOIN (
                SELECT * FROM rlc_proj_park WHERE CONCAT(',', dept_id, ',') LIKE CONCAT('%,', #{id}, '%')
            ) AS bb
            ON aa.id = bb.licence_id
            WHERE aa.id IS NOT NULL
            ORDER BY aa.id;
    </select>

</mapper>
