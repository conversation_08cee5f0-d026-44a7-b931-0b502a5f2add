<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.sms.dao.TemplateDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sloth.modules.sms.entity.Template" id="templateMap">
        <result property="id" column="ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="name" column="NAME"/>
        <result property="type" column="TYPE"/>
        <result property="code" column="CODE"/>
        <result property="content" column="CONTENT"/>
        <result property="remark" column="REMARK"/>
    </resultMap>


</mapper>