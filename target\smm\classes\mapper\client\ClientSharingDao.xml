<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.client.dao.ClientSharingDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.client.entity.ClientSharing" id="clientSharingMap">
        <result property="id" column="ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="clientId" column="CLIENT_ID"/>
        <result property="projectId" column="PROJECT_ID"/>
        <result property="sharingProjectId" column="SHARING_PROJECT_ID"/>
        <result property="sharingProjectName" column="SHARING_PROJECT_NAME"/>
        <result property="sharingSectionName" column="SHARING_SECTION_NAME"/>
        <result property="sharingStationId" column="SHARING_STATION_ID"/>
        <result property="sharingStationName" column="SHARING_STATION_NAME"/>
    </resultMap>
    <select id="selectClientSharingPage" resultType="com.ybkj.smm.modules.client.entity.ClientSharing">
        select
            clientSharing.ID,
            clientSharing.CREATE_TIME,
            clientSharing.CLIENT_ID,
            clientSharing.PROJECT_ID,
            clientSharing.SHARING_PROJECT_ID,
            clientSharing.SHARING_PROJECT_NAME,
            clientSharing.SHARING_SECTION_NAME,
            clientSharing.SHARING_STATION_ID,
            clientSharing.SHARING_STATION_NAME,
            client.PROJECT_NAME projectName,
            client.SECTION_NAME sectionName,
            client.STATION_NAME stationName
        from smm_client_client_sharing clientSharing
        left join smm_client_client client ON client.ID = clientSharing.CLIENT_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>
