<#import "/macro/macro.ftl" as my />
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>${websiteName!''}</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<meta name="apple-mobile-web-app-status-bar-style" content="black">
	<meta name="apple-mobile-web-app-capable" content="yes">
	<meta name="format-detection" content="telephone=no">
	<link rel="shortcut icon" href="${request.contextPath}/statics/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" type="text/css" href="${request.contextPath}/statics/plugins/layui/css/layui.css">
	<link rel="stylesheet" type="text/css" href="${request.contextPath}/statics/css/font-awesome.min.css">
	<link rel="stylesheet" type="text/css" href="${request.contextPath}/statics/css/index1.css">
	<link rel="stylesheet" type="text/css" href="${request.contextPath}/statics/css/main.css">
</head>
<body>
<div class="layui-layout layui-layout-admin" id="layui_layout" v-cloak>
	<!-- 顶部区域 -->
	<div class="layui-header header header-demo">
		<div class="layui-main">
		    <!-- logo区域 -->
			<div class="admin-logo-box">
				<div id="logo-site-name" style="margin: 0 10px; line-height: 70px;font-size: 24px;font-weight:bold;"><@my.config  configCode="webSite.name"/></div>
				<a class="logo" href="javascript:;"></a>
			</div>
			<div id="header-right">
			<div class="larry-side-menu">
				<i class="fa fa-bars" aria-hidden="true"></i>
			</div>
			<div class="layui-larry-menu">
				<ul class="layui-nav">
					<li><div style="font-family: Helvetica Neue,Helvetica,Arial,sans-serif;margin-top:8px;left: 10px;top: 8px;font-size: 14px; color:#fff;height: 50px;line-height: 50px;text-align: center;">欢迎 {{user.showName}}</div></li>
				</ul>
			</div>
			</div>
            <!-- 顶级菜单区域 -->
            <!-- <div class="layui-larry-menu">
                 <ul class="layui-nav clearfix">
                       <li class="layui-nav-item layui-this">
                 	   	   <a href="javascirpt:;"><i class="iconfont icon-wangzhanguanli"></i>内容管理</a>
                 	   </li>
                 	   <li class="layui-nav-item">
                 	   	   <a href="javascirpt:;"><i class="iconfont icon-weixin3"></i>微信公众</a>
                 	   </li>
                 	   <li class="layui-nav-item">
                 	   	   <a href="javascirpt:;"><i class="iconfont icon-ht_expand"></i>扩展模块</a>
                 	   </li>
                 </ul>
            </div> -->
            <!-- 右侧导航 -->
            <ul class="layui-nav larry-header-item">
				<#--
				<li class="layui-nav-item"><a href="javascript:;" @click="donate"><i class="fa fa-jpy"></i> &nbsp;捐赠作者</a></li>
				<li class="layui-nav-item"><a href="http://www.yuanbenkeji.com" target="_blank"><i class="fa fa-home"></i> &nbsp;猿本科技</a></li>
				-->
				<li class="layui-nav-item"><a href="javascript:;" @click="updatePassword"><i class="fa fa-lock"></i> &nbsp;修改密码</a></li>
       			<li class="layui-nav-item"><a href="logout"><i class="fa fa-sign-out"></i> &nbsp;退出系统</a></li>
            </ul>
		</div>
	</div>
	<!-- 左侧侧边导航开始 -->
	<div class="layui-side layui-side-bg layui-larry-side" id="larry-side">
        <div class="layui-side-scroll" id="larry-nav-side" lay-filter="side">
		<!-- 左侧菜单 -->
		<ul class="layui-nav layui-nav-tree">
			<menu-item :item="item" v-for="item in menuList"></menu-item>
		</ul>
	    </div>
	</div>

	<!-- 左侧侧边导航结束 -->
	<!-- 右侧主体内容 -->
	<div class="layui-body" id="larry-body" style="bottom: 0;border-left: solid 2px #1AA094;">
		<div class="layui-tab layui-tab-card larry-tab-box" id="larry-tab" lay-filter="main-tab" lay-allowclose="true">
			<ul class="layui-tab-title">
				<li class="layui-this" id="admin-home"><i class="fa fa-home"></i><em>控制台</em></li>
			</ul>
			<div class="layui-tab-content" style="min-height: 150px; ">
				<div class="layui-tab-item layui-show">
					<iframe class="larry-iframe" data-id='0' src="main.html"></iframe>
				</div>
			</div>
		</div>


	</div>
	<!-- 底部区域 -->
	<div class="layui-footer layui-larry-foot" id="larry-footer">
		<div class="layui-mian">
			<div class="pull-right hidden-xs" style="margin-right: 10px;">
				Version <@my.config  configCode="webSite.version"/>
			</div>
			<@my.config  configCode="webSite.copyright"/>
		</div>
	</div>

	<!-- 修改密码 -->
	<div id="passwordLayer" style="display: none;">

		<form class="layui-form" action="" id="frm">
			<div class="layui-form-item">
		    	<label class="layui-form-label">用户名</label>
		    	<label class="layui-form-label laber-account">{{user.username}}</label>
			</div>
			<div class="layui-form-item">
		    	<label class="layui-form-label">原密码</label>
		    	<div class="layui-input-inline">
		    		<input id="password" type="password" v-model="password" placeholder="原密码" autocomplete="off" class="layui-input"  rules="[{notNull:true,message:'原密码不能为空'}]">
		    	</div>
			</div>
			<div class="layui-form-item">
		    	<label class="layui-form-label">新密码</label>
		    	<div class="layui-input-inline">
		    		<input v-if="pp=='false'" id="newPassword" type="password" v-model="newPassword" placeholder="新密码" autocomplete="off" class="layui-input" rules="[{notNull:true,message:'新密码不能为空'}]">
		    		<input v-else id="newPassword" type="password" v-model="newPassword" placeholder="新密码" autocomplete="off" class="layui-input" rules="[{notNull:true,message:'新密码不能为空'},{regExp:/(?=.*[0-9])(?=.*[a-zA-Z]).{8,30}/,message:'必须包含字母和数字，长度8-30'}]">
		    	</div>
			</div>
            <div class="layui-form-item">
                <label class="layui-form-label">确认新密码</label>
                <div class="layui-input-inline">
                    <input id="conPassword" type="password" v-model="conPassword" placeholder="新密码" autocomplete="off" class="layui-input" rules="[{notNull:true,message:'确认密码不能为空'},{equalWith:'newPassword',message:'两次新密码必须相同'},]">
                </div>
            </div>
		</form>
	</div>
</div>

<script type="text/javascript" src="${request.contextPath}/statics/libs/jquery.min.js"></script>
<script type="text/javascript" src="${request.contextPath}/statics/plugins/layui/layui.js"></script>
<script type="text/javascript" src="${request.contextPath}/statics/libs/vue.min.js"></script>
<script src="${request.contextPath}/statics/plugins/layer/layer.js"></script>
<script type="text/javascript" src="${request.contextPath}/statics/js/common.validate.js"></script>
<script type="text/javascript" src="${request.contextPath}/statics/js/index1.js?_${sloth.version()}"></script>
<script type="text/javascript">
    vm.pp='<@my.config  configCode="system.passwordPolicy"/>'
</script>
</body>
</html>
