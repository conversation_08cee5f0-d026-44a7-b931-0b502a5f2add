<#import "/macro/macro.ftl" as my />
<!DOCTYPE html>
<html>
<@my.head jqgrid=true component=true vueValidate=true elementUI=true ztree=true  ueditor=true>
    <title>项目/沙场信息表</title>
</@my.head>
<body>
<div id="app" v-cloak>
    <div v-show="showList">
        <div class="grid-btn form-inline">
            <div class="form-group">
                <a class="btn btn-default" @click="query"><i class="fa fa-search"></i>&nbsp;查询</a>
                <a class="btn btn-default" @click="queryAll"><i class="fa fa-refresh"></i>&nbsp;全部</a>
            </div>
			<#if shiro.hasPermission("project:project:save")>
            <div class="form-group">
            <a class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
            </div>
			</#if>
			<#if shiro.hasPermission("project:project:delete")>
            <div class="form-group">
            <a class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;批量删除</a>
            </div>
			</#if>
        </div>
        <a class="pull-right" @click="drawer=true">更多查询<i class="fa fa-plus"></i></a>
        <div class="clearfix"></div>
        <el-drawer
                :visible.sync="drawer"
                :with-header="false"
                direction="rtl">
            <div class="form-horizontal" style="width:100%;padding: 10px">
                <div class="form-group">
                </div>
                <div class="form-group">
                    <a class="btn btn-default" @click="query('drawer')"><i class="fa fa-search"></i>&nbsp;查询</a>
                    <a class="btn btn-default" @click="clearQ"><i class="fa fa-refresh"></i>&nbsp;清空</a>
                </div>
            </div>
        </el-drawer>
        <my-table url="${request.contextPath}/project/project/list" ref="projectTable" row-key="deptId">
            <el-table-column  label="id 主键，部门表ID" min-width="80" prop="deptId" sortable="custom" column-key="DEPT_ID"></el-table-column>
            <el-table-column  label="创建人id" min-width="80" prop="createUserId" sortable="custom" column-key="CREATE_USER_ID"></el-table-column>
            <el-table-column  label="修改人id" min-width="80" prop="updateUserId" sortable="custom" column-key="UPDATE_USER_ID"></el-table-column>
            <el-table-column  label="创建时间" min-width="80" prop="createTime" sortable="custom" column-key="CREATE_TIME"></el-table-column>
            <el-table-column  label="修改时间" min-width="80" prop="updateTime" sortable="custom" column-key="UPDATE_TIME"></el-table-column>
            <el-table-column  label="部门表CODE" min-width="80" prop="deptCode" sortable="custom" column-key="DEPT_CODE"></el-table-column>
            <el-table-column  label="项目名称" min-width="80" prop="name" sortable="custom" column-key="NAME"></el-table-column>
            <el-table-column  label="项目位置（区域名称）" min-width="80" prop="areaName" sortable="custom" column-key="AREA_NAME"></el-table-column>
            <el-table-column  label="区域编码" min-width="80" prop="areaCode" sortable="custom" column-key="AREA_CODE"></el-table-column>
            <el-table-column  label="项目类型;字典值：河道治理和采砂管控、弃沙综合利用" min-width="80" prop="type" sortable="custom" column-key="TYPE"></el-table-column>
            <el-table-column  label="监管部门ID,部门表的父ID" min-width="80" prop="parentId" sortable="custom" column-key="PARENT_ID"></el-table-column>
            <el-table-column  label="控制总量（万吨）" min-width="80" prop="totalYield" sortable="custom" column-key="TOTAL_YIELD"></el-table-column>
            <el-table-column  label="采砂许可证/弃砂审批文号" min-width="80" prop="licenseNo" sortable="custom" column-key="LICENSE_NO"></el-table-column>
            <el-table-column  label="采砂人（供砂人）" min-width="80" prop="leaderName" sortable="custom" column-key="LEADER_NAME"></el-table-column>
            <el-table-column  label="联系电话" min-width="80" prop="contact" sortable="custom" column-key="CONTACT"></el-table-column>
            <el-table-column  label="删除标记" min-width="80" prop="deleted" sortable="custom" column-key="DELETED"></el-table-column>
            <el-table-column label="操作" column-key="caozuo" min-width="80" fixed="right">
                <template slot-scope="scope">
                    <button @click="update(scope.row.id)" class="btn btn-xs btn-success" title="编辑" type="button"><i
                            class="fa fa-edit" aria-hidden="true"></i></button>
                    <button @click="delOne(scope.row.id)" class="btn btn-xs btn-success" title="删除" type="button"><i
                            class="fa fa-trash" aria-hidden="true"></i></button>
                </template>
            </el-table-column>
        </my-table>
    </div>

    <div v-show="!showList" class="panel panel-default">
        <div class="panel-heading">{{title}}</div>
        <form class="form-horizontal" id="frm">
														                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">创建人id</div>
                <div class="col-sm-9">
                    <my-input type="text" id="createUserId" v-model.trim="project.createUserId" placeholder="创建人id"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">修改人id</div>
                <div class="col-sm-9">
                    <my-input type="text" id="updateUserId" v-model.trim="project.updateUserId" placeholder="修改人id"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">创建时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="createTime" v-model.trim="project.createTime" placeholder="创建时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">修改时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="updateTime" v-model.trim="project.updateTime" placeholder="修改时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">部门表CODE</div>
                <div class="col-sm-9">
                    <my-input type="text" id="deptCode" v-model.trim="project.deptCode" placeholder="部门表CODE"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">项目名称</div>
                <div class="col-sm-9">
                    <my-input type="text" id="name" v-model.trim="project.name" placeholder="项目名称"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">项目位置（区域名称）</div>
                <div class="col-sm-9">
                    <my-input type="text" id="areaName" v-model.trim="project.areaName" placeholder="项目位置（区域名称）"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">区域编码</div>
                <div class="col-sm-9">
                    <my-input type="text" id="areaCode" v-model.trim="project.areaCode" placeholder="区域编码"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">项目类型;字典值：河道治理和采砂管控、弃沙综合利用</div>
                <div class="col-sm-9">
                    <my-input type="text" id="type" v-model.trim="project.type" placeholder="项目类型;字典值：河道治理和采砂管控、弃沙综合利用"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">监管部门ID,部门表的父ID</div>
                <div class="col-sm-9">
                    <my-input type="text" id="parentId" v-model.trim="project.parentId" placeholder="监管部门ID,部门表的父ID"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">控制总量（万吨）</div>
                <div class="col-sm-9">
                    <my-input type="number" id="totalYield" v-model.trim="project.totalYield" placeholder="控制总量（万吨）"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">采砂许可证/弃砂审批文号</div>
                <div class="col-sm-9">
                    <my-input type="text" id="licenseNo" v-model.trim="project.licenseNo" placeholder="采砂许可证/弃砂审批文号"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">采砂人（供砂人）</div>
                <div class="col-sm-9">
                    <my-input type="text" id="leaderName" v-model.trim="project.leaderName" placeholder="采砂人（供砂人）"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">联系电话</div>
                <div class="col-sm-9">
                    <my-input type="text" id="contact" v-model.trim="project.contact" placeholder="联系电话"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">删除标记</div>
                <div class="col-sm-9">
                    <my-input type="text" id="deleted" v-model.trim="project.deleted" placeholder="删除标记"></my-input>
                </div>
            </div>
							
            <div class="form-group center">
                <input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
                &nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
            </div>
        </form>
    </div>
</div>
<script src="${request.contextPath}/statics/js/modules/project/project.js?_${sloth.version()}"></script>
</body>
</html>
