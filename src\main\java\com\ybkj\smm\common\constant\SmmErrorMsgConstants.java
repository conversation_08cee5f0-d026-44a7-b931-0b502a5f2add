package com.ybkj.smm.common.constant;

import com.sloth.common.constant.ErrorMsgConstant;

public class SmmErrorMsgConstants extends ErrorMsgConstant {
    public static final String MOBILE_EXIST_ERROR_2001 = "手机号已存在";

    public static final String BILL_IMG_DOC_NAME= "订单号生成失败找不到该项目所在行政区划码";
    public static final String BILL_IMG_ERROR_2002 = "请上传现场照片";

    public static final String EXIST_NOT_BILLED_APPLY_2003 = "存在未开单的申请，不能重复申请";

    public static final String EXIST_NOT_COMPLETE_BILL_2004 = "存在未核销的运砂单，不能申请";

    public static final String EXIST_NOT_COMPLETE_BILL_2005 = "客户端存在摄像头，不可删除";

    public static final String EXIST_NOT_COMPLETE_BILL_2006 = "手机号已存在";

    public static final String BILL_COUNT_LIMIT_2007 = "今日开单数量已达上限，继续开单请联系主管部门进行授权";

    public static final String DRIVER_NOT_EXISTS_2008 = "请联系采砂人导入司机信息，再进行注册。";

    public static final String SIGN_IMAGE_ERROR_2009 = "请设置采砂人印章";

    public static final String SIGN_IMAGE_ERROR_2010 = "请设置主管部门印章";

    public static final String PROJECT_NOT_EXIST_2011 = "未找到项目信息";

    public static final String STATION_ID_NOT_EXIST_2012 = "磅站ID不正确";

    public static final String TEMP_DATA_TYPE_ERROR_2013 = "dataType取值错误";

    public static final String CODE_MOBILE_NOT_UPDATE_2014 = "验证码失效，请重新获取验证码";

    public static final String CODE_ERR_INPUT_2015 = "验证码错误请重新输入";

    public static final String SEND_MESSAGE_TIME_MORE_2016 = "短信发送时间过于频繁,请 {0} s后再发送";

    public static final String USER_NOT_EXISTS_2017 = "手机号未注册";

    public static final String MOBILE_IS_DEACTIVATE_2018 = "手机号已停用";

    public static final String SCAN_CONTENT_ERROR_2019 = "扫码内容错误";

    public static final String SCAN_AUTH_ERROR_2020 = "您不是该项目的开单员，不能进行此单据的开单操作";

    public static final String SCAN_AUTH_ERROR_2021 = "运砂人已注册,不能删除";

    public static final String SYSTEM_CANNOT_BE_DELETED_ERROR_2022 = "系统管理员不能删除";

    public static final String CURRENT_USER_CANNOT_BE_DELETED_ERROR_2023 = "当前用户不能删除";

    public static final String ACCOUNT_EXIST_NOT_BILLED_ERROR_2024 = "当前账号下存在未开单的运砂申请，不允许删除账号";

    public static final String EXIST_NOT_BILLED_APPLY_2025 = "车牌号[{0}]已由用户[{1}]提交运砂申请，不能重复申请";

    public static final String CAR_HISTORY_ERR_2026 = "车辆历史数据接口返回失败[{0}]";

    public static final String CAR_REAL_TIME_ERR_2027 = "车辆实时数据接口返回失败[{0}]";

    public static final String PROJECT_DELETED_VIDEO_ERROR_2028 = "删除的项目存在摄像头,不允许删除";

    public static final String SELECT_PROJECT_SET_ERROR_2029 = "请先在项目管理中进行项目配置";

    public static final String TIE_TA_GET_URL_ERROR_2030 = "获取铁塔视频路径失败:[{0}]";

    public static final String IMPORT_NOT_EXIT_2031 = "未获取到模板内有效内容";

    public static final String DECRYPT_ERROR_2032 = "请求参数无效";

    public static final String PROJECT_DELETED_STATION_ERROR_2033 = "删除的项目存在磅站,不允许删除";

    public static final String PROJECT_DELETED_DRIVER_ERROR_2034 = "删除的项目存运砂人,不允许删除";

    public static final String PROJECT_DELETED_APPLY_ERROR_2035 = "删除的项目存在运砂申请,不允许删除";

    public static final String USER_NAME_ERROR_2035 = "用户名不正确";

    public static final String YUN_RUN_ERROR_2036 = "云睿平台创建直播地址失败[{0}]";

    public static final String APPLY_NOT_FIND_2037 = "该车辆没有提交运砂申请或者申请的不是本项目";

    public static final String SIGN_IMAGE_FORMAT_ERROR_2038 = "签名图片必须是svg图片base64码";

    public static final String NOT_DEPT_AREA_ERROR_2039 = "请联系管理员,设置{0}对应的部门";

    public static final String WRONG_FILE_TYPE_2040 = "文件类型错误";

    public static final String APPLY_STATUS_ERROR_2041 = "本车辆已入场，请勿重复提交";

    public static final String APPLY_STATUS_ERROR_2042 = "操作失败：车牌未提交入场数据或已开单";

    public static final String APPLY_STATUS_ERROR_2043 = "操作失败：运砂申请状态错误，无法重新入场";

    public static final String BILL_TIME_ERROR_2044 = "预计到达时间小于当前时间无法开单，请司机改正运砂申请中的预计到达时间";

    public static final String SAND_VOLUME_INSUFFICIENT_2045 = "采砂量不足，请联系砂场管理员！";

}
