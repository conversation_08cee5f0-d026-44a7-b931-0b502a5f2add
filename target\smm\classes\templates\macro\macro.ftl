<#--宏指令引用方式
 ftl头部添加：<#import "/macro/macro.ftl" as my />
 标签使用示例
 <@my.head jqgrid=true ztree=true uploader=true ueditor=true layDate=true validate=true>
    <title>文章管理</title>
</@my.head>
<@my.view value="Y" pvalue="yesornot" />
<@my.checkBox pvalue="yesornot" />
<@my.checkBox pvalue="yesornot" value="Y" />
<@my.radio pvalue="yesornot" />
<@my.radio pvalue="yesornot" value="Y" />
<@my.area value="130125" />
<@my.area value="130125" showPath=true pathSplitor="→" />
-->
<#assign head = "com.sloth.common.freemarker.macro.HeadDirective"?new()>
<#assign view = "com.sloth.common.freemarker.macro.ViewDirective"?new()>
<#assign checkBox = "com.sloth.common.freemarker.macro.CheckBoxDirective"?new()>
<#assign select = "com.sloth.common.freemarker.macro.SelectDirective"?new()>
<#assign radio = "com.sloth.common.freemarker.macro.RadioDirective"?new()>
<#assign area = "com.sloth.common.freemarker.macro.AreaDirective"?new()>
<#assign strComp = "com.sloth.common.freemarker.macro.StrComparison"?new()>
<#assign config = "com.sloth.common.freemarker.macro.ConfigDirective"?new()>
<#assign dictJson = "com.sloth.common.freemarker.macro.DictJsonDirective"?new()>
<#assign dictJsonArray = "com.sloth.common.freemarker.macro.DictJsonArrayDirective"?new()>
