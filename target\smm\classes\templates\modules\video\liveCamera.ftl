<#import "/macro/macro.ftl" as my />
<!DOCTYPE html>
<html>
<@my.head jqgrid=true component=true vueValidate=true elementUI=true ztree=true  ueditor=true>
    <title>摄像头表</title>
</@my.head>
<body>
<div id="app" v-cloak>
    <div v-show="showList">
        <div class="grid-btn form-inline">
            <div class="form-group">
                <a class="btn btn-default" @click="query"><i class="fa fa-search"></i>&nbsp;查询</a>
                <a class="btn btn-default" @click="queryAll"><i class="fa fa-refresh"></i>&nbsp;全部</a>
            </div>
			<#if shiro.hasPermission("video:liveCamera:save")>
            <div class="form-group">
            <a class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
            </div>
			</#if>
			<#if shiro.hasPermission("video:liveCamera:delete")>
            <div class="form-group">
            <a class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;批量删除</a>
            </div>
			</#if>
        </div>
        <a class="pull-right" @click="drawer=true">更多查询<i class="fa fa-plus"></i></a>
        <div class="clearfix"></div>
        <el-drawer
                :visible.sync="drawer"
                :with-header="false"
                direction="rtl">
            <div class="form-horizontal" style="width:100%;padding: 10px">
                <div class="form-group">
                </div>
                <div class="form-group">
                    <a class="btn btn-default" @click="query('drawer')"><i class="fa fa-search"></i>&nbsp;查询</a>
                    <a class="btn btn-default" @click="clearQ"><i class="fa fa-refresh"></i>&nbsp;清空</a>
                </div>
            </div>
        </el-drawer>
        <my-table url="${request.contextPath}/video/liveCamera/list" ref="liveCameraTable" row-key="id">
            <el-table-column  label="id 主键" min-width="80" prop="id" sortable="custom" column-key="ID"></el-table-column>
            <el-table-column  label="创建人id" min-width="80" prop="createUserId" sortable="custom" column-key="CREATE_USER_ID"></el-table-column>
            <el-table-column  label="修改人id" min-width="80" prop="updateUserId" sortable="custom" column-key="UPDATE_USER_ID"></el-table-column>
            <el-table-column  label="创建时间" min-width="80" prop="createTime" sortable="custom" column-key="CREATE_TIME"></el-table-column>
            <el-table-column  label="修改时间" min-width="80" prop="updateTime" sortable="custom" column-key="UPDATE_TIME"></el-table-column>
            <el-table-column  label="项目ID" min-width="80" prop="deptId" sortable="custom" column-key="DEPT_ID"></el-table-column>
            <el-table-column  label="项目对应部门CODE" min-width="80" prop="deptCode" sortable="custom" column-key="DEPT_CODE"></el-table-column>
            <el-table-column  label="名称" min-width="80" prop="name" sortable="custom" column-key="NAME"></el-table-column>
            <el-table-column  label="经度" min-width="80" prop="longitude" sortable="custom" column-key="LONGITUDE"></el-table-column>
            <el-table-column  label="纬度" min-width="80" prop="latitude" sortable="custom" column-key="LATITUDE"></el-table-column>
            <el-table-column  label="接入平台;字典值： yingShi-萤石云" min-width="80" prop="platform" sortable="custom" column-key="PLATFORM"></el-table-column>
            <el-table-column  label="接入平台序列号" min-width="80" prop="platformSn" sortable="custom" column-key="PLATFORM_SN"></el-table-column>
            <el-table-column  label="设备通道号" min-width="80" prop="channel" sortable="custom" column-key="CHANNEL"></el-table-column>
            <el-table-column  label="视频平台appKey" min-width="80" prop="appKey" sortable="custom" column-key="APP_KEY"></el-table-column>
            <el-table-column  label="视频平台秘钥" min-width="80" prop="appSecret" sortable="custom" column-key="APP_SECRET"></el-table-column>
            <el-table-column label="操作" column-key="caozuo" min-width="80" fixed="right">
                <template slot-scope="scope">
                    <button @click="update(scope.row.id)" class="btn btn-xs btn-success" title="编辑" type="button"><i
                            class="fa fa-edit" aria-hidden="true"></i></button>
                    <button @click="delOne(scope.row.id)" class="btn btn-xs btn-success" title="删除" type="button"><i
                            class="fa fa-trash" aria-hidden="true"></i></button>
                </template>
            </el-table-column>
        </my-table>
    </div>

    <div v-show="!showList" class="panel panel-default">
        <div class="panel-heading">{{title}}</div>
        <form class="form-horizontal" id="frm">
														                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">创建人id</div>
                <div class="col-sm-9">
                    <my-input type="text" id="createUserId" v-model.trim="liveCamera.createUserId" placeholder="创建人id"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">修改人id</div>
                <div class="col-sm-9">
                    <my-input type="text" id="updateUserId" v-model.trim="liveCamera.updateUserId" placeholder="修改人id"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">创建时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="createTime" v-model.trim="liveCamera.createTime" placeholder="创建时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">修改时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="updateTime" v-model.trim="liveCamera.updateTime" placeholder="修改时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">项目ID</div>
                <div class="col-sm-9">
                    <my-input type="text" id="deptId" v-model.trim="liveCamera.deptId" placeholder="项目ID"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">项目对应部门CODE</div>
                <div class="col-sm-9">
                    <my-input type="text" id="deptCode" v-model.trim="liveCamera.deptCode" placeholder="项目对应部门CODE"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">名称</div>
                <div class="col-sm-9">
                    <my-input type="text" id="name" v-model.trim="liveCamera.name" placeholder="名称"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">经度</div>
                <div class="col-sm-9">
                    <my-input type="number" id="longitude" v-model.trim="liveCamera.longitude" placeholder="经度"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">纬度</div>
                <div class="col-sm-9">
                    <my-input type="number" id="latitude" v-model.trim="liveCamera.latitude" placeholder="纬度"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">接入平台;字典值： yingShi-萤石云</div>
                <div class="col-sm-9">
                    <my-input type="text" id="platform" v-model.trim="liveCamera.platform" placeholder="接入平台;字典值： yingShi-萤石云"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">接入平台序列号</div>
                <div class="col-sm-9">
                    <my-input type="text" id="platformSn" v-model.trim="liveCamera.platformSn" placeholder="接入平台序列号"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">设备通道号</div>
                <div class="col-sm-9">
                    <my-input type="text" id="channel" v-model.trim="liveCamera.channel" placeholder="设备通道号"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">视频平台appKey</div>
                <div class="col-sm-9">
                    <my-input type="text" id="appKey" v-model.trim="liveCamera.appKey" placeholder="视频平台appKey"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">视频平台秘钥</div>
                <div class="col-sm-9">
                    <my-input type="text" id="appSecret" v-model.trim="liveCamera.appSecret" placeholder="视频平台秘钥"></my-input>
                </div>
            </div>
							
            <div class="form-group center">
                <input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
                &nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
            </div>
        </form>
    </div>
</div>
<script src="${request.contextPath}/statics/js/modules/video/liveCamera.js?_${sloth.version()}"></script>
</body>
</html>
