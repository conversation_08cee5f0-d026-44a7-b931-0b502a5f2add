<#import "/macro/macro.ftl" as my />
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><@my.config configCode="webSite.name"/></title>
    <!-- Tell the browser to be responsive to screen width -->
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <link rel="shortcut icon" href="${request.contextPath}/statics/favicon.ico" type="image/x-icon"/>
    <link rel="stylesheet" href="${request.contextPath}/statics/css/bootstrap.min.css">
    <link rel="stylesheet" href="${request.contextPath}/statics/css/font-awesome.min.css">
    <link rel="stylesheet" href="${request.contextPath}/statics/css/AdminLTE.min.css">
    <!-- AdminLTE Skins. Choose a skin from the css/skins
         folder instead of downloading all of them to reduce the load. -->
    <link rel="stylesheet" href="${request.contextPath}/statics/css/all-skins.min.css">
    <link rel="stylesheet" href="${request.contextPath}/statics/css/main.css?_${sloth.version()}">
    <link rel="stylesheet" href="/statics/plugins/element-ui/lib/theme-chalk/index.css?_${sloth.version()}">

    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->

    <style>
        .login-title {
            line-height: 1.8em;
            font-size: 1.2em;
        }

        .login-box-body {
            position: relative;
        }

        .switch-container {
            top: 0;
            right: 0;
            overflow: hidden;
            position: absolute;
            width: calc(60px * 1.2);
            height: calc(60px * 1.2);
        }

        .switch-wrapper {
            overflow: hidden;
            position: absolute;
            transform-origin: 0 0;
            transform: skewX(36deg) translateX(-4px);
            color: mediumseagreen;
        }

        .switch-wrapper:hover {
            color: palegreen;
        }

        .switch-icon {
            transform: skewX(-36deg) translateX(-4px);
            transform-origin: 0 0;
            font-size: 60px;
            width: calc(60px * 1.2);
            height: calc(60px * 1.2);
            text-align: center;
            line-height: calc(60px * 1.2);
        }

        .switch-icon-pc {
            font-size: 56px;
        }

        #wxlogin_container {
            width: 100%;
            height: 147px;
            /*overflow: hidden;*/
        }

        #wxlogin_container img {
            width: 147px;
            height: 147px;
            margin: 0 auto;
        }

        .wx-qr-expired {
            width: 100%;
            height: 100%;
            position: absolute;
            background-color: rgba(255, 255, 255, .9);
            font-weight: bolder;
        }

        .wx-qr-expired-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-shadow: 1px 1px 2px #FC0, 0 0 1em #FC0, 0 0 0.2em #FC0;
        }

        @media (min-width: 768px) {
            .form-horizontal {
                width: 360px;
            }
        }
    </style>
</head>
<body class="hold-transition">
<div class="login-page">
    <div class="login-box" id="app" v-cloak>
        <div class="login-logo">
            <b><@my.config configCode="webSite.name"/></b>
        </div>

        <!-- /.login-logo -->
        <div v-show="showDiv=='login'" class="login-box-body">
            <div v-show="showWxLoginBtn" class='switch-container'>
                <button @click="showWxLogin" class="btn-link switch-wrapper" type="button"><i aria-hidden="true"
                                                                                              class="fa fa-qrcode switch-icon"></i>
                </button>
            </div>

            <p class="login-box-msg login-title">用户登录</p>

            <div v-if="error" class="alert alert-danger alert-dismissible">
                <h4 style="margin-bottom: 0px;"><i class="fa fa-exclamation-triangle"></i> {{errorMsg}}</h4>
            </div>
            <div class="form-group has-feedback">
                <input class="form-control" v-model.trim="username" @keyup.enter="login(false)"
                       placeholder="用户名">
                <span class="glyphicon glyphicon-user form-control-feedback"></span>
            </div>
            <div class="form-group has-feedback">
                <input type="password" class="form-control" v-model.trim="password" @keyup.enter="login(false)"
                       placeholder="密码">
                <span class="glyphicon glyphicon-lock form-control-feedback"></span>
            </div>
            <div v-show="showValidateCode">
                <div class="col-xs-6">
                    <div class="form-group has-feedback">
                        <input class="form-control" v-model.trim="captcha" @keyup.enter="login(false)"
                               placeholder="验证码">
                        <span class="glyphicon glyphicon-warning-sign form-control-feedback"></span>
                    </div>
                </div>
                <div class="col-xs-6">&nbsp;&nbsp;&nbsp;&nbsp;<img style="height: 40px" alt="单击刷新！" class="pointer"
                                                                   :src="src" @click="refreshCode"></div>
            </div>
            <div class="clearfix"></div>
            <div class="row">
                <div class="col-xs-4">
                    <div class="checkbox icheck">
                        <input type="checkbox" id="rememberMe" v-model="rememberMe">
                        <label for="rememberMe">记住我</label>
                    </div>
                </div>
                <!-- /.col -->
                <#--<div class="col-xs-8" style="text-align: right;">-->
                <#--<a href="javascript:;">忘记密码请联系管理员</a>-->

                <#--</div>-->
                <!-- /.col -->
            </div>
            <div class="form-group has-feedback" style="margin-top: 10px;">
                <button type="button" id="loginBtn" class="btn btn-primary  btn-block btn-flat" @click="login(false)">登
                    录
                </button>
            </div>
            <div v-show="showWxLoginBtn" class="form-group has-feedback">
                <p class="form-control-static">其他方式：
                    <button @click="showWxLogin" class="btn-link" type="button"><i class="fa fa-weixin text-green"
                                                                                   aria-hidden="true"></i></button>
                </p>
            </div>
            <!-- /.social-auth-links -->

        </div>
        <!-- /.login-box-body -->

        <!--找回密码-->
        <div v-show="showDiv=='findPassword'" class="login-box-body">
            <p class="login-box-msg">找回密码</p>
            <div v-if="error" class="alert alert-danger alert-dismissible">
                <h4 style="margin-bottom: 0px;"><i class="fa fa-exclamation-triangle"></i> {{errorMsg}}</h4>
            </div>
            <div class="form-group has-feedback">
                <input class="form-control" v-model.trim="mobileOrEmail" placeholder="请输入注册用的手机号码/邮箱">
                <span class="glyphicon glyphicon-user form-control-feedback"></span>
            </div>
            <div>
                <div class="col-xs-6">
                    <div class="form-group has-feedback">
                        <input class="form-control" v-model.trim="captcha2" placeholder="验证码">
                        <span class="glyphicon glyphicon-warning-sign form-control-feedback"></span>
                    </div>
                </div>
                <div class="col-xs-6">&nbsp;&nbsp;&nbsp;&nbsp;<img style="height: 40px" alt="单击刷新！" class="pointer"
                                                                   :src="src" @click="refreshCode"></div>
            </div>
            <div class="clearfix"></div>
            <div class="form-group has-feedback" style="margin-top: 10px;">
                <button type="button" id="getCode" class="btn btn-primary  btn-block btn-flat" @click="getVerifyCode">
                    获取手机/邮箱验证码
                </button>
            </div>
            <div v-if="editVerify" class="form-group has-feedback">
                <p class="form-control-static text-danger">您的验证短信或邮件已发送，请填写收到的验证码信息及新密码并继续</p>
            </div>
            <div v-if="editVerify" class="form-group has-feedback">
                <input class="form-control" v-model.trim="verifyCode" placeholder="请填写短信/邮件中的验证码">
                <span class="glyphicon glyphicon-warning-sign form-control-feedback"></span>
            </div>
            <div v-if="editVerify" class="form-group has-feedback" style="margin-top: 10px;">
                <button type="button" class="btn btn-primary  btn-block btn-flat" @click="doVerify">下一步</button>
            </div>
            <div class="form-group has-feedback" style="margin-top: 10px;">
                <button type="button" class="btn btn-default  btn-block btn-flat" @click="reload">返 回</button>
            </div>
        </div>
        <!--找回密码结束-->

        <!-- 输入输入新密码 -->
        <div v-show="showDiv=='continue'" class="login-box-body">
            <p class="login-box-msg">找回密码</p>
            <div v-if="error" class="alert alert-danger alert-dismissible">
                <h4 style="margin-bottom: 0px;"><i class="fa fa-exclamation-triangle"></i> {{errorMsg}}</h4>
            </div>

            <div class="form-group has-feedback">
                <input autocomplete="new-password" class="form-control" id="newPassword" placeholder="请输入新密码"
                       rules="[{notNull:true,message:'新密码不能为空'},{regExp:/^[^\u4e00-\u9fa5]{0,}$/,message:'新密码不可包含汉字'}]"
                       type="password" v-if="pp=='false'" v-model.trim="newPassword"/>
                <input autocomplete="new-password" class="form-control" id="newPassword" placeholder="请输入新密码"
                       rules="[{notNull:true,message:'新密码不能为空'},{regExp:/(?=.*[0-9])(?=.*[a-zA-Z]).{8,30}/,message:'必须包含字母和数字，长度8-30'},{regExp:/^[^\u4e00-\u9fa5]{0,}$/,message:'新密码不可包含汉字'}]"
                       type="password" v-else v-model.trim="newPassword"/>
                <span class="glyphicon glyphicon-lock form-control-feedback"></span>
            </div>
            <div class="form-group has-feedback">
                <input autocomplete="new-password" class="form-control" id="conPassword" placeholder="请再次输入新密码"
                       rules="[{notNull:true,message:'确认密码不能为空'},{equalWith:'newPassword',message:'两次新密码必须相同'},]"
                       type="password" v-model.trim="conPassword"/>
                <span class="glyphicon glyphicon-lock form-control-feedback"></span>
            </div>

            <div class="form-group has-feedback" style="margin-top: 10px;">
                <button type="button" class="btn btn-primary  btn-block btn-flat" @click="complete">完 成</button>
            </div>
            <div class="form-group has-feedback" style="margin-top: 10px;">
                <button type="button" class="btn btn-default  btn-block btn-flat" @click="reload">返 回</button>
            </div>
        </div>

        <#--微信登录二维码-->
        <div v-show="showDiv==='wxLogin'" class="login-box-body">
            <div class='switch-container'>
                <button @click="reload" class="btn-link switch-wrapper" type="button"><i aria-hidden="true"
                                                                                         class="fa fa-desktop switch-icon switch-icon-pc"></i>
                </button>
            </div>

            <p class="login-box-msg login-title">微信登录</p>

            <div v-if="error" class="alert alert-danger alert-dismissible">
                <h4 style="margin-bottom: 0px;"><i class="fa fa-exclamation-triangle"></i> {{errorMsg}}</h4>
            </div>

            <div id="wxlogin_container" class="form-group has-feedback">
                <div id="wx_qr_expired" class="wx-qr-expired" v-if="wxQrcodeExpired" @click="showWxLogin">
                    <div class="wx-qr-expired-text">
                        二维码过期<br>
                        <span class="text-blue">请点击刷新</span>
                    </div>
                </div>
            </div>
            <div class="text-center"><i class="fa fa-weixin text-green" aria-hidden="true"></i> 微信扫码<span
                        class="text-green text-bold">关注公众号</span></div>
            <div class="text-center">登录更快更安全</div>

            <div class="form-group has-feedback" style="margin-top: 10px;">
                <button type="button" class="btn-link center-block" @click="reload">密码登录</button>
            </div>
        </div>

        <#--扫码后未绑定账户，绑定现存账户。-->
        <form @submit="bindExistingAccount" action="javascript:" autocomplete="off" class="login-box-body"
              v-show="showDiv==='bindExistingAccount'">
            <p class="login-box-msg login-title">完善资料</p>

            <div v-if="error" class="alert alert-danger alert-dismissible">
                <h4 style="margin-bottom: 0px;"><i class="fa fa-exclamation-triangle"></i> {{errorMsg}}</h4>
            </div>
            <div class="form-group has-feedback">
                <h5>您已使用微信扫码</h5>
            </div>
            <div class="form-group has-feedback">
                <input class="form-control" v-model.trim="username" placeholder="用户名">
                <span class="glyphicon glyphicon-user form-control-feedback"></span>
            </div>

            <div class="form-group has-feedback">
                <input type="password" class="form-control" v-model.trim="password" placeholder="密码">
                <span class="glyphicon glyphicon-lock form-control-feedback"></span>
            </div>

            <div v-show="showValidateCode">
                <div class="col-xs-6">
                    <div class="form-group has-feedback">
                        <input class="form-control" v-model.trim="captcha" placeholder="验证码">
                        <span class="glyphicon glyphicon-warning-sign form-control-feedback"></span>
                    </div>
                </div>
                <div class="col-xs-6">&nbsp;&nbsp;&nbsp;&nbsp;<img style="height: 40px" alt="单击刷新！" class="pointer"
                                                                   :src="src" @click="refreshCode"></div>
            </div>

            <div class="clearfix"></div>

            <div class="form-group has-feedback" style="margin-top: 10px;">
                <button class="btn btn-primary  btn-block btn-flat">登录并绑定</button>
            </div>

            <div class="form-group has-feedback" style="margin-top: 10px;">
                <button type="button" class="btn-link center-block" @click="showDiv='bindNewAccount';refreshCode();">
                    注册一个新账号
                </button>
            </div>
        </form>

        <#--绑定新的账户-->
        <form @submit="bindNewAccount" action="javascript:" autocomplete="off" class="login-box-body"
              id="new-account-form" v-show="showDiv==='bindNewAccount'">
            <p class="login-box-msg login-title">完善资料</p>

            <div class="form-group has-feedback">
                <h5>您已使用微信扫码</h5>
            </div>
            <div class="form-group has-feedback">
                <label><span class="text-danger">*</span>用户名</label>
                <my-input placeholder="用户名"
                          rules="[{notNull:true,message:'用户名不能为空'},{regExp:/^[\u4e00-\u9fa5|\da-zA-Z]+$/,message:'只能使用中文、字母或数字'}, {regExp:/[^\d]/,message:'用户名不能是纯数字'},{fn:vm.verifyUsername,message:'用户名已存在'}]"
                          v-model.trim="username"></my-input>
            </div>

            <div class="form-group has-feedback">
                <label><span class="text-danger">*</span>姓名</label>
                <my-input placeholder="真实姓名" rules="[{notNull:true,message:'姓名不能为空'}]"
                          v-model="showName"></my-input>
            </div>

            <div class="form-group has-feedback">
                <label><span class="text-danger">*</span>密码</label>
                <my-input v-if="pp=='false'" @input="verifyPassword" autocomplete="new-password" placeholder="密码"
                          rules="[{notNull:true,message:'密码不能为空'},{regExp:/^[^\u4e00-\u9fa5]{0,}$/,message:'密码不可包含汉字'}]"
                          type="password" v-model="newPassword"></my-input>
                <my-input v-if="pp!='false'" @input="verifyPassword" autocomplete="new-password" placeholder="密码"
                          rules="[{notNull:true,message:'密码不能为空'},{regExp:/(?=.*[0-9])(?=.*[a-zA-Z]).{8,30}/,message:'必须包含字母和数字，长度8-30'},{regExp:/^[^\u4e00-\u9fa5]{0,}$/,message:'密码不可包含汉字'}]"
                          type="password" v-model="newPassword"></my-input>
            </div>

            <div class="form-group has-feedback">
                <label><span class="text-danger">*</span>确认密码</label>
                <my-input autocomplete="new-password" placeholder="密码"
                          rules="[{notNull:true,message:'密码不能为空'},{fn:vm.verifyPassword,message:'两次密码不一致'}]"
                          type="password" v-model="confirmPassword"></my-input>
            </div>

            <div class="form-group">
                <label>邮箱</label>
                <my-input placeholder="邮箱"
                          rules="[{isEmail:true,message:'邮箱不正确'},{fn:vm.verifyEmail,message:'邮箱已存在'}]" type="email"
                          v-model="email"></my-input>
            </div>

            <div class="form-group">
                <label>手机号</label>
                <my-input type="tel" v-model="mobile" placeholder="手机号"
                          rules="[{isMobile:true,message:'手机号格式不正确'},{fn:vm.verifyMobile,message:'手机号已存在'}]"
                          maxlength="100"></my-input>
            </div>
            <div class="clearfix"></div>

            <div>
                <div class="col-xs-6">
                    <div class="form-group has-feedback">
                        <input type="text" id="reg_captcha" class="form-control" v-model.trim="captcha" placeholder="验证码" rules="[{notNull:true,message:'验证码不能为空'}]">
                        <span class="glyphicon glyphicon-warning-sign form-control-feedback"></span>
                        <label for="reg_captcha" class="normal-label"></label>
                    </div>
                </div>
                <div class="col-xs-6">&nbsp;&nbsp;&nbsp;&nbsp;<img style="height: 40px" alt="单击刷新！" class="pointer"
                                                                   :src="src" @click="refreshCode"></div>
            </div>

            <div class="clearfix"></div>

            <div class="form-group has-feedback" style="margin-top: 10px;">
                <button class="btn btn-primary  btn-block btn-flat">注册并绑定</button>
            </div>

            <div class="form-group has-feedback" style="margin-top: 10px;">
                <button type="button" class="btn-link center-block" @click="showDiv='bindExistingAccount'">选择已有账户
                </button>
            </div>
        </form>
    </div>
</div>
<div class="login-footer">
    <span><@my.config configCode='webSite.copyright'></@my.config></span>&nbsp;&nbsp;&nbsp;&nbsp;<a
            href="https://beian.miit.gov.cn/"><span><@my.config configCode='webSite.ICP'></@my.config></span></a>
</div>
<!-- /.login-box -->
<script src="${request.contextPath}/statics/libs/minified.min.js?_${sloth.version()}"></script>
<script src="${request.contextPath}/statics/libs/jquery.min.js"></script>
<script src="${request.contextPath}/statics/plugins/layer/layer.js"></script>
<script src="${request.contextPath}/statics/libs/vue.min.js"></script>
<script src="${request.contextPath}/statics/libs/bootstrap.min.js"></script>
<script src="${request.contextPath}/statics/libs/jquery.slimscroll.min.js"></script>
<script src="${request.contextPath}/statics/libs/fastclick.min.js"></script>
<script src="${request.contextPath}/statics/libs/app.js"></script>
<script src="${request.contextPath}/statics/libs/yuanben.js"></script>
<script src="${request.contextPath}/statics/libs/base64.min.js"></script>
<script src="${request.contextPath}/statics/js/common.validate.js"></script>
<script src="${request.contextPath}/statics/js/common.js?_${sloth.version()}"></script>
<script src="${request.contextPath}/statics/libs/axios.min.js"></script>
<script src="${request.contextPath}/statics/js/common.axios.js?_${sloth.version()}"></script>
<script src="${request.contextPath}/statics/libs/qrcode.min.js"></script>

<script src="${request.contextPath}/statics/js/common.vue-validate.js"></script>
<script src="${request.contextPath}/statics/js/common.vue-component.js"></script>

<!--elementUI-->
<script src="/statics/plugins/element-ui/lib/index.js?_${sloth.version()}"></script>

<script type="text/javascript">
    var vm = new Vue({
        el: '#app',
        data: {
            showDiv: 'login',
            username: '',
            showName: '',
            password: '',
            captcha: '',
            confirmPassword: '',
            email: '',
            mobile: '',
            error: false,
            errorMsg: '',
            src: '',
            rememberMe: false,
            from: '${from!""}'
            , showValidateCode: ${showValidateCode?string("true","false")}
            , mobileOrEmail: ''
            , captcha2: ''
            , verifyCode: ''//短信或邮件中的验证码
            , newPassword: ''
            , conPassword: ''
            , pp: '<@my.config  configCode="system.passwordPolicy"/>'//密码策略
            , id: ''
            , editVerify: false//显示输入验证码文本框
            //是否显示微信登录按钮
            , showWxLoginBtn: '<@my.config configCode="system.showWxLoginBtn"></@my.config>' == 'true'
            , qrCode: null,
            pollingTimerId: 0,
            pollingTimerStartAt: 0,
            wxQrcodeExpired: false,
        },
        beforeCreate: function () {
            if (self != top) {
                top.location.href = self.location.href;
            }
        },
        methods: {
            getUser: function () {
                var that = this;
                this.$http.get("sys/user/info?_" + Date.now()).then(function (r) {
                    if (r.user != null && r.user.userId != null && r.user.userId !== '') {
                        if (typeof that.from === 'string' && that.from.length && that.from.indexOf('logout') < 0) {
                            top.location.href = that.from;
                        } else {
                            top.location.href = "${request.contextPath}/system.html";
                        }
                    }
                });
            },
            refreshCode: function () {
                this.src = "captcha.jpg?t=" + Date.now();
            },
            login: function (force) {
                if (!force) {
                    //非空验证
                    if (vm.username == "") {
                        vm.error = true;
                        vm.errorMsg = "用户名不能为空";
                        return;
                    }
                    if (vm.password == "") {
                        vm.error = true;
                        vm.errorMsg = "密码不能为空";
                        return
                    }
                    if (vm.showValidateCode && vm.captcha == "") {
                        vm.error = true;
                        vm.errorMsg = "验证码不能为空";
                        return
                    }
                    if ($("#loginBtn").attr("disabled")) {
                        //防止验证码回车重复提交
                        return
                    }
                    $("#loginBtn").html("登录中...").removeClass("btn-primary").attr("disabled", "disabled");
                }
                $.post("sys/login", {"username": "********", "password": "********"}, function (result) {
                    if (result.code == 0) {
                        vm.makeFun(result, force);
                    } else {
                        vm.error = true;
                        vm.errorMsg = result.msg;
                        $("#loginBtn").html("登录").addClass("btn-primary").removeAttr("disabled");
                        vm.refreshCode();
                    }

                })

            },
            bindExistingAccount: function (e) {
                //非空验证
                if (this.username === "") {
                    this.error = true;
                    this.errorMsg = "用户名不能为空";
                    return;
                }
                if (this.password === "") {
                    this.error = true;
                    this.errorMsg = "密码不能为空";
                    return
                }
                if (this.showValidateCode && this.captcha === "") {
                    this.error = true;
                    this.errorMsg = "验证码不能为空";
                    return
                }

                var p = new URLSearchParams();
                p.append('username', this.username);
                p.append('password', this.password);
                p.append('captcha', this.captcha);
                var that = this;
                this.$http.post(baseURL + 'sys/qrcode/doBind', p).then(function (r) {
                    // logon, redirect
                    if (typeof this.from === 'string' && this.from.length && that.from.indexOf('logout') < 0) {
                        top.location.href = this.from;
                    } else {
                        top.location.href = '${request.contextPath}/system.html';
                    }
                }.bind(this)).catch(function () {
                    this.refreshCode();
                }.bind(this));
            },
            bindNewAccount: function (e) {
                if (!validateForm("new-account-form")) {
                    return;
                }

                var userStr = {
                    username: this.username,
                    showName: this.showName,
                    password: this.newPassword,
                    email: this.email,
                    mobile: this.mobile,
                };

                var that = this;
                this.$http.post(baseURL + 'sys/qrcode/register2?captcha=' + this.captcha, userStr).then(function (r) {

                    that.$http.post(baseURL + "sys/qrcode/poll").then(function (r) {
                        if (r.scanned) {
                            that.stopPolling();
                            //登陆跳转 (清除 cache sessionId)

                            if (!r.bound) {
                                //没有绑定账号。注册新账号，或者绑定现有账号。
                                that.showDiv = 'bindNewAccount';
                                that.refreshCode();
                            } else {
                                //否则做登录跳转
                                if (typeof that.from === 'string' && that.from.length && that.from.indexOf('logout') < 0) {
                                    top.location.href = that.from;
                                } else {
                                    top.location.href = '${request.contextPath}/system.html';
                                }
                            }
                        }
                    }).catch(function (err) {
                        that.stopPolling();
                        //登录失败
                    });
                }).catch(function () {
                    this.refreshCode();
                }.bind(this));
            },
            verifyUsername: function () {
                if (this.username.length) {
                    var result = false;
                    $.post({
                        url: baseURL + 'sys/qrcode/checkUserName/' + encodeURIComponent(this.username),
                        async: false,
                        success: function (r) {
                            if (r.code === 0) {
                                result = !r.result;
                            }
                        }
                    });
                    return result;
                } else {
                    return true;
                }
            },
            verifyPassword: function () {
                if (this.confirmPassword.length && this.newPassword.length) {
                    return this.confirmPassword === this.newPassword;
                } else {
                    return true;
                }
            },
            verifyEmail: function () {
                if (this.email.length) {
                    var result = false;
                    $.ajax({
                        type: "POST",
                        url: baseURL + 'sys/qrcode/checkEmail/' + encodeURIComponent(this.email),
                        async: false,
                        success: function (r) {
                            if (r.code === 0) {
                                result = !r.result;
                            }
                        }
                    });
                    return result;
                } else {
                    return true;
                }
            },
            verifyMobile: function () {
                var that = this;
                if (this.mobile.length) {
                    var result = false;
                    $.ajax({
                        type: "POST",
                        url: baseURL + 'sys/qrcode/checkMobile/' + encodeURIComponent(this.mobile),
                        async: false,
                        success: function (r) {
                            if (r.code == 0) {
                                result = !r.result;
                            }
                        }
                    });
                    return result;
                } else {
                    return true;
                }
            },
            makeFun: function (result, force) {
                var data = vm.getData(result, Base64.encode(vm.username + "&&&&" + vm.password), vm.password) + "&captcha=" + vm.captcha + "&rememberMe=" + vm.rememberMe;
                var url = "sys/joke";
                if (force) {
                    url = "sys/force";
                }

                var that = this;
                $.post({
                    url: url,
                    data: data,
                    dataType: "json",
                    success: function (r) {
                        if (r.code == 0) {//登录成功
                            if (typeof that.from === 'string' && that.from.length && that.from.indexOf('logout') < 0) {
                                top.location.href = that.from;
                            } else {
                                top.location.href = '${request.contextPath}/system.html';
                            }
                        } else if (r.code == 1002) {
                            confirm("您的账号已在其他地点登录，是否强制登录", function () {
                                AppUtils.loadingStart("登录中...")
                                vm.login(true);
                            });
                            $("#loginBtn").html("登录").addClass("btn-primary").removeAttr("disabled");
                        } else {
                            vm.error = true;
                            vm.errorMsg = r.msg;
                            vm.showValidateCode = r.showValidateCode;
                            $("#loginBtn").html("登录").addClass("btn-primary").removeAttr("disabled");
                            vm.refreshCode();
                        }
                    }
                });
            },
            getData: function (r, u, p) {
                var a = YBUtils.encryptedString(YBUtils.getKeyPair(r.b, '', r.a), u);
                var b = YBUtils.encryptedString(YBUtils.getKeyPair(r.d, '', r.c), u);
                var c = YBUtils.encryptedString(YBUtils.getKeyPair(r.f, '', r.e), p);
                var d = YBUtils.encryptedString(YBUtils.getKeyPair(r.h, '', r.g), p);
                return "&a=" + a + "&b=" + b + "&c=" + c + "&d=" + d
            },
            showWxLogin: function () {
                this.wxQrcodeExpired = false;
                this.showDiv = "wxLogin";
                //获取ticket二维码url
                this.getTicketQrCode();

                //开始轮询后端，是否收到微信扫码回调
                this.startPolling();
            },
            //获取二维码url并，创建qrcode图片
            getTicketQrCode: function () {
                var that = this;
                this.$http.post(baseURL + 'sys/qrcode/url').then(function (r) {
                    if (r.code === 0) {
                        if (that.qrCode) {
                            that.qrCode.clear();
                            that.qrCode.makeCode(r.url);
                        } else {
                            that.qrCode = new QRCode(document.getElementById("wxlogin_container"), {
                                text: r.url,
                                width: 147,
                                height: 147,
                            });
                        }
                        that.qrCode._el.title = '';
                    } else {
                        alert(r.msg)
                    }
                });
            },
            forgetPassword: function () {
                this.error = false;
                this.showDiv = "findPassword";
                vm.refreshCode();
            },
            getVerifyCode: function () {
                if (vm.mobileOrEmail == "") {
                    vm.error = true;
                    vm.errorMsg = "请输入邮箱或电话号码";
                    return
                }
                if (vm.captcha2 == "") {
                    vm.error = true;
                    vm.errorMsg = "请输入图片验证码";
                    return
                }
                $.post("sys/getCode", {mobileOrEmail: vm.mobileOrEmail, captcha: vm.captcha2}, function (r) {
                    if (r.code == 0) {
                        vm.id = r.id;
                        vm.pp = r.pp;
                        vm.error = false;
                        vm.editVerify = true;
                        vm.setTime(60);
                    } else {
                        vm.error = true;
                        vm.errorMsg = r.msg;
                    }
                    vm.refreshCode();
                })
            },
            setTime: function (time) {
                if (time == 0) {
                    $("#getCode").removeAttr("disabled");
                    $("#getCode").text("获取手机/邮箱验证码");
                } else {
                    $("#getCode").attr("disabled", true);
                    $("#getCode").text("再次获取（" + time + ")");
                    time--
                    setTimeout(function () {
                        vm.setTime(time)
                    }, 1000);
                }
            },
            doVerify: function () {
                if (vm.verifyCode === '') {
                    vm.error = true;
                    vm.errorMsg = '请输入获取的验证码';
                    return
                }
                $.post("sys/verifyCode", {verifyCode: vm.verifyCode, id: vm.id}, function (r) {
                    if (r.code == 0) {
                        vm.error = false;
                        vm.showDiv = 'continue';
                    } else {
                        vm.errorMsg = r.msg;
                        vm.error = true;
                        vm.refreshCode();
                    }
                });
            },
            complete: function () {
                if (!validateInputById('newPassword') || !validateInputById('conPassword')) {
                    return false;
                }

                var params = new URLSearchParams();
                params.append("id", this.id);
                params.append("password", this.newPassword);
                var that = this;
                $.post("sys/findPassword", params, function (r) {
                    if (r.code == 0) {
                        alert("操作成功");
                        that.reload();
                    } else {
                        that.error = true;
                        that.errorMsg = r.msg;
                    }

                });

            },
            reload: function () {
                vm.showDiv = 'login';
                vm.error = false;
                vm.editVerify = false;
                vm.verifyCode = '';
                vm.refreshCode();

                //停止轮询后端是否收到微信扫码回调。
                this.stopPolling();
            },
            startPolling: function () {
                if (this.pollingTimerId) {
                    return;
                }

                var that = this;
                var func = function () {
                    //二维码等待超时，要求刷新二维码
                    if (Date.now() - that.pollingTimerStartAt >= 60 * 1000) {
                        that.wxQrcodeExpired = true;
                        that.stopPolling();
                        return;
                    }

                    that.$http.post(baseURL + "sys/qrcode/poll").then(function (r) {
                        if (r.scanned) {
                            that.stopPolling();
                            //登陆跳转 (清除 cache sessionId)

                            if (!r.bound) {
                                //没有绑定账号。注册新账号，或者绑定现有账号。
                                that.showDiv = 'bindNewAccount';
                                that.refreshCode();
                            } else {
                                //否则做登录跳转
                                if (typeof that.from === 'string' && that.from.length && that.from.indexOf('logout') < 0) {
                                    top.location.href = that.from;
                                } else {
                                    top.location.href = '${request.contextPath}/system.html';
                                }
                            }
                        }
                    }).catch(function (err) {
                        that.stopPolling();
                        //登录失败
                    });
                };
                this.pollingTimerStartAt = Date.now();
                this.pollingTimerId = setInterval(func, 1500);
            },
            stopPolling: function () {
                clearInterval(this.pollingTimerId);
                this.pollingTimerId = 0;
            }
        },
    });

    $(function () {
        // $.get("sys/user/info?_"+Date.now(), function(r){
        //     if(r.user!=null&&r.user.userId!=null&&r.user.userId!=""){
        //         window.location.href="/index.html"
        //     }
        // });
        vm.getUser();
        vm.refreshCode();
    })
</script>
</body>
</html>
