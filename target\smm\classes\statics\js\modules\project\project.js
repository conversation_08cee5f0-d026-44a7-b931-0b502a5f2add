																															
var vm = new Vue({
	el:'#app',
	mixins: [basicTableMixin],
	data:{
		showList: true,
		title: null,
		drawer:false,
		q:{},
		project: {}
	},
	methods: {
		add: function(){
			vm.title = "新增";
			vm.project = {};
			vm.$nextTick(function () {
				vm.showList = false;
			})
		},
		update: function (deptId) {
			//var deptId = getSelectedRow();
			if(deptId == null){
				return ;
			}
			vm.showList = false;
            vm.title = "修改";
            vm.getInfo(deptId)
		},
		saveOrUpdate: function (event) {
		    if(!validateForm("frm")){
		        return;
			}
			var url = vm.project.deptId == null ? "project/project/save" : "project/project/update";
			var that=this;
			this.$http.postWithShade(baseURL + url, this.project)
					.then(function (r) {
						alert("操作成功", function () {
							that.reload();
						});
					});
		},
		del: function (event) {
			var deptIds = this.$refs.projectTable.getSelectRowKeys();
			if(deptIds == null){
				alert('请选择一条记录');
				return ;
			}
			var that=this;
			confirm('确定要删除选中的 ' + deptIds.length + ' 条记录？', function () {
				that.$http.post(baseURL + "project/project/delete", deptIds)
						.then(function (r) {
							alert("操作成功", function () {
								that.reload();
							});
						});
			});
		},
		delOne: function (deptId) {
			var that=this;
			confirm('确定要删除选中的记录？', function(){
				that.$http.post(baseURL + "project/project/delete/" +deptId)
						.then(function (r) {
							alert("操作成功", function () {
								that.reload();
							});
						});
			});
        },
		getInfo: function(deptId){
			var that=this
			this.$http.post(baseURL + "project/project/info/" +deptId)
					.then(function (r) {
						that.project = r.project;
					});
		},
		reload: function (event) {
			vm.showList = true;
			this.$refs.projectTable.search(this.q, event === 'query');
            $(".error-label").remove();
		}
	},
});
