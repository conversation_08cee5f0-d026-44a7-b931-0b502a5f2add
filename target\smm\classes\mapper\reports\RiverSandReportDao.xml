<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.reports.dao.RiverSandReportDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.reports.entity.RiverSandReport" id="riverSandReportMap">
        <result property="id" column="ID"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="deptId" column="DEPT_ID"/>
        <result property="deptCode" column="DEPT_CODE"/>
        <result property="areaCode" column="AREA_CODE"/>
        <result property="yearMonth" column="YEAR_MONTH"/>
        <result property="plandedTotalInvestment" column="PLANDED_TOTAL_INVESTMENT"/>
        <result property="monthInvestment" column="MONTH_INVESTMENT"/>
        <result property="monthRiverLength" column="MONTH_RIVER_LENGTH"/>
        <result property="status" column="STATUS"/>
        <result property="reportUserId" column="REPORT_USER_ID"/>
        <result property="reportTime" column="REPORT_TIME"/>
    </resultMap>

    <select id="queryProject" resultType="com.ybkj.smm.modules.reports.entity.RiverSandReport">
        SELECT
            pro.DEPT_ID deptId,
            pro.DEPT_CODE deptCode,
            pro.AREA_CODE areaCode
        FROM
            smm_project_project pro
        WHERE
            pro.CREATE_TIME &lt;= #{month} and pro.DELETED = 0 and pro.type = 'riverSand'
    </select>

    <select id="sandReport" resultType="com.ybkj.smm.modules.reports.dto.SandReportDto">
        SELECT
            report.id,
            '项目' areaLevel,
            (case when LENGTH(area.AREA_CODE) = 11 THEN area.AREA_NAME ELSE cityArea.AREA_NAME END) cityAreaName,
            area.AREA_CODE countyAreaCode,
            (case when LENGTH(area.AREA_CODE) = 11 THEN '市本级' ELSE area.AREA_NAME END) countyAreaName,
            pro.`NAME` areaName,
            pro.SECTION_NAME sectionName,
            report.`YEAR_MONTH` yearMonth,
            report.`STATUS`,
            '1' sandProjectCount,
            IFNULL(pro.INVESTMENT,0) plandedTotalInvestment,
            IFNULL(SUM(report.MONTH_INVESTMENT),0) monthInvestment,
            IFNULL(pro.REVER_LENGTH,0) totalReverLength,
            IFNULL(SUM(report.MONTH_RIVER_LENGTH),0) monthRiverLength,
            IFNULL(pro.TOTAL_YIELD,0) sandTotalYield,
            (select IFNULL(sum(VEHICLE_LOAD),0) from smm_bill_bill where PROJECT_ID = pro.DEPT_ID and CREATE_TIME &gt;= #{startTime} and CREATE_TIME &lt;= #{endTime}) AS sandTotalVehicleLoad
        FROM
            smm_reports_river_sand_report report
        LEFT JOIN smm_project_project pro on report.DEPT_ID = pro.DEPT_ID
        LEFT JOIN sys_dept dept on pro.DEPT_ID = dept.DEPT_ID
        LEFT JOIN sys_area area on pro.AREA_CODE = area.AREA_CODE
        LEFT JOIN sys_area cityArea on area.PARENT_CODE = cityArea.AREA_CODE
        <where>
            ${ew.sqlSegment}
        </where>
        GROUP BY report.DEPT_ID
    </select>
    <select id="abandonReport" resultType="com.ybkj.smm.modules.reports.dto.AbandonReportDto">
        select
            result.id,
            result.areaLevel,
            result.cityAreaName,
            result.countyAreaCode,
            result.countyAreaName,
            result.proName areaName,
            result.abandonProjectCount,
            IFNULL(SUM(result.abandonTotalYield),0) abandonTotalYield,
            IFNULL(SUM(result.historyYield),0) historyYield,
            IFNULL(SUM(result.abandonTotalVehicleLoad),0) abandonTotalVehicleLoad,
            IFNULL(SUM(result.abandonTotalVehicleLoadSum),0) abandonTotalVehicleLoadSum
        from (
            SELECT
                pro.DEPT_ID id,
                '项目' areaLevel,
                (case when LENGTH(area.AREA_CODE) = 11 THEN area.AREA_NAME ELSE cityArea.AREA_NAME END) cityAreaName,
                area.AREA_CODE countyAreaCode,
                (case when LENGTH(area.AREA_CODE) = 11 THEN '市本级' ELSE area.AREA_NAME END) countyAreaName,
                pro.`NAME` proName,
                '1' abandonProjectCount,
                pro.TOTAL_YIELD abandonTotalYield,
                pro.HISTORY_YIELD  historyYield,
                (select IFNULL(sum(VEHICLE_LOAD),0) from smm_bill_bill where PROJECT_ID = pro.DEPT_ID and CREATE_TIME &gt;= #{startTime} and CREATE_TIME &lt;= #{endTime})/10000 AS abandonTotalVehicleLoad,
                (select IFNULL(sum(VEHICLE_LOAD),0) from smm_bill_bill where PROJECT_ID = pro.DEPT_ID)/10000 + pro.HISTORY_YIELD AS abandonTotalVehicleLoadSum
            FROM
                smm_project_project pro
                LEFT JOIN sys_dept dept on pro.DEPT_ID = dept.DEPT_ID
                LEFT JOIN sys_area area on pro.AREA_CODE = area.AREA_CODE
                LEFT JOIN sys_area cityArea on area.PARENT_CODE = cityArea.AREA_CODE
            <where>
                ${ew.sqlSegment}
            </where>
        ) result
        GROUP BY result.proName
        ORDER BY result.abandonTotalVehicleLoad desc
    </select>




    <select id="sandReportPageHistory" resultType="com.ybkj.smm.modules.reports.dto.SandReportDto">
        SELECT
            report.id,
            report.STATUS,
            area.`AREA_NAME` areaName,
            area.`AREA_CODE` areaCode,
            report.`YEAR_MONTH` yearMonth,
            stat.SAND_PROJECT_COUNT sandProjectCount,
            stat.SAND_INVESTMENT plandedTotalInvestment,
            report.MONTH_INVESTMENT monthInvestment,
            stat.TOTAL_REVER_LENGTH totalReverLength,
            report.MONTH_RIVER_LENGTH monthRiverLength,
            stat.SAND_TOTAL_YIELD sandTotalYield,
            stat.SAND_TOTAL_VEHICLE_LOAD sandTotalVehicleLoad
        FROM
            smm_reports_river_sand_report report
            LEFT JOIN smm_reports_area_stat stat ON stat.DEPT_ID = report.DEPT_ID and stat.YEAR_MONTH = #{yearMonth}
            LEFT JOIN sys_dept dept ON report.DEPT_ID = dept.DEPT_ID
            LEFT JOIN sys_area area ON dept.AREA_CODE = area.AREA_CODE
        WHERE report.YEAR_MONTH =  #{yearMonth}
        <if test='areaCode != null &amp;&amp; areaCode != "" '>
            AND dept.AREA_CODE like CONCAT(#{areaCode}, '%')
        </if>
        <if test='status != null &amp;&amp; status != "" '>
            AND report.STATUS = #{status}
        </if>
        <if test='sqlFilter != null &amp;&amp; sqlFilter != "" '>
            and ${sqlFilter}
        </if>
    </select>

    <select id="sandReportPageCurrent" resultType="com.ybkj.smm.modules.reports.dto.SandReportDto">
        SELECT
            report.id,
            report.STATUS,
            area.`AREA_NAME` areaName,
            area.`AREA_CODE` areaCode,
            report.`YEAR_MONTH` yearMonth,
            stat.sandProjectCount,
            stat.sandInvestment plandedTotalInvestment,
            report.MONTH_INVESTMENT monthInvestment,
            stat.totalReverLength,
            report.MONTH_RIVER_LENGTH monthRiverLength,
            stat.sandTotalYield,
            stat.sandTotalVehicleLoad
        FROM
            smm_reports_river_sand_report report
        LEFT JOIN (
            SELECT
            dept.DEPT_ID deptId,
            dept.DEPT_CODE deptCode,
            dept.AREA_CODE areaCode,
            SUM( CASE WHEN a.TYPE = 'riverSand' THEN 1 ELSE 0 END ) sandProjectCount,
            SUM( CASE WHEN a.TYPE = 'riverSand' THEN a.TOTAL_YIELD ELSE 0 END ) sandTotalYield,
            SUM( CASE WHEN a.TYPE = 'riverSand' THEN a.INVESTMENT ELSE 0 END ) sandInvestment,
            SUM( a.REVER_LENGTH ) totalReverLength,
            SUM( CASE WHEN b.TYPE = 'riverSand' THEN b.totalVehicleLoad ELSE 0 END ) sandTotalVehicleLoad
            FROM
            (
            SELECT
            pro.DEPT_ID,
            pro.PARENT_ID,
            pro.TYPE,
            SUM(pro.TOTAL_YIELD) TOTAL_YIELD,
            SUM(pro.REVER_LENGTH) REVER_LENGTH,
            SUM(pro.INVESTMENT) INVESTMENT
            FROM
            smm_project_project pro
            WHERE pro.DELETED = 0 and pro.CREATE_TIME &lt;= #{endTime}
            GROUP BY pro.`NAME`
            ) a
            LEFT JOIN (
            SELECT
            pro.DEPT_ID,
            pro.PARENT_ID,
            pro.TYPE,
            SUM( bill.VEHICLE_LOAD ) totalVehicleLoad
            FROM
            smm_project_project pro
            LEFT JOIN smm_bill_bill bill ON bill.PROJECT_ID = pro.DEPT_ID
            WHERE pro.DELETED = 0 and bill.CREATE_TIME &gt;= #{startTime} and bill.CREATE_TIME &lt;= #{endTime}
            GROUP BY
            pro.DEPT_ID
            ) b ON a.DEPT_ID = b.DEPT_ID
            LEFT JOIN sys_dept dept ON a.PARENT_ID = dept.DEPT_ID
            where dept.DEPT_ID is not NULL
            GROUP BY
            a.PARENT_ID
        ) stat on stat.deptId = report.DEPT_ID
        LEFT JOIN sys_dept dept ON report.DEPT_ID = dept.DEPT_ID
        LEFT JOIN sys_area area ON dept.AREA_CODE = area.AREA_CODE
        where report.YEAR_MONTH = #{yearMonth}
        <if test='areaCode != null &amp;&amp; areaCode != "" '>
            AND dept.AREA_CODE like CONCAT(#{areaCode}, '%')
        </if>
        <if test='status != null &amp;&amp; status != "" '>
            AND report.STATUS = #{status}
        </if>
        <if test='sqlFilter != null &amp;&amp; sqlFilter != "" '>
            and ${sqlFilter}
        </if>

    </select>

    <select id="sandReportPageAll" resultType="com.ybkj.smm.modules.reports.dto.SandReportDto">
        select result.* from(
        SELECT
        report.id,
        report.STATUS,
        area.`AREA_NAME` areaName,
        area.`AREA_CODE` areaCode,
        report.`YEAR_MONTH` yearMonth,
        stat.sandProjectCount,
        stat.sandInvestment plandedTotalInvestment,
        report.MONTH_INVESTMENT monthInvestment,
        stat.totalReverLength,
        report.MONTH_RIVER_LENGTH monthRiverLength,
        stat.sandTotalYield,
        stat.sandTotalVehicleLoad
        FROM
        smm_reports_river_sand_report report
        LEFT JOIN (
        SELECT
        dept.DEPT_ID deptId,
        dept.DEPT_CODE deptCode,
        dept.AREA_CODE areaCode,
        SUM( CASE WHEN a.TYPE = 'riverSand' THEN 1 ELSE 0 END ) sandProjectCount,
        SUM( CASE WHEN a.TYPE = 'riverSand' THEN a.TOTAL_YIELD ELSE 0 END ) sandTotalYield,
        SUM( CASE WHEN a.TYPE = 'riverSand' THEN a.INVESTMENT ELSE 0 END ) sandInvestment,
        SUM( a.REVER_LENGTH ) totalReverLength,
        SUM( CASE WHEN b.TYPE = 'riverSand' THEN b.totalVehicleLoad ELSE 0 END ) sandTotalVehicleLoad
        FROM
        (
        SELECT
        pro.DEPT_ID,
        pro.PARENT_ID,
        pro.TYPE,
        SUM(pro.TOTAL_YIELD) TOTAL_YIELD,
        SUM(pro.REVER_LENGTH) REVER_LENGTH,
        SUM(pro.INVESTMENT) INVESTMENT
        FROM
        smm_project_project pro
        WHERE pro.DELETED = 0 and pro.CREATE_TIME &lt;= #{endTime}
        GROUP BY pro.`NAME`
        ) a
        LEFT JOIN (
        SELECT
        pro.DEPT_ID,
        pro.PARENT_ID,
        pro.TYPE,
        SUM( bill.VEHICLE_LOAD ) totalVehicleLoad
        FROM
        smm_project_project pro
        LEFT JOIN smm_bill_bill bill ON bill.PROJECT_ID = pro.DEPT_ID
        WHERE pro.DELETED = 0 and bill.CREATE_TIME &gt;= #{startTime} and bill.CREATE_TIME &lt;= #{endTime}
        GROUP BY
        pro.DEPT_ID
        ) b ON a.DEPT_ID = b.DEPT_ID
        LEFT JOIN sys_dept dept ON a.PARENT_ID = dept.DEPT_ID
        where dept.DEPT_ID is not NULL
        GROUP BY
        a.PARENT_ID
        ) stat on stat.deptId = report.DEPT_ID
        LEFT JOIN sys_dept dept ON report.DEPT_ID = dept.DEPT_ID
        LEFT JOIN sys_area area ON dept.AREA_CODE = area.AREA_CODE
        where report.YEAR_MONTH = #{yearMonth}
        <if test='areaCode != null &amp;&amp; areaCode != "" '>
            AND dept.AREA_CODE like CONCAT(#{areaCode}, '%')
        </if>
        <if test='status != null &amp;&amp; status != "" '>
            AND report.STATUS = #{status}
        </if>
        <if test='sqlFilter != null &amp;&amp; sqlFilter != "" '>
            and ${sqlFilter}
        </if>

        UNION ALL

        SELECT
        report.id,
        report.STATUS,
        area.`AREA_NAME` areaName,
        area.`AREA_CODE` areaCode,
        report.`YEAR_MONTH` yearMonth,
        stat.SAND_PROJECT_COUNT sandProjectCount,
        stat.SAND_INVESTMENT plandedTotalInvestment,
        report.MONTH_INVESTMENT monthInvestment,
        stat.TOTAL_REVER_LENGTH totalReverLength,
        report.MONTH_RIVER_LENGTH monthRiverLength,
        stat.SAND_TOTAL_YIELD sandTotalYield,
        stat.SAND_TOTAL_VEHICLE_LOAD sandTotalVehicleLoad
        FROM
        smm_reports_river_sand_report report
        LEFT JOIN smm_reports_area_stat stat ON stat.DEPT_ID = report.DEPT_ID and report.`YEAR_MONTH` =
        stat.`YEAR_MONTH` and stat.YEAR_MONTH &lt; #{yearMonth}
        LEFT JOIN sys_dept dept ON report.DEPT_ID = dept.DEPT_ID
        LEFT JOIN sys_area area ON dept.AREA_CODE = area.AREA_CODE
        WHERE report.YEAR_MONTH &lt; #{yearMonth}
        <if test='areaCode != null &amp;&amp; areaCode != "" '>
            AND dept.AREA_CODE like CONCAT(#{areaCode}, '%')
        </if>
        <if test='status != null &amp;&amp; status != "" '>
            AND report.STATUS = #{status}
        </if>
        <if test='sqlFilter != null &amp;&amp; sqlFilter != "" '>
            and ${sqlFilter}
        </if>
        ) AS result
        order by result.yearMonth desc

    </select>

    <select id="abandonReportPageHistory" resultType="com.ybkj.smm.modules.reports.dto.AbandonReportDto">
        SELECT
        area.`AREA_NAME` areaName,
        area.`AREA_CODE` areaCode,
        stat.`YEAR_MONTH` yearMonth,
        stat.ABANDON_PROJECT_COUNT abandonProjectCount,
        stat.ABANDON_TOTAL_YIELD abandonTotalYield,
        stat.ABANDON_TOTAL_VEHICLE_LOAD abandonTotalVehicleLoad
        FROM
        smm_reports_area_stat stat
        LEFT JOIN sys_dept dept ON stat.DEPT_ID = dept.DEPT_ID
        LEFT JOIN sys_area area ON dept.AREA_CODE = area.AREA_CODE
        WHERE stat.YEAR_MONTH =  #{yearMonth}
        <if test='areaCode != null &amp;&amp; areaCode != "" '>
            AND dept.AREA_CODE like CONCAT(#{areaCode}, '%')
        </if>
        <if test='sqlFilter != null &amp;&amp; sqlFilter != "" '>
            and ${sqlFilter}
        </if>
    </select>

    <select id="abandonReportPageCurrent" resultType="com.ybkj.smm.modules.reports.dto.AbandonReportDto">
        SELECT
        area.`AREA_NAME` areaName,
        area.`AREA_CODE` areaCode,
        #{yearMonth} as yearMonth,
        stat.abandonProjectCount,
        stat.abandonTotalYield,
        stat.abandonTotalVehicleLoad
        FROM(
        SELECT
        dept.DEPT_ID deptId,
        dept.DEPT_CODE deptCode,
        dept.AREA_CODE areaCode,
        SUM( CASE WHEN a.TYPE = 'abandonSand' THEN 1 ELSE 0 END ) abandonProjectCount,
        SUM( CASE WHEN a.TYPE = 'abandonSand' THEN a.TOTAL_YIELD ELSE 0 END ) abandonTotalYield,
        SUM( CASE WHEN b.TYPE = 'abandonSand' THEN b.totalVehicleLoad ELSE 0 END ) abandonTotalVehicleLoad
        FROM
        (
        SELECT
        pro.DEPT_ID,
        pro.PARENT_ID,
        pro.TYPE,
        SUM(pro.TOTAL_YIELD) TOTAL_YIELD
        FROM
        smm_project_project pro
        where pro.DELETED = 0
        <if test='endTime != null &amp;&amp; endTime != "" '>
            and pro.CREATE_TIME &lt;= #{endTime}
        </if>
        GROUP BY pro.`NAME`
        ) a
        LEFT JOIN (
        SELECT
        pro.DEPT_ID,
        pro.PARENT_ID,
        pro.TYPE,
        SUM( bill.VEHICLE_LOAD ) totalVehicleLoad
        FROM
        smm_project_project pro
        LEFT JOIN smm_bill_bill bill ON bill.PROJECT_ID = pro.DEPT_ID
        WHERE pro.DELETED = 0
        <if test='startTime != null &amp;&amp; startTime != "" '>
            and bill.CREATE_TIME &gt;= #{startTime}
        </if>
        <if test='endTime != null &amp;&amp; endTime != "" '>
            and bill.CREATE_TIME &lt;= #{endTime}
        </if>
        GROUP BY
        pro.DEPT_ID
        ) b ON a.DEPT_ID = b.DEPT_ID
        LEFT JOIN sys_dept dept ON a.PARENT_ID = dept.DEPT_ID
        where dept.DEPT_ID is not NULL
        GROUP BY
        a.PARENT_ID
        ) stat
        LEFT JOIN sys_dept dept ON stat.deptId = dept.DEPT_ID
        LEFT JOIN sys_area area ON dept.AREA_CODE = area.AREA_CODE
        where 1=1
        <if test='areaCode != null &amp;&amp; areaCode != "" '>
            AND dept.AREA_CODE like CONCAT(#{areaCode}, '%')
        </if>
        <if test='sqlFilter != null &amp;&amp; sqlFilter != "" '>
            and ${sqlFilter}
        </if>
    </select>

    <select id="abandonReportPageAll" resultType="com.ybkj.smm.modules.reports.dto.AbandonReportDto">
        select result.* from (
        SELECT
        area.`AREA_NAME` areaName,
        area.`AREA_CODE` areaCode,
        #{yearMonth} as yearMonth,
        stat.abandonProjectCount,
        stat.abandonTotalYield,
        stat.abandonTotalVehicleLoad
        FROM(
        SELECT
        dept.DEPT_ID deptId,
        dept.DEPT_CODE deptCode,
        dept.AREA_CODE areaCode,
        SUM( CASE WHEN a.TYPE = 'abandonSand' THEN 1 ELSE 0 END ) abandonProjectCount,
        SUM( CASE WHEN a.TYPE = 'abandonSand' THEN a.TOTAL_YIELD ELSE 0 END ) abandonTotalYield,
        SUM( CASE WHEN b.TYPE = 'abandonSand' THEN b.totalVehicleLoad ELSE 0 END ) abandonTotalVehicleLoad
        FROM
        (
        SELECT
        pro.DEPT_ID,
        pro.PARENT_ID,
        pro.TYPE,
        SUM(pro.TOTAL_YIELD) TOTAL_YIELD
        FROM
        smm_project_project pro
        WHERE pro.DELETED = 0 and pro.CREATE_TIME &lt;= #{endTime}
        GROUP BY pro.`NAME`
        ) a
        LEFT JOIN (
        SELECT
        pro.DEPT_ID,
        pro.PARENT_ID,
        pro.TYPE,
        SUM( bill.VEHICLE_LOAD ) totalVehicleLoad
        FROM
        smm_project_project pro
        LEFT JOIN smm_bill_bill bill ON bill.PROJECT_ID = pro.DEPT_ID
        WHERE pro.DELETED = 0 and bill.CREATE_TIME &gt;= #{startTime} and bill.CREATE_TIME &lt;= #{endTime}
        GROUP BY
        pro.DEPT_ID
        ) b ON a.DEPT_ID = b.DEPT_ID
        LEFT JOIN sys_dept dept ON a.PARENT_ID = dept.DEPT_ID
        where dept.DEPT_ID is not NULL
        GROUP BY
        a.PARENT_ID
        ) stat
        LEFT JOIN sys_dept dept ON stat.deptId = dept.DEPT_ID
        LEFT JOIN sys_area area ON dept.AREA_CODE = area.AREA_CODE
        where 1=1
        <if test='areaCode != null &amp;&amp; areaCode != "" '>
            AND dept.AREA_CODE like CONCAT(#{areaCode}, '%')
        </if>
        <if test='sqlFilter != null &amp;&amp; sqlFilter != "" '>
            and ${sqlFilter}
        </if>

        union all

        SELECT
        area.`AREA_NAME` areaName,
        area.`AREA_CODE` areaCode,
        stat.`YEAR_MONTH` yearMonth,
        stat.ABANDON_PROJECT_COUNT abandonProjectCount,
        stat.ABANDON_TOTAL_YIELD abandonTotalYield,
        stat.ABANDON_TOTAL_VEHICLE_LOAD abandonTotalVehicleLoad
        FROM
        smm_reports_area_stat stat
        LEFT JOIN sys_dept dept ON stat.DEPT_ID = dept.DEPT_ID
        LEFT JOIN sys_area area ON dept.AREA_CODE = area.AREA_CODE
        WHERE stat.YEAR_MONTH &lt; #{yearMonth}
        <if test='areaCode != null &amp;&amp; areaCode != "" '>
            AND dept.AREA_CODE like CONCAT(#{areaCode}, '%')
        </if>
        <if test='sqlFilter != null &amp;&amp; sqlFilter != "" '>
            and ${sqlFilter}
        </if>
        ) AS result
        order by result.yearMonth desc
    </select>


</mapper>
