//ws工具类，提供发送ws消息，注册接收消息处理方法
window.WebSocketUtils = top.WebSocketUtils || {};
//处理消息的方法map，key为message的type，value为处理方法
WebSocketUtils.messageHandlers = WebSocketUtils.messageHandlers || {};
WebSocketUtils.activeClose = false;
WebSocketUtils.initWebSocket = function () {
    //定义ws对象
    WebSocketUtils.ws = WebSocketUtils.ws || new WebSocket(webSocketUrl);
    //连接成功建立的回调方法
    WebSocketUtils.ws.onopen = WebSocketUtils.ws.onopen|| function(event) {
        top.vm.user && top.vm.user.userId && WebSocketUtils.newUserMessage(top.vm.user.userId);
        console.log("web socket is ready");
    }
    //链接错误时的回调方法
    WebSocketUtils.ws.onerror= WebSocketUtils.ws.onerror||function (errorInfo) {
        console.error(errorInfo);
    }
    //接收到消息的回调方法
    WebSocketUtils.ws.onmessage = WebSocketUtils.ws.onmessage || function (event) {
        var wsMessage = JSON.parse(event.data);
        var callback = WebSocketUtils.messageHandlers[wsMessage.type];
        if(typeof (callback)=="function") {
            callback(wsMessage);
        }else{
            console.log("未注册ws消息处理方法")
            console.log(wsMessage)
        }
    };

    //连接关闭的回调方法
    WebSocketUtils.ws.onclose =  WebSocketUtils.ws.onclose||function(event) {
        // console.error(event);
        console.log("web socket is closed");
        if (!WebSocketUtils.activeClose) {
            console.log("websocket abnormal shutdown,restart it");
            //不是主动关闭的，保持重连
            WebSocketUtils.ws = null;
            WebSocketUtils.initWebSocket();
        }
    }

    //监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
    window.onbeforeunload = function() {
        WebSocketUtils.activeClose = true;
        WebSocketUtils.ws.close();
    }
    //关闭连接
    WebSocketUtils.closeWebSocket = WebSocketUtils.closeWebSocket || function () {
        WebSocketUtils.ws.close();
    };
};
//注册回调方法
WebSocketUtils.registerHandler=WebSocketUtils.registerHandler||function (type,callback) {
    WebSocketUtils.messageHandlers[type] = callback;
}

//发送消息
WebSocketUtils.sendMessage=WebSocketUtils.sendMessage||function(toUserId,type,toAll,content) {
    if (WebSocketUtils.ws && WebSocketUtils.ws.readyState === 1) {
        var wsMessage={
            toUserId:toUserId,
            type:type,
            toAll:toAll,
            content: content
        };
        WebSocketUtils.ws.send(JSON.stringify(wsMessage));
    }else{
        console.error("WebSocket is not ready,cannot send message");
    }

}

//发送心跳检测
WebSocketUtils.sendHeartbeat=WebSocketUtils.sendHeartbeat||function() {
    if (WebSocketUtils.ws && WebSocketUtils.ws.readyState === 1) {
        var wsMessage={
            type:"heartbeat",
        };
        WebSocketUtils.ws.send(JSON.stringify(wsMessage));
    }else{
        console.error("WebSocket is not ready,cannot send message");
    }

}

//当前用户与消息通道建立关联关系
WebSocketUtils.newUserMessage=WebSocketUtils.newUserMessage||function(fromUserId) {
    if (WebSocketUtils.ws && WebSocketUtils.ws.readyState === 1) {
        var wsMessage={
            fromUserId:fromUserId,
            type:"newUser"
        };
        WebSocketUtils.ws.send(JSON.stringify(wsMessage));
    }else{
        console.error("WebSocket is not ready,cannot send message");
    }

}
if ('WebSocket' in window) {
    if(webSocketOpen === 'true'){
        WebSocketUtils.initWebSocket();
        //注册用户超时处理方法
        WebSocketUtils.registerHandler("timeout", function (wsMessage) {
            console.log(wsMessage);
            WebSocketUtils.activeClose = true;
            WebSocketUtils.closeWebSocket();
        });
        if(webSocketHeartBeat === 'true'){
            //通过心跳检测，解决nginx转发时总是断开链接的问题
            setInterval(function () {
                WebSocketUtils.sendHeartbeat();
            }, 25 * 1000);
        }
    }
}else{
    console.log("WebSocket is not support");
}

