<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.bill.dao.ApplyExpireDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.bill.entity.ApplyExpire" id="applyExpireMap">
        <result property="id" column="ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="projectId" column="PROJECT_ID"/>
        <result property="carNumber" column="CAR_NUMBER"/>
        <result property="carCompany" column="CAR_COMPANY"/>
        <result property="carBrand" column="CAR_BRAND"/>
        <result property="carOwnerName" column="CAR_OWNER_NAME"/>
        <result property="carKerbWeight" column="CAR_KERB_WEIGHT"/>
        <result property="carMaxPayload" column="CAR_MAX_PAYLOAD"/>
        <result property="destination" column="DESTINATION"/>
        <result property="destLongitudeGps" column="DEST_LONGITUDE_GPS"/>
        <result property="destLatitudeGps" column="DEST_LATITUDE_GPS"/>
        <result property="destLongitude" column="DEST_LONGITUDE"/>
        <result property="destLatitude" column="DEST_LATITUDE"/>
        <result property="estimatedArrivalTime" column="ESTIMATED_ARRIVAL_TIME"/>
        <result property="destLeader" column="DEST_LEADER"/>
        <result property="destLeaderMobile" column="DEST_LEADER_MOBILE"/>
        <result property="status" column="STATUS"/>
        <result property="version" column="VERSION"/>
        <result property="deletedTime" column="DELETED_TIME"/>
    </resultMap>


</mapper>