<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybkj.smm.modules.rlc.mapper.RlcStatisticsMapper">

    <select id="getCountLicenceByType" parameterType="RlcLicence" resultType="java.util.Map">
        SELECT proj_type AS name, COUNT(proj_type) AS value
        FROM rlc_licence a
            LEFT JOIN sys_dept b   ON a.dept_id= b.DEPT_ID
        WHERE b.DEPT_CODE LIKE CONCAT(#{deptCode}, '%')
        GROUP BY proj_type
    </select>

    <select id="getCountLicenceByChildren" parameterType="RlcLicence" resultType="java.util.Map">

        SELECT dd.dept_name AS name, SUM(CASE WHEN ll.lic_code IS NULL THEN 0 ELSE 1 END) AS value
        FROM (
            SELECT
            dept_id,
            name as  dept_name,
            DEPT_CODE,
            ( CASE WHEN dept_code = #{deptCode} THEN 1 ELSE 0 END) AS is_head
            FROM sys_dept WHERE dept_code like CONCAT(#{deptCode}, '-____') AND type = 'dept'
            ) AS dd LEFT JOIN rlc_licence AS ll
        ON (dd.dept_id = ll.dept_id AND dd.is_head = 1) OR (ll.dept_id LIKE CONCAT(dd.dept_id, '%') AND dd.is_head = 0)
        GROUP BY dd.dept_name

    </select>


    <select id="getCountLicenceByYear" parameterType="RlcLicence" resultType="java.util.Map">
        SELECT YEAR(lic_date) AS name, COUNT(YEAR(lic_date)) AS value
        FROM rlc_licence a
            LEFT JOIN sys_dept b   ON a.dept_id= b.DEPT_ID
        WHERE b.DEPT_CODE LIKE CONCAT(#{deptCode}, '%')
        GROUP BY YEAR(lic_date)
    </select>

    <select id="getCountProjectByType" parameterType="RlcProject" resultType="java.util.Map">

        SELECT
            type AS name,
            COUNT(type) AS value
        FROM (
            SELECT id, licence_id, geojson, 'park' as type, dept_id FROM rlc_proj_park
            UNION ALL
            SELECT id, licence_id, geojson, 'wire' as type, dept_id FROM rlc_proj_wire
            UNION ALL
            SELECT id, licence_id, geojson, 'pipe' as type, dept_id FROM rlc_proj_pipe
            UNION ALL
            SELECT id, licence_id, geojson, 'road' as type, dept_id FROM rlc_proj_road
            ) AS aa
        WHERE EXISTS (
            SELECT 1
            FROM sys_dept d
            WHERE
        -- 匹配部门树（包含子部门）
            d.dept_code LIKE CONCAT(#{deptCode}, '%')  -- 假设部门代码是层级编码（如A01、A0101）
          AND (
        -- 处理逗号分隔的dept_id存储方式
            FIND_IN_SET(d.dept_id, REPLACE(aa.dept_id, ' ', '')) > 0  -- MySQL语法
        -- 或使用字符串匹配（其他数据库）
        -- aa.dept_id LIKE CONCAT('%,', d.dept_id, ',%')
        -- OR aa.dept_id LIKE CONCAT(d.dept_id, ',%')
        -- OR aa.dept_id LIKE CONCAT('%,', d.dept_id)
        -- OR aa.dept_id = d.dept_id
            )
            )
        GROUP BY type;
    </select>

    <select id="getCountInspectionByStatus" parameterType="RlcInspection" resultType="java.util.Map">
        WITH aa AS (
            SELECT id, is_cons_rec, is_finished FROM rlc_inspection
            WHERE dept_code LIKE CONCAT(#{deptCode}, '%')
            AND id IN (
                SELECT max(id) FROM rlc_inspection
                GROUP BY proj_id
            )
        )
        SELECT '批复未建' AS name, count(id) AS value FROM aa WHERE is_cons_rec = 'N'
        UNION ALL
        SELECT '在建' AS name, count(id) AS value FROM aa WHERE is_cons_rec = 'Y' AND is_finished = 'N'
        UNION ALL
        SELECT '已完工' AS name, count(id) AS value FROM aa WHERE is_cons_rec = 'Y' AND is_finished = 'Y'
    </select>

    <select id="getCountInspection" parameterType="RlcInspection" resultType="int">
        SELECT count(id) AS count
        FROM rlc_inspection
        WHERE dept_code LIKE CONCAT(#{deptCode}, '%')
        AND (is_cons_rec IS NOT NULL OR is_finished IS NOT NULL)

    </select>

    <select id="getSummary" parameterType="RlcParamInterval" resultType="RlcSummary">
        SELECT
            ddll.deptName,
            ddll.deptId,
            ( CASE WHEN ddll.count IS NULL THEN 0 ELSE ddll.count END ) AS countLicence,
            ( CASE WHEN ddpp.count IS NULL THEN 0 ELSE ddpp.count END ) AS countProject,
            ( CASE WHEN ddpp.countRegistered IS NULL THEN 0 ELSE ddpp.countRegistered END ) AS countProjectRegistered,
            ( CASE WHEN ddpp.countUndergoing IS NULL THEN 0 ELSE ddpp.countUndergoing END ) AS countProjectUndergoing,
            ( CASE WHEN ddpp.countComplete IS NULL THEN 0 ELSE ddpp.countComplete END ) AS countProjectComplete,
            ( CASE WHEN ddii.count IS NULL THEN 0 ELSE ddii.count END ) AS countInspectionTotal
        FROM
            (
                SELECT
                    dd.dept_name AS deptName,
                    dd.dept_id AS deptId,
                    SUM( ll.count ) AS count
                FROM
                    (
                        SELECT
                            dept_id,
                            dept_code,
                            NAME AS dept_name,
                            ( CASE WHEN dept_code = #{deptId} THEN 1 ELSE 0 END ) AS is_head
                        FROM
                            sys_dept
                        WHERE
                            dept_code LIKE #{deptId}
                           OR dept_code LIKE CONCAT( #{deptId}, '-____' )
                            AND type = 'dept'
                    ) AS dd
                        LEFT JOIN (
                        SELECT
                            sd.dept_code AS dept_code,
                            count( sd.dept_id ) AS count
                        FROM
                            rlc_licence rl
                                LEFT JOIN sys_dept sd ON rl.dept_id = sd.DEPT_ID
                        WHERE 1=1
                        <if test="dateBegin != null "> and rl.create_time &gt;= #{dateBegin} and rl.create_time &lt;= #{dateEnd}</if>
                        GROUP BY
                            sd.dept_code
                    ) AS ll ON ( dd.dept_code = ll.dept_code AND dd.is_head = 1 )
                        OR ( ll.dept_code LIKE CONCAT( dd.dept_code, '%' ) AND dd.is_head = 0 )
                GROUP BY
                    dd.dept_name,
                    dd.dept_id
            ) AS ddll
                LEFT JOIN (
                SELECT
                    dd.dept_name AS deptName,
                    dd.dept_id AS deptId,
                    SUM( pp.count ) AS count,
                    SUM( pp.countRegistered ) AS countRegistered,
                    SUM( pp.countUndergoing ) AS countUndergoing,
                    SUM( pp.countComplete ) AS countComplete
                FROM
                    (
                        SELECT
                            dept_id,
                            dept_code,
                            NAME AS dept_name,
                            ( CASE WHEN dept_code = #{deptId} THEN 1 ELSE 0 END ) AS is_head
                        FROM
                            sys_dept
                        WHERE
                            dept_code LIKE #{deptId}
                           OR dept_code LIKE CONCAT( #{deptId}, '-____' )
                            AND type = 'dept'
                    ) AS dd
                        LEFT JOIN (
                        SELECT
                            ppp.dept_id,
                            sd.dept_code,
                            count( ppp.dept_id ) AS count,
                            SUM( CASE WHEN ppi.is_cons_rec = 'N' THEN 1 ELSE 0 END ) AS countRegistered,
                            SUM( CASE WHEN ppi.is_cons_rec = 'Y' AND ppi.is_finished = 'N' THEN 1 ELSE 0 END ) AS countUndergoing,
                            SUM( CASE WHEN ppi.is_cons_rec = 'Y' AND ppi.is_finished = 'Y' THEN 1 ELSE 0 END ) AS countComplete
                        FROM
                            (
                                SELECT
                                    id,
                                    licence_id,
                                    create_time,
                                    dept_id
                                FROM
                                    rlc_proj_park UNION ALL
                                SELECT
                                    id,
                                    licence_id,
                                    create_time,
                                    dept_id
                                FROM
                                    rlc_proj_wire UNION ALL
                                SELECT
                                    id,
                                    licence_id,
                                    create_time,
                                    dept_id
                                FROM
                                    rlc_proj_pipe UNION ALL
                                SELECT
                                    id,
                                    licence_id,
                                    create_time,
                                    dept_id
                                FROM
                                    rlc_proj_road
                            ) AS ppp
                                LEFT JOIN (
                                SELECT
                                    id,
                                    proj_id,
                                    is_cons_rec,
                                    is_finished
                                FROM
                                    rlc_inspection
                                WHERE
                                    dept_code LIKE CONCAT( #{deptId}, '%' )
                                  AND id IN ( SELECT max( id ) FROM rlc_inspection GROUP BY proj_id )
                            ) AS ppi ON ppp.id = ppi.proj_id
                                LEFT JOIN sys_dept sd on ppp.dept_id = sd.DEPT_ID
                                WHERE 1=1
                                <if test="dateBegin != null "> and ppp.create_time &gt;= #{dateBegin} and ppp.create_time &lt;= #{dateEnd}</if>
                        GROUP BY
                            ppp.dept_id,sd.dept_code
                    ) AS pp ON (
                                   CONCAT( ',', pp.dept_code, ',' ) LIKE CONCAT( '%,', dd.dept_code, ',%' )
                                       AND dd.is_head = 1
                                   )
                        OR ( CONCAT( ',', pp.dept_code ) LIKE CONCAT( '%,', dd.dept_code, '%' ) AND dd.is_head = 0 )
                GROUP BY
                    dd.dept_name,
                    dd.dept_id
            ) AS ddpp ON ddll.deptId = ddpp.deptId
                LEFT JOIN (
                SELECT
                    dd.dept_name AS deptName,
                    dd.dept_id AS deptId,
                    SUM( ii.count ) AS count
                FROM
                    (
                        SELECT
                            dept_id,
                            dept_code,
                            NAME AS dept_name,
                            ( CASE WHEN dept_code = #{deptId} THEN 1 ELSE 0 END ) AS is_head
                        FROM
                            sys_dept
                        WHERE
                            dept_code LIKE #{deptId}
                           OR dept_code LIKE CONCAT( #{deptId}, '-____' )
                            AND type = 'dept'
                    ) AS dd
                        LEFT JOIN ( SELECT dept_code, count( dept_code ) AS count FROM rlc_inspection
                        WHERE 1=1
                        <if test="dateBegin != null "> and create_time &gt;= #{dateBegin} and create_time &lt;= #{dateEnd}</if>GROUP BY dept_code ) AS ii ON ( dd.dept_code = ii.dept_code AND dd.is_head = 1 )
                        OR ( ii.dept_code LIKE CONCAT( dd.dept_code, '%' ) AND dd.is_head = 0 )
                GROUP BY
                    dd.dept_name,
                    dd.dept_code
            ) AS ddii ON ddll.deptId = ddii.deptId
        ORDER BY
            ddll.deptId
    </select>

    <select id="getCountLicenceForBoss" parameterType="RlcParamInterval" resultType="java.util.Map">
        select lic_dept as name, count(lic_dept) as value
        from rlc_licence
        where dept_id = '000_013'
        <if test="dateBegin != null "> and create_time &gt;= #{dateBegin} and create_time &lt;= #{dateEnd}</if>
        group by lic_dept
    </select>

</mapper>
