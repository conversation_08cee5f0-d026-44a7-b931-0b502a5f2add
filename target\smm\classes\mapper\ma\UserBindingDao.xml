<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.ma.dao.UserBindingDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sloth.modules.ma.entity.UserBinding" id="userBindingMap">
        <result property="id" column="ID"/>
        <result property="userId" column="USER_ID"/>
        <result property="maUserId" column="MA_USER_ID"/>
        <result property="lastLoginTime" column="LAST_LOGIN_TIME"/>
        <result property="lastAccessTime" column="LAST_ACCESS_TIME"/>
        <result property="loginStatus" column="LOGIN_STATUS"/>
        <result property="createTime" column="CREATE_TIME"/>
    </resultMap>


</mapper>