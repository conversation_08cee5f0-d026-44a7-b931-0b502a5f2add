<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <include resource="org/springframework/boot/logging/logback/console-appender.xml" />

    <!--自定义转换器-->
    <conversionRule conversionWord="port" converterClass="com.sloth.common.logconverter.PortConverter"></conversionRule>

    <!--日志存储路径-->
    <property name="LOG_PATH" value="logs" />
    <!-- 日志文件名前缀 -->
    <property name="LOG_FILE" value="admin" />
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 日志文件格式 -->
        <encoder>
            <pattern>%date [%port] [%level] [%thread] %logger{60} [%file : %line] %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 正在记录的日志文件路径和名称 -->
        <file>${LOG_PATH}/${LOG_FILE}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--<fileNamePattern>${LOG_PATH}daily/${LOG_FILE}.%d{yyyy-MM-dd}.gz</fileNamePattern>-->
            <fileNamePattern>${LOG_PATH}/${LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>180</maxHistory> <!-- 保留180天 -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>20MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <!--druid日志配置-->
    <appender name="FILE_DRUID" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>%date [%level] %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <file>${LOG_PATH}/druid.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--<fileNamePattern>${LOG_PATH}daily/${LOG_FILE}.%d{yyyy-MM-dd}.gz</fileNamePattern>-->
            <fileNamePattern>${LOG_PATH}/druid.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>180</maxHistory> <!-- 保留180天 -->
        </rollingPolicy>
    </appender>

    <!-- additivity="false" 不向上级（root）传递日志打印行为， -->
    <logger name="com.sloth.common.config.DruidStatLogger" level="DEBUG" additivity="false">
        <!--只记录到文件-->
        <appender-ref ref="FILE_DRUID"/>
    </logger>
    <!-- 通过设置日志级别，控制台打印sql语句-->
    <!--<logger name="com.sloth.modules.cms.dao" level="DEBUG">-->
    <!--<appender-ref ref="CONSOLE"/>-->
    <!--</logger>-->

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>


</configuration>
