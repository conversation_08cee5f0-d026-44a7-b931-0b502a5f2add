@charset "utf-8";
/*
* Copyright https://github.com/larryqin/larrycms
*/
/* 头部 */
.header{height: 65px; border-bottom: 1px solid #404553;  background-color: #393D49; color: #fff;}
.logo{display:block;position: absolute; left: 10px; top: 10px;}
.logo img{width: 150px; height: 35px;}

.header .layui-nav{position: absolute; right: 0; top: 0; padding: 0; background: none;}
.header .layui-nav .layui-nav-item{margin: 0 20px; line-height: 66px;}

.body{display: block;}
.layui-layout-admin .header-demo{border-bottom-color: #1AA094;}
.header-demo .logo{font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;left: 10px;top: 8px;font-size: 24px;font-weight:bold; color:#fff;height: 50px;line-height: 50px;text-align: center;}
.header-demo .layui-nav{top: 0;}
.header-demo .layui-nav .layui-nav-item{margin: 0 10px; line-height: 70px;}
.header-demo .layui-nav .layui-nav-item a{
	color: #999;
	padding-left: 15px;
	padding-right: 15px;
	position: relative;
}
.header-demo .layui-nav .layui-nav-item:hover{
	border-bottom-color: #000;
}
.header-demo .layui-nav .layui-this{background-color: #000;/* #5FB878 */}
.header-demo .layui-nav .layui-nav-item a:hover,
.header-demo .layui-nav .layui-this a{color: #ffffff;}
.header-demo .layui-nav .layui-nav-item span.layui-nav-more{
	top: 32px;
}
.header-demo .layui-nav .layui-nav-item span.layui-nav-mored{
	top: 26px;
}
.header-demo .layui-nav .layui-this:after,
.header-demo .layui-nav-bar{background-color: #393D49;}
.header-demo .layui-nav .layui-this a{padding: 0 15px;}
.header{
	background: #393D49;
}
.admin-logo-box{
	width: 185px; height: 70px; position: relative;
}
.larry-side-menu{
	position: absolute;
	cursor: pointer;
    z-index: 19861219;
    left: 200px;
    color: white;
    text-align: center;
    width: 30px;
    height: 30px;
    background-color: #1AA094;
    line-height: 30px;
    top: 23%;
}
.larry-side-menu:hover{
	background-color: #5FB878;
}
/* 头部左侧 */
.layui-larry-menu{
	width: auto;
	height: 70px;
	position: absolute;
	left: 245px;
	top: 0px;
}
.layui-larry-menu ul.layui-nav{
	height: 70px;
	position:static;
}
.layui-larry-menu ul.layui-nav li.layui-nav-item{
	margin: 0px;
}
.layui-larry-menu ul.layui-nav li.layui-nav-item a{
	color: #F5F5F5;
	font-size: 14px;
	line-height: 60px;
	padding-left: 10px;
	padding-right: 10px;
}
.layui-larry-menu ul.layui-nav li.layui-nav-item a i{
	padding-right: 5px;
	line-height: 70px;
}
.layui-larry-menu ul.layui-nav .layui-this{
	background: #2B2E37;

}
.layui-larry-menu ul.layui-nav .layui-this::after{
	/* background: #2B2E37; */
	background: #1AA094;
}
/* 头部右侧 */
.header ul.larry-header-item{
	height: 70px;
}
.header ul.larry-header-item li.layui-nav-item{
	margin-left: 0px;
	margin-right: 0px;
}
.header ul.larry-header-item .userimg{
	width: 40px;
	border-radius: 50%;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border: 4px solid #44576b;
	margin-top: -6px;
}
.header ul.larry-header-item .layui-this{
	background: #393D49;
	border-bottom: none;
}
.header ul.larry-header-item .layui-this::after{
	background: transparent;
}
/* ======================= */
/* 左侧区域 */
.layui-side-bg{
	background: #393D49;
}
.layui-larry-side{
	padding: 0px;
}
.layui-larry-side .user-photo{
	width: 200px;
	height: 120px;
	padding-top: 15px;
	padding-bottom: 5px;
}
.layui-larry-side  .user-photo a.img{
    display: block;
    width: 76px;
    height: 76px;
    margin: 0 auto;
    margin-bottom: 15px;
}
.layui-larry-side  .user-photo a.img img{
	display: block;
	border: none;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border: 4px solid #44576b;
}
.layui-larry-side  .user-photo p{
	display: block;
	width: 100%;
	height: 25px;
	color: #ffffff;
	text-align: center;
	font-size: 12px;
	white-space: nowrap;
	line-height: 25px;
	overflow: hidden;
}
.layui-nav-tree li.layui-nav-item a{
	padding-right: 10px;
}
.layui-nav-tree li.layui-nav-item a i{
	padding-right: 3px;
}
.layui-nav-tree li.layui-nav-item .layui-nav-child dd a{
	padding-left: 40px !important;
	padding-right: 0px;
}
.layui-nav-tree li.layui-nav-item .layui-nav-child dd a i{
	padding-right: 3px;
}
/* 右侧主体区域 */
.layui-body{
	bottom: 0;
	border-left: solid 2px #1AA094;
}
.layui-tab-content{
	min-height: 150px; 
	/* padding: 5px 0 0 0; */
}
 /* tab选项卡 */
.layui-body{
 	padding-left: 0px;
 }
 .layui-layout-admin .layui-body {
    top: 58px;
}

.larry-tab-menu{
	width: 200px;
	height: 40px;
	display: none;
}
.larry-tab-box .layui-tab-title li em{
   padding-left: 5px;
   font-style: normal;
}
#admin-home i.layui-icon{
	display: none;
}
.larry-tab-box{
	box-shadow:none;
}
/* layui-tab-title start */
.layui-tab-title .larry-tab-menu{
	display: inline-block;
	float: right;border: 1px solid red;
	position: relative;
	font-size: 14px;
}
.larry-test{
	
	height: 40px;
	color: #000;
	position: absolute;
	top: 0px;
	left: 0px;
	font-size: 16px;
	color: #ffffff;
}
.refresh_iframe{
	display: none;
	position: absolute;
	top: 10px;
	right: 1px;
	height: 41px;
	background: #33AB9F;
	color: #FFFFFF;
}

/* layui-tab-title end */
.layui-tab-content{
	padding: 0px;
	margin: 0px;
}
.layui-tab-item{
	padding: 10px 0 0 10px;
	margin: 0px;

}
.layui-tab-content .layui-tab-item iframe{
	width: 100%;
	border: 0;
	height: 100%;
}
.larry-tab-box>.layui-tab-title{
	border-bottom: 1px solid #1AA094;
}
.larry-tab-box .layui-tab-title cite{
	font-style: normal;
	padding-left: 5px;
}
.larry-tab-box>.layui-tab-title .layui-this{
	color: white;
    background-color: #1AA094;
}
.larry-tab-box>.layui-tab-title .layui-this:after{
	border-bottom: 0;
}

#time{
	width:100%;
	color:#fff;
	font-size:60px;
	margin-bottom:80px;
	display:inline-block;
	text-align:center;
}
.form-group {
    margin-bottom: 15px;
}
.col-lg-12{
	position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
}

::selection{background:#FF6C60;color:#fff;}
.form-control {
    height: 30px;
}
@media (min-width: 1200px){
	.col-lg-12 {
	    width: 100%;
	    float: left;
	}
}

body .layui-layout-admin .layui-larry-foot{
    height: 30px;
    padding-left:10px;
    line-height: 30px;
    background-color: #eee;
    color: #666;
    font-weight: 300;
    border-left: 2px solid #1AA094;
    z-index: 998;
}
body .layui-layout-admin .layui-larry-foot a{
    padding: 0 5px;

}

.layui-form-item .layui-input-inline {
    float: left;
    width: 350px;
    margin-right: 10px;
}

.laber-account{
	padding:3px 6px;margin-top:8px;width:auto;border-radius:5px;text-align: left;background-color:#009688; color: #fff;
}
.text-danger{
	color:#a94442
}
.normal-label{
	display:none;
}

