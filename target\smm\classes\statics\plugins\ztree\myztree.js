/**
 * ztree封装，效果：单击input对应的输入框，在输入框下展示treedom对应的ztree
 * treedom指加载ztree的ul元素，外层应有一个div元素
 * html示例：<input type="text" name="policyTagName" id="policyTagName" readonly="readonly">
 *     <div id="tagTreeDiv"  style="display:none; position: absolute;z-index:99999;">
           <ul id="tagTree" class="ztree" style="margin-top:0;border: 1px solid #617775;background: #f0f6e4;width:180px;height:200px;overflow-y:scroll;overflow-x:auto;"></ul>
       </div>
 * js示例：var tagTree;
 * var tagTreeSetting={
        data: {
            simpleData: {
                enable: true,
                idKey: "id",
                pIdKey: "pId",
                // rootPId: "-1"
            },
        },
        callback:{
            onClick: zTreeOnClick,
            beforeClick: zTreeBeforeCheck
        }
    }
 $(function() {
        $.get("${base}/policy/article/article_tagTree.do", function (data) {
            tagTree =$.fn.myZTree($("#tagTree"), tagTreeSetting, data,$("#policyTagName"));
            var node = tagTree.getNodeByParam("id", "${policyTagId!''}");
            if (node != null && typeof (node) != "undefined") {
                $("#policyTagName").val(node.name);
                $("#FpolicyTagId").val(node.id);
            }
            $("#policyTagName").click(function () {
            tagTree.showTree();
            })
        });

	});
 function zTreeOnClick(event, treeId, treeNode) {

    }
 function zTreeBeforeCheck(treeId, treeNode) {

    }
 * @param input 要展示ztree的input输入框
 * @param setting ztree的setting
 * @param data ztree的list数据
 * @param treeDom ztree的隐藏域DOM
 */
$.fn.myZTree = function (treeDom, setting, data, input) {
    var mytree = $.fn.zTree.init(treeDom, setting, data);
    var parent = $(treeDom).parent();
    jQuery.prototype.showTree = null;
    jQuery.prototype.hiddenTree = null;
    jQuery.prototype.onBodyDown = null;
    mytree.showTree = function () {
        var inputOffset = $(input).offset();
        $(parent).css({
            left: inputOffset.left + "px",
            top: inputOffset.top + input.outerHeight() + "px"
        }).slideDown("fast");
        $("body").bind("mousedown", mytree.onBodyDown);
    }
    mytree.hiddenTree = function () {
        $(parent).fadeOut("fast");
        $("body").unbind("mousedown", mytree.onBodyDown);
    }
    mytree.onBodyDown = function () {
        var parentId = $(parent).attr("id");
        if (!(event.target.id == $(input).attr("id") || event.target.id == parentId
            || $(event.target).parents("#"+parentId).length > 0)) {
            mytree.hiddenTree();
        }
    }
    return mytree;
};