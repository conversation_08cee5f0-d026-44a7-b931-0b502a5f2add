<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.project.dao.ProjectConfigDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.project.entity.ProjectConfig" id="projectConfigMap">
        <result property="deptId" column="DEPT_ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="leaderAuthDuration" column="LEADER_AUTH_DURATION"/>
        <result property="supervisorAuthDuration" column="SUPERVISOR_AUTH_DURATION"/>
        <result property="authorityAuthDuration" column="AUTHORITY_AUTH_DURATION"/>
    </resultMap>


</mapper>