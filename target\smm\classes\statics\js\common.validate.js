

/* *
 * js工具函数库
 * By: yeshujun
 * Version : 1.0
*/
(function ($) {
    $.extend({
        /**
         * 1、浮点数校验
         */
        isNumber: function (num) {
            return /^-?(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d+)?$/.test(num);
        },

        /**
         * 2、整数校验
         */
        isDigit: function (num) {
            return /^((-?[1-9]\d*)|0)$/.test(num);
        },

        /**
         * 3、email校验
         */
        isEmail: function (email) {
            return /^\b[A-Z0-9._%-]+@[A-Z0-9.-]+\.[A-Z]{2,4}\b$/i.test(email);
        },
        /**
         * 4、数字串校验，用于电话号码，银行卡号等
         */
        isNumStr: function (num) {
            return /^\d+$/.test(num);
        }

    })
})(jQuery);

/**
 * 身份证号验证
 */
var Wi = [ 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1 ];    // 加权因子
var ValideCode = [ 1, 0, 10, 9, 8, 7, 6, 5, 4, 3, 2 ];            // 身份证验证位值.10代表X
function idCardValidate(idCard) {
    idCard = trim(idCard);               //去掉字符串头尾空格
    if (idCard.length == 15) {
        //return isValidityBrithBy15IdCard(idCard);       //进行15位身份证的验证
        return false; // 15位身份证号视为无效
    } else if (idCard.length == 18) {
        var a_idCard = idCard.split("");                // 得到身份证数组
        if(isValidityBrithBy18IdCard(idCard)&&isTrueValidateCodeBy18IdCard(a_idCard)){   //进行18位身份证的基本验证和第18位的验证
            return true;
        }else {
            return false;
        }
    } else {
        return false;
    }
}
/**
 * 判断身份证号码为18位时最后的验证位是否正确
 * @param a_idCard 身份证号码数组
 * @return
 */
function isTrueValidateCodeBy18IdCard(a_idCard) {
    var sum = 0;                             // 声明加权求和变量
    if (a_idCard[17].toLowerCase() == 'x') {
        a_idCard[17] = 10;                    // 将最后位为x的验证码替换为10方便后续操作
    }
    for ( var i = 0; i < 17; i++) {
        sum += Wi[i] * a_idCard[i];            // 加权求和
    }
    valCodePosition = sum % 11;                // 得到验证码所位置
    if (a_idCard[17] == ValideCode[valCodePosition]) {
        return true;
    } else {
        return false;
    }
}
/**
 * 验证18位数身份证号码中的生日是否是有效生日
 * @param idCard 18位书身份证字符串
 * @return
 */
function isValidityBrithBy18IdCard(idCard18){
    var year =  idCard18.substring(6,10);
    var month = idCard18.substring(10,12);
    var day = idCard18.substring(12,14);
    var temp_date = new Date(year,parseFloat(month)-1,parseFloat(day));
// 这里用getFullYear()获取年份，避免千年虫问题
    if(temp_date.getFullYear()!=parseFloat(year)
        ||temp_date.getMonth()!=parseFloat(month)-1
        ||temp_date.getDate()!=parseFloat(day)){
        return false;
    }else{
        return true;
    }
}
/**
 * 验证15位数身份证号码中的生日是否是有效生日
 * @param idCard15 15位书身份证字符串
 * @return
 */
function isValidityBrithBy15IdCard(idCard15){
    var year =  idCard15.substring(6,8);
    var month = idCard15.substring(8,10);
    var day = idCard15.substring(10,12);
    var temp_date = new Date(year,parseFloat(month)-1,parseFloat(day));
// 对于老身份证中的你年龄则不需考虑千年虫问题而使用getYear()方法
    if(temp_date.getYear()!=parseFloat(year)
        ||temp_date.getMonth()!=parseFloat(month)-1
        ||temp_date.getDate()!=parseFloat(day)){
        return false;
    }else{
        return true;
    }
}
//去掉字符串头尾空格
function trim(str) {
    return str.replace(/(^\s*)|(\s*$)/g, "");
}

/**
 * 通过身份证判断是男是女
 * @param idCard 15/18位身份证号码
 * @return 'female'-女、'male'-男
 */
function maleOrFemalByIdCard(idCard){
    idCard = trim(idCard);        // 对身份证号码做处理。包括字符间有空格。
    if(idCard.length==15){
        if(idCard.substring(14,15)%2==0){
            return 'female';
        }else{
            return 'male';
        }
    }else if(idCard.length ==18){
        if(idCard.substring(14,17)%2==0){
            return 'female';
        }else{
            return 'male';
        }
    }else{
        return null;
    }
}

/**
 * 统一社会信用代码校验
 */

function checkSocialCreditCode(Code) {
    var patrn = /^[0-9A-Z]+$/;
    //18位校验及大写校验
    if ((Code.length != 18) || (patrn.test(Code) == false)) {
        return false;
    } else {
        var Ancode;//统一社会信用代码的每一个值
        var Ancodevalue;//统一社会信用代码每一个值的权重
        var total = 0;
        var weightedfactors = [1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28];//加权因子
        var str = '0123456789ABCDEFGHJKLMNPQRTUWXY';
        //不用I、O、S、V、Z
        for (var i = 0; i < Code.length - 1; i++) {
            Ancode = Code.substring(i, i + 1);
            Ancodevalue = str.indexOf(Ancode);
            total = total + Ancodevalue * weightedfactors[i];
            //权重与加权因子相乘之和
        }
        var logiccheckcode = 31 - total % 31;
        if (logiccheckcode == 31) {
            logiccheckcode = 0;
        }
        var Str = "0,1,2,3,4,5,6,7,8,9,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,T,U,W,X,Y";
        var Array_Str = Str.split(',');
        logiccheckcode = Array_Str[logiccheckcode];
        var checkcode = Code.substring(17, 18);
        if (logiccheckcode != checkcode) {
            return false;
        }
        return true;
    }
}




/*  form 校验
		1、	rules="[
			    {notNull:true, message:'姓名不能为空'},
			    {isNumStr:true,message:'只能是数字'},//数字串校验，用于电话号码，银行卡号等
			    {isNumber:true, message:'只能是数字'},//浮点数
			    {isDigit:true, message:'只能是整数'},
				{isEmail:true,message:'电子邮件格式不正确'},
				{isIdCard:true,message:'身份证号无效'},
				{minLength:6,message:'帐号长度至少为6'},
				{maxLength:6,message:'帐号长度最多为6'},
				{minValue:0,message:'年龄最小值为0'},
				{maxValue:100,message:'年龄最大值为100'},
				{equalWith:'newPassword',message:'确认密码需等于新密码'},
				{greaterThan:'startDate',message:'结束日期需大于开始日期'},
				{lessThan:'endDate',message:'开始日期需小于结束日期'},
				{notGreaterThan:'endDate',message:'开始日期需不大于结束日期'},
				{notLessThan:'startDate',message:'结束日期需不小于开始日期'},
				{notGreaterThanNum:'jihua',message:'竣工数需不大于计划数'},
				{notLessThanNum:'jungong',message:'计划数需不小于竣工数'},
				{greaterThanNum:'jungong',message:'计划数需大于竣工数'},
				{lessThanNum:'jungong',message:'计划数需小于竣工数'},
				{regExp:/^-?(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d+)?$/,message:'自定义正在表达式验证'},
				{regExp:/^[^\<|\>]{0,}$/,message:'用户名不可包含<或>'}，
				{regExp:/^[^\u4e00-\u9fa5]{0,}$/,message:'用户名不可包含汉字'},
			]"
		2、如果是radio和checkbox，请创建一个hidden域，动态赋值再校验
		3、需校验的text、password、hidden、textarea、radio、checkbox，number加上id属性
	*/
function validateForm(formId){
    //是否验证通过
    var valide = true;
    $("#"+formId).find(":input[type='number'],:input[type='text'],:input[type='password'],:input[type='hidden'],:input[type='file'],select,textarea").each(function(){
        if(!validateInput($(this))){
            valide=false;
        }
    });
    // if(valide){
    //     $(":button,:submit,:reset").attr("disabled","disabled");
    // }
    if (!valide) {
        //验证不通过，第一个元素获取焦点
        var id = $($(".error-label:visible")[0]).attr("for");
        var input = $("#" + id);
        if (input.length == 0) {
            input = $("[name='" + id + "']");
        }
        $(input).focus();
    }
    return valide;
}

/**
 * 单个input校验
 * @param inputId input控件Id
 * @returns {boolean} 验证是否通过
 */
function validateInputById(inputId){
    return validateInput($("#"+inputId));
}
/**
 *<li>功能描述：表单即时校验</li>
 * @params: 都好分隔的input id
 * @author: <EMAIL>
 * @date:  2019/1/27 10:43
 */
function validateInputs(ids) {
    if(ids!=undefined&&ids!=""){
        ids=ids.replace("，",",")
        for (var i=0;i<ids.split(",").length;i++){
            var id=ids.split(",")[i];
            if ($("#" + id).is('select')) {
                $("#"+id).change(function () { validateInput($(this))});
            }else{
                $("#"+id).keyup(function () { validateInput($(this))});
            }


        }
    }
}
/**
 * 单个input校验
 * @param e input控件
 * @returns {boolean} 验证是否通过
 */
function validateInput(e){
    var val = $(e).val();
    var isDisabled = $(e).attr("disabled");
    //验证规则
    var rules = $(e).attr("rules");
    if(rules!="undefined" && typeof(rules)!="undefined" && !isDisabled){
        //alert(val+"  "+rules);
        //转化成json
        rules = (new Function("return " + rules))();
        if(rules!=null){
            var labelId = $(e).attr("id");
            if(typeof(labelId)==="undefined"){labelId = $(e).attr("name");}
            if(typeof(labelId)==="undefined"){
                labelId=Math.random()*10000000;
                $(e).attr("id",labelId);
            }
            if($("label[for='"+labelId+"']").length==0){
                $(e).after("<label for='"+labelId+"' class='error-label text-danger'></label>");
            }
            $("label[for='"+labelId+"']").toggleClass("normal-label", true).html("");
            for(var i = 0; i < rules.length; i++) {
                var rule = rules[i];
                if(rule.notNull) {
                    if(val == null || /^\s*$/i.test(val)) {//非空
                        if(typeof(rule.message)!="undefined"){
                            $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                            $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        }
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                }else if(rule.isNumber) {//只能是浮点数
                    if(!$.isNumber(val) && val!="") {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                }else if(rule.isNumStr) {//只能是数字串
                    if(!$.isNumStr(val) && val!="") {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                } else if(rule.isDigit) {//只能是整数
                    if(!$.isDigit(val) && val!="") {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                }  else if(rule.isEmail) {//email
                    if(!$.isEmail(val) && val!="") {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                } else if(rule.isIdCard) {
                    if(val!="" && !idCardValidate(val)) {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                } else if(rule.minLength) {//最小长度
                    if((val.length < rule.minLength) && val!="") {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                } else if(rule.maxLength) {//最大长度
                    if((val.length > rule.maxLength)&& val!="") {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                } else if(rule.minValue != undefined) {//最小值
                    var test = parseFloat(val.replace(/,/g,""));
                    if((!isNaN(test) && test < rule.minValue) && val!="") {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                } else if(rule.maxValue != undefined) {//最大值
                    var test = parseFloat(val.replace(/,/g,""));
                    if((!isNaN(test) && test > rule.maxValue) && val!="") {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                }
                else if(rule.equalWith) {
                    var compareNode = rule.equalWith;
                    if(compareNode!="" && val != $("#"+compareNode).val()) {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                }
                else if(rule.greaterThan) {
                    var compareNode = rule.greaterThan;
                    if(!/^\s*$/i.test(val) && compareNode!="" && val <= $("#"+compareNode).val()) {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                }
                else if(rule.lessThan) {
                    var compareNode = rule.lessThan;
                    if(!/^\s*$/i.test(val) && compareNode!="" && $("#"+compareNode).val()!="" && val >= $("#"+compareNode).val()) {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                }
                else if(rule.notGreaterThan) {
                    var compareNode = rule.notGreaterThan;
                    if(!/^\s*$/i.test(val) && compareNode!="" && $("#"+compareNode).val()!="" && val > $("#"+compareNode).val()) {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                }
                else if(!/^\s*$/i.test(val) && rule.notLessThan) {
                    var compareNode = rule.notLessThan;
                    if(compareNode!="" && $("#"+compareNode).val()!="" && val < $("#"+compareNode).val()) {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                }
                else if(rule.lessThanNum) {
                    var compareNode = rule.lessThanNum;
                    if(!/^\s*$/i.test(val)&&$.isNumber(val) && compareNode!="" && $("#"+compareNode).val()!="" && parseFloat(val) >= parseFloat($("#"+compareNode).val())) {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                }
                else if(rule.greaterThanNum) {
                    var compareNode = rule.greaterThanNum;
                    if(!/^\s*$/i.test(val)&&$.isNumber(val) && compareNode!="" && $("#"+compareNode).val()!="" && parseFloat(val) <= parseFloat($("#"+compareNode).val())) {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                }
                else if(rule.notGreaterThanNum) {
                    var compareNode = rule.notGreaterThanNum;
                    if(!/^\s*$/i.test(val)&&$.isNumber(val) && compareNode!="" && $("#"+compareNode).val()!="" && parseFloat(val) > parseFloat($("#"+compareNode).val())) {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                }
                else if(rule.notLessThanNum) {
                    var compareNode = rule.notLessThanNum;
                    if(!/^\s*$/i.test(val)&&$.isNumber(val) && compareNode!="" && $("#"+compareNode).val()!="" && parseFloat(val) < parseFloat($("#"+compareNode).val())) {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                }
                else if(rule.regExp) {//自定义正则表达式
                    if(!rule.regExp.test(val) && val!="") {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                } else if(rule.fn) {
                    if(!rule.fn.call(e, val)) {
                        $("label[for='"+labelId+"']").removeClass("normal-label").addClass("error-label text-danger");
                        $("label[for='"+labelId+"']").html("").html("&nbsp;&nbsp;"+rule.message);
                        return false;
                    }else{
                        $("label[for='"+labelId+"']").html("");
                    }
                }
            }
            if($("label[for='"+labelId+"']").length!=0){
                $("label[for='"+labelId+"']").removeClass("error-label text-danger").addClass("normal-label");
            }
        }
    }
    return true;
}

$(function () {
    /**
     * 对含有rules属性的控件绑定动态，在支持html5规范的浏览器下用oninput，
     * 不支持的用onpropertychange
     */
    var testInput = document.createElement('input');
    if ('oninput' in testInput) {
        $("body").on("change input","input[rules*='message']",function () {
            validateInput(this);
        });
        $("body").on("change input","textarea[rules*='message']",function () {
            validateInput(this);
        });
        $("body").on("change","select[rules*='message']",function () {
            validateInput(this);
        });
    } else {
        $("body").on("change propertychange","input[rules*='message']",function () {
            validateInput(this);
        });
        $("body").on("change propertychange","textarea[rules*='message']",function () {
            validateInput(this);
        });
        $("body").on("change","select[rules*='message']",function () {
            validateInput(this);
        });
    }

})