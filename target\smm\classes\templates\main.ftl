<#import "/macro/macro.ftl" as my />
<!DOCTYPE html>
<html>
<@my.head jqgrid=true component=true vueValidate=true elementUI=true ztree=true uploader=true ueditor=true layDate=true>
    <title>欢迎页</title>
    <style type="text/css">
        table tr>th{
            font-weight: bold;
        }
        table td{
            text-align: left;
        }
    </style>
</@my.head>
<body >
<div id="app" class="panel panel-default">
    <div class="panel-heading">系统信息</div>
    <div class="panel-body">
        <table class="table table-bordered table-hover">
           <tr v-for="(item,index) in list" v-if="index%2==0">
               <th>{{item.name}}</th>
               <td>{{item.value}}</td>
               <th v-if="index+1<list.length">{{list[index+1].name}}</th>
               <td v-if="index+1<list.length">{{list[index+1].value}}</td>
           </tr>
        </table>
    </div>
</div>
<script type="text/javascript">
    var vm=new Vue({
        el:'#app',
        data:{
            list:[],
        },
        methods:{
            getInfo:function () {
                $.post("${request.contextPath}/sys/info", function (r) {
                    if (r.code == 0) {
                        vm.list = r.list;
                    }else{
                        alert(r.msg);
                    }

                });
            }
        },
        mounted:function () {
            this.getInfo();
        }
    })
</script>
</body>
</html>