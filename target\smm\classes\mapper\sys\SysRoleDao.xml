<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.sys.dao.SysRoleDao">


    <select id="queryRoleListByUserId" resultType="com.sloth.modules.sys.entity.SysRoleEntity">
        select sr.*
        from sys_role sr
        left join sys_user_role sur on sr.role_id =sur.role_id
        where sur.user_id=#{userId}
    </select>

    <select id="queryRoleListBydeptId" resultType="com.sloth.modules.sys.entity.SysRoleEntity">
        SELECT
            DISTINCT role.ROLE_ID, role.ROLE_NAME
        FROM
            sys_role role
        LEFT JOIN sys_role_dept roleDept ON roleDept.ROLE_ID = role.ROLE_ID
        WHERE
        (roleDept.DEPT_ID IN (
        WITH RECURSIVE dept AS (
        SELECT a.*
        FROM sys_dept a
        WHERE a.DEPT_ID = #{deptId}
        <if test="deptType!=null and deptType!=''">
            and a.TYPE=#{deptType}
        </if>
        UNION ALL
        SELECT b.*
        FROM sys_dept b
        INNER JOIN dept c ON c.PARENT_ID = b.DEPT_ID
        <if test="deptType!=null and deptType!=''">
            and b.TYPE=#{deptType}
        </if>
        ) SELECT DEPT_ID
        FROM dept where DEPT_ID &lt;&gt; #{deptId} ) and role.SUB_SHARED = 1 ) or (roleDept.DEPT_ID = #{deptId})
    </select>
    <select id="queryAuthViewRolesByDeptId" resultType="com.sloth.modules.sys.entity.SysRoleEntity">
        SELECT
            DISTINCT role.ROLE_ID, role.ROLE_NAME
        FROM
            sys_role role
                LEFT JOIN sys_role_dept roleDept ON roleDept.ROLE_ID = role.ROLE_ID
        WHERE
            (roleDept.DEPT_ID IN (
                WITH RECURSIVE dept AS (
                    SELECT a.*
                    FROM sys_dept a
                    WHERE a.DEPT_ID = #{deptId}
                    UNION ALL
                    SELECT b.*
                    FROM sys_dept b
                             INNER JOIN dept c ON c.PARENT_ID = b.DEPT_ID
                ) SELECT DEPT_ID
                FROM dept where DEPT_ID &lt;&gt; #{deptId} ) and role.SUB_SHARED = 1 )
           or (roleDept.DEPT_ID = #{deptId})
           or roleDept.DEPT_ID in (
               select DEPT_ID from sys_dept where DEPT_CODE like concat(#{deptCode},"%")
            )

    </select>
</mapper>
