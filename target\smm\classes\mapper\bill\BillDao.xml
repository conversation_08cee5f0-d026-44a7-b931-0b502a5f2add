<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.bill.dao.BillDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.bill.entity.Bill" id="billMap">
        <result property="id" column="ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="billNo" column="BILL_NO"/>
        <result property="projectId" column="PROJECT_ID"/>
        <result property="carNumber" column="CAR_NUMBER"/>
        <result property="carCompany" column="CAR_COMPANY"/>
        <result property="carBrand" column="CAR_BRAND"/>
        <result property="carOwnerName" column="CAR_OWNER_NAME"/>
        <result property="carKerbWeight" column="CAR_KERB_WEIGHT"/>
        <result property="carMaxPayload" column="CAR_MAX_PAYLOAD"/>
        <result property="driverName" column="DRIVER_NAME"/>
        <result property="driverMobile" column="DRIVER_MOBILE"/>
        <result property="vehicleLoad" column="VEHICLE_LOAD"/>
        <result property="destination" column="DESTINATION"/>
        <result property="destLongitude" column="DEST_LONGITUDE"/>
        <result property="destLatitude" column="DEST_LATITUDE"/>
        <result property="estimatedArrivalTime" column="ESTIMATED_ARRIVAL_TIME"/>
        <result property="destLeader" column="DEST_LEADER"/>
        <result property="destLeaderMobile" column="DEST_LEADER_MOBILE"/>
        <result property="signTime" column="SIGN_TIME"/>
        <result property="auditTime" column="AUDIT_TIME"/>
        <result property="status" column="STATUS"/>
        <result property="driverSignatureId" column="DRIVER_SIGNATURE_ID"/>
        <result property="projectLeaderSignatureId" column="PROJECT_LEADER_SIGNATURE_ID"/>
        <result property="supervisorSignatureId" column="SUPERVISOR_SIGNATURE_ID"/>
        <result property="authoritySignatureId" column="AUTHORITY_SIGNATURE_ID"/>
        <result property="authorityId" column="AUTHORITY_ID"/>
        <result property="verifyCancelUserId" column="VERIFY_CANCEL_USER_ID"/>
        <result property="verifyCancelTime" column="VERIFY_CANCEL_TIME"/>
        <result property="verifyCancelLongitude" column="VERIFY_CANCEL_LONGITUDE"/>
        <result property="verifyCancelLatitude" column="VERIFY_CANCEL_LATITUDE"/>
        <result property="distance" column="DISTANCE"/>
    </resultMap>
    <insert id="add">
        INSERT INTO transport_document (
            doc_number, payload_type, creator_name, creator_department, create_time,
            create_location, mining_type, trans_type, doc_status, mining_license_no,
            approval_no, mining_area_type, mining_area, dredging_area, mining_area_scope,
            section_name, belong_watershed, mining_total_limit, mining_equip_name,
            mining_charge_company, mining_charge_person, mining_charge_person_phone,
            trans_ship_name, transport_cert_no, ship_inspect_register_no, trans_car_brand_no,
            trans_car_no, actual_load, to_dock, charge_person, charge_person_contact_no,
            charge_person_id_no, ais_code, gps_no, start_time, end_time, supervisor_phone,
            reviewer, review_type, review_time, report_phone, hotline, mining_river,
            region, region_code, dock_name, remark, audit_department, department_id,
            sid, s_doc_number, ssxt, mining_company_name,push_status
        )VALUES  <foreach item="item" index="index" collection="list" separator=",">
        (#{item.docNumber}, #{item.payloadType}, #{item.creatorName}, #{item.creatorDepartment}, #{item.createTime},
        #{item.createLocation}, #{item.miningType}, #{item.transType}, #{item.docStatus}, #{item.miningLicenseNo},
        #{item.approvalNo}, #{item.miningAreaType}, #{item.miningArea}, #{item.dredgingArea}, #{item.miningAreaScope},
        #{item.sectionName}, #{item.belongWatershed}, #{item.miningTotalLimit}, #{item.miningEquipName},
        #{item.miningChargeCompany}, #{item.miningChargePerson}, #{item.miningChargePersonPhone},
        #{item.transShipName}, #{item.transportCertNo}, #{item.shipInspectRegisterNo}, #{item.transCarBrandNo},
        #{item.transCarNo}, #{item.actualLoad}, #{item.toDock}, #{item.chargePerson}, #{item.chargePersonContactNo},
        #{item.chargePersonIdNo}, #{item.aisCode}, #{item.gpsNo}, #{item.startTime}, #{item.endTime}, #{item.supervisorPhone},
        #{item.reviewer}, #{item.reviewType}, #{item.reviewTime}, #{item.reportPhone}, #{item.hotline}, #{item.miningRiver},
        #{item.region}, #{item.regionCode}, #{item.dockName}, #{item.remark}, #{item.auditDepartment}, #{item.departmentId},
        #{item.sid}, #{item.sDocNumber}, #{item.ssxt}, #{item.miningCompanyName} ,#{item.pushStatus} )
             </foreach>
    </insert>
    <insert id="save">
        INSERT INTO sand_purchase_docs (
            doc_number,
            create_location,
            buy_dock_name,
            buy_dock_address,
            buyer_name,
            charge_person,
            charge_person_contact_no,
            charge_person_id_no,
            supervisor_phone,
            report_phone,
            hot_line,
            region,
            remark,
            s_doc_number,
            create_record_account,
            creator_name,
            creator_department,
            create_time,
            doc_status,
            belong_watershed,
            source_doc_no1,
            source_doc_no2,
            source_doc_no3,
            weight1,
            weight2,
            weight3,
            buy_weight,
            buy_time,
            reviewer,
            review_type,
            review_time,
            region_code,
            ssxt,
            department_id,
            sid
        ) VALUES (
                     #{docNumber},
                     #{createLocation},
                     #{buyDockName},
                     #{buyDockAddress},
                     #{buyerName},
                     #{chargePerson},
                     #{chargePersonContactNo},
                     #{chargePersonIdNo},
                     #{supervisorPhone},
                     #{reportPhone},
                     #{hotline},
                     #{region},
                     #{remark},
                     #{sDocNumber},
                     #{createRecordAccount},
                     #{creatorName},
                     #{creatorDepartment},
                     #{createTime},
                     #{docStatus},
                     #{belongWatershed},
                     #{sourceDocNo1},
                     #{sourceDocNo2},
                     #{sourceDocNo3},
                     #{weight1},
                     #{weight2},
                     #{weight3},
                     #{buyWeight},
                     #{buyTime},
                     #{reviewer},
                     #{reviewType},
                     #{reviewTime},
                     #{regionCode},
                     #{ssxt},
                     #{departmentId},
                     #{sid}
                 );



    </insert>
    <update id="updatetransport">

        UPDATE transport_document
        SET push_status = '1'
        WHERE doc_number = #{docNumber};

    </update>
    <delete id="deleteTransportDocument">

        DELETE FROM transport_document WHERE doc_number = #{docNumber} and push_status = '0'

    </delete>

    <select id="selectBillPage" resultType="com.ybkj.smm.modules.bill.entity.Bill">
        select
        bill.id,
        bill.CREATE_USER_ID,
        bill.UPDATE_USER_ID,
        bill.CREATE_TIME,
        bill.UPDATE_TIME,
        bill.PROJECT_ID,
        bill.BILL_NO,
        bill.CAR_NUMBER,
        bill.CAR_COMPANY,
        bill.CAR_BRAND,
        bill.CAR_OWNER_NAME,
        bill.CAR_KERB_WEIGHT,
        bill.CAR_MAX_PAYLOAD,
        bill.DRIVER_NAME,
        bill.DRIVER_MOBILE,
        bill.VEHICLE_LOAD,
        bill.DESTINATION,
        bill.DEST_LONGITUDE,
        bill.DEST_LATITUDE,
        bill.ESTIMATED_ARRIVAL_TIME,
        bill.DEST_LEADER,
        bill.DEST_LEADER_MOBILE,
        bill.SIGN_TIME,
        bill.AUDIT_TIME,
        bill.STATUS,
        bill.DRIVER_SIGNATURE_ID,
        bill.PROJECT_LEADER_SIGNATURE_ID,
        bill.SUPERVISOR_SIGNATURE_ID,
        bill.AUTHORITY_SIGNATURE_ID,
        bill.AUTHORITY_ID,
        bill.VERIFY_CANCEL_USER_ID,
        bill.VERIFY_CANCEL_TIME,
        bill.VERIFY_CANCEL_LONGITUDE,
        bill.VERIFY_CANCEL_LATITUDE,
        bill.DISTANCE,
        project.NAME,
        project.SECTION_NAME,
        project.AREA_NAME,
        project.TYPE projectType,
        project.LICENSE_NO,
        project.LEADER_NAME,
        project.CONTACT,
        bill.AUDIT_STATUS,
        station.NAME stationName
        from
        smm_bill_bill bill
        left join smm_project_project project on bill.PROJECT_ID = project.DEPT_ID
        left join smm_project_weighing_station station on bill.STATION_ID = station.ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="statTodayCounts" resultType="com.ybkj.smm.modules.project.entity.Project">
        select count(bill.id) billCount,project.DEPT_ID deptId
        from smm_project_project project
            left join smm_bill_bill bill force index(IDX_CREATE_TIME) on project.DEPT_ID=bill.PROJECT_ID
        and bill.CREATE_TIME &gt;= #{startTime} and bill.CREATE_TIME &lt;= #{endTime}
        group by project.DEPT_ID
    </select>
    <select id="statMonthVehicleLoad" resultType="com.ybkj.smm.modules.project.entity.Project">
        select PROJECT_ID deptId,
        count(*) totalBillCount,
        sum(IF(#{endTime} >= CREATE_TIME and CREATE_TIME >= #{startTime},1,0)) monthBillCount,
        sum(IF(CREATE_TIME>#{nowDate},1,0)) billCount,
        sum(VEHICLE_LOAD) totalVehicleLoad,
        sum(IF(#{endTime} >= CREATE_TIME and CREATE_TIME >= #{startTime},VEHICLE_LOAD,0)) monthVehicleLoad,
        sum(IF(CREATE_TIME>#{nowDate},VEHICLE_LOAD,0)) dailyVehicleLoad
        from smm_bill_bill
        <where>
            ${ew.sqlSegment}
        </where>
        group by PROJECT_ID
    </select>
    <select id="selectBillList" resultType="com.ybkj.smm.modules.bill.entity.Bill">
        select ( @row_number := @row_number + 1 ) orderNumber,billSorted.* from (select
        bill.id,
        bill.CREATE_USER_ID,
        bill.UPDATE_USER_ID,
        bill.CREATE_TIME,
        bill.UPDATE_TIME,
        bill.PROJECT_ID,
        bill.BILL_NO,
        bill.CAR_NUMBER,
        bill.CAR_COMPANY,
        bill.CAR_BRAND,
        bill.CAR_OWNER_NAME,
        bill.CAR_KERB_WEIGHT,
        bill.CAR_MAX_PAYLOAD,
        bill.DRIVER_NAME,
        bill.DRIVER_MOBILE,
        bill.VEHICLE_LOAD,
        bill.DESTINATION,
        bill.DEST_LONGITUDE,
        bill.DEST_LATITUDE,
        bill.ESTIMATED_ARRIVAL_TIME,
        bill.DEST_LEADER,
        bill.DEST_LEADER_MOBILE,
        bill.SIGN_TIME,
        bill.AUDIT_TIME,
        bill.STATUS,
        bill.DRIVER_SIGNATURE_ID,
        bill.PROJECT_LEADER_SIGNATURE_ID,
        bill.SUPERVISOR_SIGNATURE_ID,
        bill.AUTHORITY_SIGNATURE_ID,
        bill.AUTHORITY_ID,
        bill.VERIFY_CANCEL_USER_ID,
        bill.VERIFY_CANCEL_TIME,
        bill.VERIFY_CANCEL_LONGITUDE,
        bill.VERIFY_CANCEL_LATITUDE,
        bill.DISTANCE,
        project.NAME,
        project.SECTION_NAME,
        project.AREA_NAME,
        project.TYPE projectType,
        project.LICENSE_NO,
        project.LEADER_NAME,
        project.CONTACT,
        station.NAME stationName
        from
        smm_bill_bill bill
        left join smm_project_project project on bill.PROJECT_ID = project.DEPT_ID
        left join smm_project_weighing_station station on bill.STATION_ID = station.ID
        join (select @row_number:=0) temp on 1=1
        <where>
            ${ew.sqlSegment}
        </where>) billSorted,
        ( SELECT @row_number := 0 ) temp
    </select>

    <resultMap id="BillProvinceLoadMap" type="com.ybkj.smm.modules.bill.dto.BillProvinceLoadDto">
        <result property="areaCode" column="AREA_CODE"/>
        <result property="areaName" column="AREA_NAME"/>
        <result property="completedBill" column="COMPLETED_BILL"/>
        <result property="proCount" column="PRO_COUNT"/>
        <result property="totalLoad" column="TOTAL_LOAD"/>
        <result property="transitBill" column="TRANSIT_BILL"/>
    </resultMap>
    <select id="getProvinceLoad" parameterType="map" resultMap="BillProvinceLoadMap">
        SELECT
            sa.AREA_NAME AS AREA_NAME,
            sa.AREA_CODE AS AREA_CODE,
            complete_info.completed_bill AS COMPLETED_BILL,
            inTransit_info.inTransit_bill AS TRANSIT_BILL,
            project_info.pro_count AS PRO_COUNT,
            IFNULL( complete_info.total_load, 0 ) + IFNULL( inTransit_info.total_load, 0 ) AS TOTAL_LOAD
        FROM
            sys_area AS sa
        LEFT JOIN (
        SELECT
            SUBSTR( pp.AREA_CODE, 1, #{subAreaCodeLength} ) AS city_code,
            COUNT( pp.AREA_CODE ) AS completed_bill,
            SUM( bb.VEHICLE_LOAD ) AS total_load
        FROM
            smm_bill_bill AS bb,
            smm_project_project AS pp
        WHERE
            bb.PROJECT_ID = pp.DEPT_ID
           <if test='(startTime != "" &amp;&amp; startTime != null) &amp;&amp; (endTime != ""  &amp;&amp; endTime != null) '>
               and bb.CREATE_TIME &gt;=#{startTime} and bb.CREATE_TIME &lt;=CONCAT(#{endTime},' 23:59:59')
           </if>
            AND ( bb.`STATUS` = 'completed' )
            AND pp.DELETED = 0
            <if test='sqlFilter != null'>
                AND ${sqlFilter}
            </if>
        GROUP BY
            city_code
            ) complete_info ON sa.AREA_CODE = complete_info.city_code
        LEFT JOIN (
        SELECT
            SUBSTR( pp.AREA_CODE, 1, #{subAreaCodeLength} ) AS city_code,
            COUNT( pp.AREA_CODE ) AS inTransit_bill,
            SUM( bb.VEHICLE_LOAD ) AS total_load
        FROM
            smm_bill_bill AS bb,
            smm_project_project AS pp
        WHERE
            bb.PROJECT_ID = pp.DEPT_ID
            <if test='(startTime != "" &amp;&amp; startTime != null) &amp;&amp; (endTime != ""  &amp;&amp; endTime != null) '>
                and bb.CREATE_TIME &gt;=#{startTime} and bb.CREATE_TIME &lt;=CONCAT(#{endTime},' 23:59:59')
            </if>
            AND ( bb.`STATUS` in ('inTransit','abnormal') )
            AND pp.DELETED = 0
            <if test='sqlFilter != null'>
                AND ${sqlFilter}
            </if>
        GROUP BY
            city_code
            ) inTransit_info ON sa.AREA_CODE = inTransit_info.city_code
        LEFT JOIN (
        SELECT
            city_code,
            COUNT(DISTINCT NAME) AS pro_count
        FROM
        (
            SELECT  pp.NAME as NAME,
            SUBSTR( pp.AREA_CODE, 1, #{subAreaCodeLength} ) AS city_code
            FROM
            smm_project_project AS pp
            WHERE
                pp.DELETED = 0
                and pp.CREATE_TIME &lt;= CONCAT(#{endTime},' 23:59:59')
                <if test='sqlFilter != null'>
                    AND ${sqlFilter}
                </if>
        ) pro_query
        GROUP BY
            city_code
            ) project_info ON sa.AREA_CODE = project_info.city_code
        WHERE
            (sa.AREA_LEVEL = #{areaLevel})
            AND
            SUBSTR( sa.AREA_CODE, 1, #{subParentCodeLength} ) = #{areaCode};
    </select>

    <resultMap id="BillCityLoadMap" type="com.ybkj.smm.modules.bill.dto.BillCityLoadDto">
        <result property="billTime" column="BILL_TIME"/>
        <result property="totalLoad" column="TOTAL_LOAD"/>
    </resultMap>
    <select id="getCityLoad" parameterType="map" resultMap="BillCityLoadMap">
        <![CDATA[
        SELECT
            ADDDATE(#{startTime}, sa.num) AS BILL_TIME , time_load.total_load as TOTAL_LOAD
        FROM
            sys_adddate AS sa
        LEFT JOIN (
            SELECT
                DATE_FORMAT(bb.CREATE_TIME, '%Y-%m-%d') AS x_time,
                SUM(bb.VEHICLE_LOAD) AS total_load
            FROM
                smm_bill_bill AS bb
            JOIN smm_project_project AS pp ON bb.PROJECT_ID = pp.DEPT_ID
            WHERE
                pp.AREA_CODE like concat(#{code},'%')
                AND DATE_FORMAT(bb.CREATE_TIME, '%Y-%m-%d') BETWEEN #{startTime} AND #{endTime}
        ]]>
                AND pp.DELETED = 0
                <if test='sqlFilter != null'>
                    AND ${sqlFilter}
                </if>
        <![CDATA[
            GROUP BY
                x_time
            ORDER BY
                x_time
        ) time_load ON ADDDATE(#{startTime}, sa.num) = time_load.x_time
        WHERE
            sa.num <= DATEDIFF(#{endTime}, #{startTime});
        ]]>
    </select>

    <resultMap id="UserInfoMap" type="com.ybkj.smm.modules.bill.dto.UserInfoDto">
        <result property="deptCode" column="DEPT_CODE"/>
        <result property="areaName" column="AREA_NAME"/>
        <result property="areaCode" column="AREA_CODE"/>
        <result property="parentName" column="PARENT_NAME"/>
        <result property="parentCode" column="PARENT_CODE"/>
    </resultMap>
    <select id="getLoginUserInfo" parameterType="map" resultMap="UserInfoMap">
        SELECT
            sd.DEPT_CODE,
            areaTable.AREA_NAME,
            areaTable.AREA_CODE,
            parentAreaTable.AREA_NAME AS PARENT_NAME,
            areaTable.PARENT_CODE
        FROM
            sys_dept AS sd
                LEFT JOIN ( SELECT sa.AREA_NAME, sa.AREA_CODE, sa.PARENT_CODE FROM sys_area AS sa WHERE sa.DEL_FLAG = 0) areaTable ON sd.AREA_CODE = areaTable.AREA_CODE
                LEFT JOIN ( SELECT AREA_NAME, AREA_CODE FROM sys_area WHERE DEL_FLAG = 0) parentAreaTable ON areaTable.PARENT_CODE = parentAreaTable.AREA_CODE
        WHERE
            sd.DEPT_CODE = (
                SELECT
                    DEPT_CODE
                FROM
                    `sys_user`
                WHERE
                    USER_ID = #{userId})
            AND sd.DEL_FLAG = 0
    </select>

    <resultMap id="BillProjectLoadMap" type="com.ybkj.smm.modules.bill.dto.BillProjectLoadDto">
        <result property="billTime" column="BILL_TIME"/>
        <result property="transitLoad" column="TRANSIT_LOAD"/>
        <result property="transitSum" column="TRANSIT_SUM"/>
        <result property="completedLoad" column="COMPLETED_LOAD"/>
        <result property="completedSum" column="COMPLETED_SUM"/>
        <result property="totalLoad" column="TOTAL_LOAD"/>
    </resultMap>
    <select id="getProjectLoad" parameterType="map" resultMap="BillProjectLoadMap">
        SELECT
            ADDDATE( #{startTime}, sa.num ) AS BILL_TIME,
            IFNULL(transit_table.inTransit_load,0) TRANSIT_LOAD,
            IFNULL(transit_table.inTransit_sum,0) TRANSIT_SUM,
            IFNULL(completed_table.completed_load,0) COMPLETED_LOAD,
            IFNULL(completed_table.completed_sum,0) COMPLETED_SUM,
            (IFNULL(transit_table.inTransit_load,0) + IFNULL(completed_table.completed_load,0)) TOTAL_LOAD
        FROM
            sys_adddate AS sa
        LEFT JOIN (
        SELECT
            sum( sbb.VEHICLE_LOAD ) inTransit_load,
            DATE_FORMAT( sbb.CREATE_TIME, '%Y-%m-%d' ) AS x_time,
            count( sbb.`STATUS` ) AS inTransit_sum
        FROM
            sys_user su,
            smm_project_project spp,
            smm_bill_bill sbb
        WHERE
            su.DEPT_CODE = spp.DEPT_CODE
            AND sbb.PROJECT_ID = spp.DEPT_ID
            AND su.USER_ID = #{userId}
            AND sbb.`STATUS` = 'inTransit'
            AND su.DEL_FLAG = 0
            AND spp.DELETED = 0
            <if test='sqlFilter != null'>
                AND ${sqlFilter}
            </if>
        GROUP BY
            x_time
            ) transit_table ON ADDDATE( #{startTime}, sa.num ) = transit_table.x_time
        LEFT JOIN (
        SELECT
            sum( sbb.VEHICLE_LOAD ) completed_load,
            DATE_FORMAT( sbb.CREATE_TIME, '%Y-%m-%d' ) x_time,
            count( sbb.`STATUS` ) completed_sum
        FROM
            sys_user su,
            smm_project_project spp,
            smm_bill_bill sbb
        WHERE
            su.DEPT_CODE = spp.DEPT_CODE
            AND sbb.PROJECT_ID = spp.DEPT_ID
            AND su.USER_ID = #{userId}
            AND sbb.`STATUS` = 'completed'
            AND su.DEL_FLAG = 0
            AND spp.DELETED = 0
            <if test='sqlFilter != null'>
                AND ${sqlFilter}
            </if>
        GROUP BY
            x_time
            ) completed_table ON ADDDATE( #{startTime}, sa.num ) = completed_table.x_time
        WHERE
            sa.num &lt;= DATEDIFF( #{endTime}, #{startTime} );
    </select>
    <select id="statVehicleLoad" resultType="com.ybkj.smm.modules.project.dto.ProjectStatDto">
        select PROJECT_ID deptId,
        sum(IF(CREATE_TIME>#{nowDate},1,0)) billCount,
        sum(IF(CREATE_TIME>#{nowDate},VEHICLE_LOAD,0)) dailyVehicleLoad,
        count(*) totalBillCount,
        sum(VEHICLE_LOAD) totalVehicleLoad
        from smm_bill_bill
        <where>
            ${ew.sqlSegment}
        </where>
        group by PROJECT_ID
    </select>
    <select id="queryDriverPage" resultType="com.ybkj.smm.modules.bill.dto.BillResultDto">
        select
        bill.id,
        bill.BILL_NO,
        bill.CAR_NUMBER,
        bill.VEHICLE_LOAD,
        bill.SIGN_TIME,
        bill.STATUS,
        if(project.SECTION_NAME,concat(project.NAME,'(',project.SECTION_NAME,')'),project.NAME) projectName
        from
        smm_bill_bill bill
        LEFT JOIN smm_project_project project
        ON bill.PROJECT_ID = project.DEPT_ID
        left join smm_project_weighing_station station on bill.STATION_ID = station.ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="getbill" resultType="com.ybkj.smm.modules.Push.dto.billcs">

        SELECT  a.doc_number,c.SHOW_NAME as CREATE_USER_ID ,a.CREATE_TIME,b.LICENSE_NO,b.AREA_NAME,b.NAME,b.TOTAL_YIELD,b.LEADER_NAME,b.CONTACT ,
                a.CAR_NUMBER,a.VEHICLE_LOAD,a.DESTINATION,a.CAR_OWNER_NAME,a.DRIVER_MOBILE,a.AUTHORITY_ID,a.AUDIT_TIME,a.BILL_NO ,d.department_code,d.division_code
                ,d.name as PARENT_ID,a.CAR_BRAND,a.ESTIMATED_ARRIVAL_TIME,a.STATUS,b.CREATE_USER_MOBILE,b.type
        FROM smm_bill_bill a
                 left join smm_project_project  b on a.PROJECT_ID = b.DEPT_ID
                 left join sys_dept d on d.DEPT_ID = b.PARENT_ID
                 left join sys_user c on a.CREATE_USER_ID = c.USER_ID
        WHERE a.STATUS IN ("completed","inTransit","abnormal") AND  a.CREATE_TIME > #{nowDate}

    </select>
    <select id="selectTransportDocument" resultType="java.lang.String">
        SELECT s_doc_number FROM  transport_document
    </select>
    <select id="selectTransportDocuments" resultType="com.ybkj.smm.modules.Push.dto.TransportDocument">
        SELECT * FROM  transport_document where  push_status='0'

    </select>
    <select id="selectDocument" resultType="com.ybkj.smm.modules.bill.entity.Bill">
        SELECT *
        FROM smm_bill_bill
        WHERE STATUS = 'inTransit'
    </select>

    <update id="updateByBillId">
        UPDATE smm_bill_bill
        SET STATUS ='abnormal'
        WHERE ID = #{id}
    </update>
    <update id="updateStatus">

        UPDATE transport_document
        SET  doc_status=#{docStatus}
        WHERE doc_number=#{docNumber}
    </update>

    <select id="getProjectIdByProjectNameAndAreaCode" resultType="com.ybkj.smm.modules.sys.query.SysDeptQuery"
            parameterType="java.lang.String">
        SELECT DEPT_ID AS deptId,NAME AS name FROM smm_project_project
        <where>
            <if test="deptCode != null and deptCode != ''">
                DEPT_CODE LIKE CONCAT(#{deptCode},'%')
            </if>
            <if test="deptCode != null and deptCode != ''">
                AND DEPT_ID in (select DEPT_ID from sys_dept where DEL_FLAG=0 and DEPT_CODE like concat(#{deptCode},'%'))
            </if>
            AND DELETED = '0'
        </where>
        GROUP BY DEPT_ID, NAME
    </select>
    <select id="selectSandManagerUserId" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT sur.USER_ID
        FROM sys_user_role sur
                 JOIN sys_user su ON sur.USER_ID = su.USER_ID
        WHERE su.DEPT_ID = #{deptId}
          AND sur.ROLE_ID = '01HN2GHWS7S109JQRZPSZJCKTS'
    </select>
    <select id="selectWaterUserId" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT sur.USER_ID
        FROM sys_user_role sur
                 JOIN sys_user su ON sur.USER_ID = su.USER_ID
                 JOIN sys_dept sd ON su.DEPT_ID = sd.DEPT_ID
        WHERE sd.DEPT_ID = (
            SELECT PARENT_ID
            FROM sys_dept
            WHERE DEPT_ID = #{deptId}
        )
          AND sur.ROLE_ID = '01JRWFQ5M0HV4ME35232P0AA1Y';
    </select>
    <select id="getProvinceLoadByProject" resultType="com.ybkj.smm.modules.bill.dto.BillProvinceLoadDto"
            parameterType="java.lang.String">
        SELECT
            pp.NAME AS areaName,
            SUM(CASE WHEN bb.`STATUS` = 'completed' THEN 1 ELSE 0 END) AS completedBill,
            SUM(CASE WHEN bb.`STATUS` = 'inTransit' THEN 1 ELSE 0 END) AS transitBill,
            IFNULL(SUM( bb.VEHICLE_LOAD ),0) AS total_load,
            COUNT(DISTINCT pp.DEPT_ID) AS pro_count

        FROM
            smm_bill_bill AS bb
                RIGHT JOIN
            smm_project_project AS pp
            ON bb.PROJECT_ID= pp.DEPT_ID

        WHERE pp.AREA_CODE LIKE #{areaCode}
        <if test='(startTime != "" &amp;&amp; startTime != null) &amp;&amp; (endTime != ""  &amp;&amp; endTime != null) '>
            AND DATE_FORMAT(bb.CREATE_TIME, '%Y-%m-%d') BETWEEN #{startTime} AND #{endTime}
        </if>
        AND pp.DELETED = 0

        <if test="projectId != null and projectId != ''">
            AND pp.DEPT_ID = #{projectId}
        </if>
        <if test='sqlFilter != null'>
            AND ${sqlFilter}
        </if>
        GROUP BY pp.NAME
    </select>
    <select id="getDeptCodeByAreaCode" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT DEPT_CODE FROM sys_dept WHERE AREA_CODE = #{areaCode} AND type = 'dept' AND DEL_FLAG = 0 limit 1
    </select>
    <select id="getTransportdeocem" resultType="com.ybkj.smm.modules.Push.dto.TransportDocument">
        SELECT * FROM  transport_document where  push_status='1'
    </select>
    <select id="getbils" resultType="com.ybkj.smm.modules.Push.dto.billcs">
        SELECT * FROM  smm_bill_bill where  CREATE_TIME > #{nowDate}
    </select>
    <select id="getTransportdeocems" resultType="com.ybkj.smm.modules.Push.dto.TransportDocument">
        SELECT * FROM  transport_document where  doc_number=#{docNumber}
    </select>
    <select id="getRegionCode" resultType="java.lang.String">
        SELECT division_code FROM sys_dept WHERE DEPT_ID=#{projectId}
    </select>
    <select id="selectBillVehicleLoad" resultType="java.math.BigDecimal">
        select
        SUM(bill.VEHICLE_LOAD)
        from
        smm_bill_bill bill
        left join smm_project_project project on bill.PROJECT_ID = project.DEPT_ID
        left join smm_project_weighing_station station on bill.STATION_ID = station.ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="getProjectList" resultType="com.ybkj.smm.modules.bill.dto.ProjectList">
        SELECT DEPT_ID AS value,NAME AS text,SECTION_NAME AS sectionName FROM smm_project_project WHERE DELETED = 0
    </select>
    <select id="getProjectListCity" resultType="com.ybkj.smm.modules.bill.dto.ProjectList"
            parameterType="java.lang.String">
        SELECT DEPT_ID AS value,NAME AS text,SECTION_NAME AS sectionName FROM smm_project_project WHERE DELETED = 0 AND DEPT_CODE LIKE CONCAT(#{deptCode},'%')
    </select>
    <select id="getProjectListSG" resultType="com.ybkj.smm.modules.bill.dto.ProjectList"
            parameterType="java.lang.String">
        SELECT spp.DEPT_ID AS value,spp.NAME AS text,spp.SECTION_NAME AS sectionName
        FROM smm_project_project spp LEFT JOIN sys_dept sd ON spp.DEPT_ID = sd.DEPT_ID
        WHERE spp.DELETED = 0 AND spp.DEPT_CODE = #{deptCode} AND sd.TYPE = 'sand'
    </select>
    <select id="getDriverProjectList" resultType="com.ybkj.smm.modules.bill.dto.ProjectList">
        SELECT spp.DEPT_ID AS value,spp.NAME AS text,spp.SECTION_NAME AS sectionName
        FROM smm_project_project spp LEFT JOIN smm_project_driver spd ON spp.DEPT_ID = spd.PROJECT_ID
        WHERE spp.DELETED = 0 AND spd.MOBILE = #{mobile}
    </select>
    <select id="getProjects" resultType="com.ybkj.smm.modules.bill.dto.ProjectList">
        SELECT DEPT_ID AS value,NAME AS text,SECTION_NAME AS sectionName FROM smm_project_project
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="getType" resultType="java.lang.String">
        SELECT type  FROM smm_project_project where DEPT_ID=#{projectId}
    </select>
    <select id="selectRoleMenuByUserId" resultType="com.sloth.modules.sys.entity.SysRoleMenuEntity">
        SELECT
            rm.*
        FROM
            sys_role_menu rm
                LEFT JOIN sys_user_role ur ON ur.ROLE_ID = rm.ROLE_ID
        WHERE
            ur.USER_ID = #{userId}
          AND (rm.MENU_ID = '01K0RFVFM2NQE4217XBYCA2ZMV' OR rm.MENU_ID = '01K0RFE3RSYWADV54168GVAC45')
    </select>


</mapper>
