/**
 * <li>功能描述：vue组件，主要解决数据字典的使用和简化表单验证</li>
 * @author: <EMAIL>
 * @date: 2021/1/27 9:06
 * @since: v2.1.1
 */
var COST_MULTIPLE = 100000000;
/**
 * 公共props
 */
var publicProp = {
    id: {
        type: String,
        default: ''
    },
    name: {
        type: String,
        default: ''
    },
    value: {
        type: [String, Number],
    },
    rules: {
        type: String,
        default: ''
    },
    styleClass: {
        type: String,
        default: 'form-control'
    },
}
/**
 * select 组件,结合本地缓存和后台缓存，获取字典数据并渲染option
 */
Vue.component('my-select', {
    props: $.extend({}, publicProp, {
        pvalue: {
            type: String,
            default: '',
        },
        nullValue: {
            type: Boolean,
            default: true,
        },
        firstOption: {
            type: String,
            default: '--请选择--'
        },
        disabled: {
            type: Boolean,
            default: false
        },
        options: {
            type: Array,
            default: function () {
                return [];
            }
        }
    }),
    template: "<div :id=\"'div_'+finalId\">" +
        "<select :id='finalId' :name='finalName'  :value='value' :class='styleClass' :rules='rules'" +
        "@input='$emit(\"input\", $event.target.value)' :disabled='disabled'>" +
        "<option v-if='nullValue' value='' >{{firstOption}}</option>" +
        "<option :value='option.value' v-for='option in finalOptions'>{{ option.name }}</option>" +
        "</select>" +
        "<label :for='finalId' class='normal-label'></label>" +
        "</div>",
    watch: {
        value: function (nVal) {
            if ($("#div_" + this.finalId).is(":visible")) {
                if (typeof (nVal) != "undefined" && this.rules.length > 0) {
                    validateInputByValue(nVal, this.finalId, this.rules);
                }
            } else {
                $("label[for='" + this.finalId + "']").removeClass("error-label text-danger").addClass("normal-label");
            }
        }
    },
    computed: {
        finalId: function () {
            return this.id ? this.id : 'id_' + Math.round(COST_MULTIPLE * Math.random());
        },
        finalName: function () {
            return this.name ? this.name : 'name_' + Math.round(COST_MULTIPLE * Math.random());
        },
        finalOptions: function () {
            var vm = this;
            if (vm.pvalue && vm.pvalue != '' && vm.options.length == 0) {
                var r = DictUtils.cList(vm.pvalue);
                return r[vm.pvalue];
            } else {
                return vm.options;
            }
        }
    }
});
/**
 * radio 组件
 */
Vue.component("my-radio", {
    model: {
        prop: 'value',
        event: 'change'
    },
    props: $.extend({}, publicProp, {
        pvalue: {
            type: String,
            default: '',
        },
        styleClass: {
            type: String,
            default: 'radio-inline'
        },
        disabled: {
            type: Boolean,
            default: false
        },
        options: {
            type: Array,
            default: function () {
                return [];
            }
        }
    }),
    data: function () {
        return {
            innerValue: '',
        }
    },
    template: "<div :id=\"'div_'+finalId\">" +
        "<div class='radio' style='padding-top: 0px'>" +
        "<label v-for='(option,index) in finalOptions' :class='styleClass' :for='finalId+index' :key='index'>" +
        "<input type='radio' :id='finalId+index' :name='finalName' :value='option.value' v-model='innerValue' " +
        "@change='$emit(\"change\", $event.target.value)' :disabled='disabled'>{{option.name}}" +
        "</label>" +
        "</div>" +
        "<input :id='finalId' type='hidden' v-model='innerValue' :rules='rules'>" +
        "<label :for='finalId' class='normal-label'></label>" +
        "</div>",
    watch: {
        value: function (nVal) {
            this.innerValue = nVal;
        },
        innerValue: function (nVal) {
            if ($("#div_" + this.finalId).is(":visible")) {
                if (typeof (nVal) != "undefined" && this.rules.length > 0) {
                    validateInputByValue(nVal, this.finalId, this.rules);
                }
            } else {
                $("label[for='" + this.finalId + "']").removeClass("error-label text-danger").addClass("normal-label");
            }
        }
    },
    computed: {
        finalId: function () {
            return this.id ? this.id : 'id_' + Math.round(COST_MULTIPLE * Math.random());
        },
        finalName: function () {
            return this.name ? this.name : 'name_' + Math.round(COST_MULTIPLE * Math.random());
        },
        finalOptions: function () {
            var vm = this;
            if (vm.pvalue && vm.pvalue != '' && vm.options.length == 0) {
                var r = DictUtils.cList(vm.pvalue);
                return r[vm.pvalue];
            } else {
                return vm.options;
            }
        }
    },
    mounted: function () {
        this.innerValue = this.value;
    }
});
/**
 * checkbox 组件，注意v-model绑定的value必须是一个数组
 */
Vue.component("my-checkbox", {
    model: {
        prop: 'value',
        event: 'change'
    },
    props: $.extend({}, publicProp, {
        pvalue: {
            type: String,
            default: '',
        },
        styleClass: {
            type: String,
            default: 'checkbox-inline'
        },
        value: {
            type: Array,
            default: function () {
                return [];
            },
        },
        disabled: {
            type: Boolean,
            default: false
        },
        options: {
            type: Array,
            default: function () {
                return [];
            }
        }
    }),
    data: function () {
        return {
            innerValue: '',
        }
    },
    template: "<div :id=\"'div_'+finalId\">" +
        "<label v-for='(option,index) in finalOptions' :class='styleClass'>" +
        "<input type='checkbox' :id='finalId+index' :name='finalName' :value='option.value' v-model='innerValue' " +
        "@change='$emit(\"change\", innerValue)' :disabled='disabled'>{{option.name}}" +
        "</label>" +
        "<br>" +
        "<input :id='finalId' type='hidden' v-model='innerValue'  :rules='rules'>" +
        "<label :for='finalId' class='normal-label'></label>" +
        "</div>",
    watch: {
        value: function (nVal) {
            this.innerValue = nVal;
        },
        innerValue: function (nVal) {
            if ($("#div_" + this.finalId).is(":visible")) {
                if (typeof (nVal) != "undefined" && this.rules.length > 0) {
                    validateInputByValue(nVal, this.finalId, this.rules);
                }
            } else {
                $("label[for='" + this.finalId + "']").removeClass("error-label text-danger").addClass("normal-label");
            }
        }
    },
    computed: {
        finalId: function () {
            return this.id ? this.id : 'id_' + Math.round(COST_MULTIPLE * Math.random());
        },
        finalName: function () {
            return this.name ? this.name : 'name_' + Math.round(COST_MULTIPLE * Math.random());
        },
        finalOptions: function () {
            var vm = this;
            if (vm.pvalue && vm.pvalue != '' && vm.options.length == 0) {
                var r = DictUtils.cList(vm.pvalue);
                return r[vm.pvalue];
            } else {
                return vm.options;
            }
        }
    },
    mounted: function () {
        this.innerValue = this.value;
    }
})
/**
 * input组件，一个透明的input，所有input的属性和方法都支持。
 * 只是给原生的input添加了表单验证触发
 */
Vue.component("my-input", {
    props: $.extend({}, publicProp, {
        type: {
            type: String,
            default: 'text',
        },
        placeholder: {
            type: String,
            default: '',
        },
        autocomplete: {
            type: String,
            default: 'off'
        }
    }),
    template: "<div>" +
        "<input :id='finalId' :name='finalName' :type='type' :class='styleClass'  :value='value' :placeholder='placeholder' :autocomplete='autocomplete'" +
        "v-bind='$attrs' v-on='inputListeners' :rules='rules'>" +
        "<label :for='finalId' class='normal-label'></label>" +
        "</div>",
    watch: {
        value: function (nVal) {
            if ($("#" + this.finalId).is(":visible")) {
                if (typeof (nVal) != "undefined" && this.rules.length > 0) {
                    validateInputByValue(nVal, this.finalId, this.rules);
                }
            } else {
                $("label[for='" + this.finalId + "']").removeClass("error-label text-danger").addClass("normal-label");
            }
        }
    },
    computed: {
        finalId: function () {
            return this.id ? this.id : 'id_' + Math.round(COST_MULTIPLE * Math.random());
        },
        finalName: function () {
            return this.name ? this.name : 'name_' + Math.round(COST_MULTIPLE * Math.random());
        },
        inputListeners: function () {
            var vm = this
            // `Object.assign` 将所有的对象合并为一个新对象
            return Object.assign({},
                // 我们从父级添加所有的监听器
                this.$listeners,
                // 然后我们添加自定义监听器，
                // 或覆写一些监听器的行为
                {
                    // 这里确保组件配合 `v-model` 的工作
                    input: function (event) {
                        vm.$emit('input', event.target.value)
                    }
                }
            )
        }
    }
})
/**
 * 一个添加了表单验证的div，用于处理上传文件等需要hidden配合验证的情况
 */
Vue.component("my-div", {
    props: $.extend({}, publicProp),
    template: "<div :id=\"'div_'+finalId\">" +
        "<slot></slot>" +
        "<input :id='finalId' type='hidden' v-model='value' :rules='rules'>" +
        "<label :for='finalId' class='normal-label'></label>" +
        "</div>",
    watch: {
        value: function (nVal) {
            if ($("#div_" + this.finalId).is(":visible")) {
                if (typeof (nVal) != "undefined" && this.rules.length > 0) {
                    validateInputByValue(nVal, this.finalId, this.rules);
                }
            } else {
                $("label[for='" + this.finalId + "']").removeClass("error-label text-danger").addClass("normal-label");
            }
        }
    },
    computed: {
        finalId: function () {
            return this.id ? this.id : 'id_' + Math.round(COST_MULTIPLE * Math.random());
        }
    }
})

/**
 * 区域三级联动组件
 * 示例：
 * <my-area v-model="user.areaCode" @area-id="setAreaId($event)" @area-name="setAreaName($event)"></my-area>
 * @deprecated 由于vue的懒加载机制，这个组件的初始值总是有问题，暂时弃用
 */
Vue.component("my-area2", {
    props: {
        id: {
            type: String,
            default: ''
        },
        level: {//显示几级区域，暂时只支持省市县3级显示
            type: Number,
            default: 3
        },
        name: {
            type: String,
            default: ''
        },
        value: {
            type: String,
            default: ''
        },
        rules: {
            type: String,
            default: ''
        },
        disabled: {
            type: Boolean,
            default: false
        },
        reset: {
            /**
             * 当需要在不执行mounted的时候，重置area的值为空时，先设置reset，再给value赋值空
             * 如果对value赋值时有视图切换，通过v-if控制，不用这个属性，通过v-show控制，可以利用这个属性
             */
            type: Boolean,
            default: false
        }
    },
    data: function () {
        return {
            provinceCode: ''
            , cityCode: ''
            , countyCode: ''
            , provinces: []
            , cities: []
            , counties: []
            , initFlag: false
            , initCitiesFlag: false
            , initCountiesFlag: false
        };
    },
    template: "<div>" +
        "<div style=\"width:30%;float:left;margin-right: 5px;\"> " +
        "      <select v-model=\"provinceCode\" class=\"form-control\" :disabled='disabled'> " +
        "       <option value=\"\">--省--</option> " +
        "       <option v-for=\"item in provinces\" :value=\"item.areaCode\">{{item.areaName}}</option> " +
        "      </select> " +
        "     </div> " +
        "     <div style=\"width: 30%;float:left;margin-right: 5px;\"> " +
        "      <select v-model=\"cityCode\" class=\"form-control\" :disabled='disabled'> " +
        "       <option value=\"\">--市--</option> " +
        "       <option v-for=\"item in cities\" :value=\"item.areaCode\">{{item.areaName}}</option> " +
        "      </select> " +
        "     </div> " +
        "     <div style=\"width: 30%;float:left\"> " +
        "      <select v-model=\"countyCode\" class=\"form-control\" :disabled='disabled'> " +
        "       <option value=\"\">--县--</option> " +
        "       <option v-for=\"item in counties\" :value=\"item.areaCode\">{{item.areaName}}</option> " +
        "      </select> " +
        "     </div> " +
        "   <input :id='finalId' type='hidden' :value='countyCode' :rules='rules'>" +
        "   <label :for='finalId' class='normal-label'></label>" +
        "</div>",
    methods: {
        //获取区域
        getAreaList: function (type, areaCode) {
            var that = this;
            AreaUtils.getSubList(areaCode, function (areaList) {
                if (type == 'province') {
                    that.provinces = [];
                    that.provinces.push(...areaList);
                } else if (type == 'city') {
                    that.cities = [];
                    var hasCurrentCity = false;
                    for (var i = 0; i < areaList.length; i++) {
                        if (areaList[i].areaCode == that.cityCode) {
                            hasCurrentCity = true;
                        }
                        that.cities.push(areaList[i]);
                    }
                    if (!that.initCitiesFlag) {
                        that.initCitiesFlag = true;
                    } else if (!hasCurrentCity) {
                        that.cityCode = '';
                    } else {
                        that.getAreaList('county', that.cityCode);
                    }
                } else {
                    that.counties = [];
                    var hasCurrentCounty = false;
                    for (var i = 0; i < areaList.length; i++) {
                        if (areaList[i].areaCode == that.countyCode) {
                            hasCurrentCounty = true;
                        }
                        that.counties.push(areaList[i]);
                    }
                    if (!that.initCountiesFlag) {
                        that.initCountiesFlag = true;
                    } else if (!hasCurrentCounty) {
                        that.countyCode = '';
                    }
                }
            });
        },
    },
    watch: {
        "value": function (nVal) {
            if (nVal && nVal.length > (4 * this.level + 2)) {
                if (this.level >= 1) {
                    this.provinceCode = nVal.substring(0, 4 * 1 + 3);
                }
                if (this.level >= 2) {
                    this.cityCode = nVal.substring(0, 4 * 2 + 3);
                }
                if (this.level >= 3) {
                    this.countyCode = nVal.substring(0, 4 * 3 + 3);
                }
            } else {
                this.provinceCode = "";
                this.cityCode = "";
                this.countyCode = "";
                this.initFlag = true;
                /*if (!this.initFlag) {
                }*/
            }
            if ($("#" + this.finalId).is(":visible")) {
                if (typeof (nVal) != "undefined" && this.rules.length > 0) {
                    validateInputByValue(nVal, this.finalId, this.rules);
                }
            } else {
                $("label[for='" + this.finalId + "']").removeClass("error-label text-danger").addClass("normal-label");
            }
        },
        "provinceCode": function (newVal) {
            if (this.level == 1) {
                this.$emit('input', newVal);
            } else {
                if (newVal) {
                    this.getAreaList('city', newVal);
                } else {
                    this.cityCode = "";
                    this.cities = [];
                }
            }
        },
        "cityCode": function (newVal) {
            if (this.level == 2) {
                this.$emit('input', newVal);
            } else {
                if (newVal) {
                    this.getAreaList('county', newVal);
                } else {
                    this.countyCode = "";
                    this.counties = [];
                }
            }
        },
        "countyCode": function (newVal) {
            if (this.level == 3) {
                this.$emit('input', newVal);
                for (var i = 0; i < this.counties.length; i++) {
                    if (this.counties[i].areaCode == newVal) {
                        this.$emit('area-id', this.counties[i].id);
                        this.$emit('area-name', this.counties[i].areaName);
                    }
                }
            }
        },
        "reset": function (newVal) {
            if (newVal) {
                this.initFlag = false;
            }
        }
    },
    computed: {
        finalId: function () {
            return this.id ? this.id : 'id_' + Math.round(COST_MULTIPLE * Math.random());
        }
    },
    mounted: function () {
        this.getAreaList("province", "");
        if (this.value) {
            if (this.value && this.value.length > (4 * this.level + 2)) {
                if (this.level >= 1) {
                    this.provinceCode = this.value.substring(0, 4 * 1 + 3);
                }
                if (this.level >= 2) {
                    this.cityCode = this.value.substring(0, 4 * 2 + 3);
                }
                if (this.level >= 3) {
                    this.countyCode = this.value.substring(0, 4 * 3 + 3);
                }
            }
        }
        this.initFlag = true;
    },
})


/**
 * 区域三级联动组件
 * 示例：
 * <my-area v-model="user.areaCode" @area-id="setAreaId($event)" @area-name="setAreaName($event)"></my-area>
 */
Vue.component("my-area", {
    props: $.extend({}, publicProp, {
        disabled: {
            type: Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: '所属区域'
        },
        showFullName: {//显示省市县三级全名
            type: Boolean,
            default: true,
        },
        zIndex: {
            type: Number, //layer弹窗的层次
            default: 1024
        }
    }),
    data: function () {
        return {
            setting: {//ztree配置
                data: {
                    simpleData: {
                        enable: true,
                        idKey: "areaCode",
                        pIdKey: "parentCode",
                        rootPId: ""
                    },
                    key: {
                        url: "nourl",
                        name: "areaName",
                    }
                }
            },
            areaTree: null,
            areaName: '',
            areaId: '',
        };
    },
    template: "<div :id=\"'div_'+finalId\">" +
        "<div v-if='!disabled' class='input-group'>" +
        ' <input :class="styleClass" style="background-color: #fff" :value="areaName" readonly @click="showTree" :placeholder="placeholder">' +
        ' <div  class="input-group-addon" @click="showTree"><i class="fa fa-sitemap"></i></div>' +
        '</div>' +
        '<input v-if="disabled"  :class="styleClass" readonly :value="areaName" :placeholder="placeholder">' +
        '<input :id="finalId" type="hidden" :value="value"  :rules="rules">' +
        "<label :for='finalId' class='normal-label'></label>" +
        "</div>",
    methods: {
        getAreaTree: function () {
            var that = this;
            //加载区域树
            $.ajax({
                type: "get",
                url: baseURL + "sys/area/select",
                contentType: "application/json",
                async: false,
                success: function (r) {
                    if (r.code === 0) {
                        that.areaTree = $.fn.zTree.init($("#area_tree_" + that.finalId), that.setting, r.areaList);
                    } else {
                        alert(r.msg);
                    }
                },
            });
        },
        setAreaName: function (areaCode) {
            if (!areaCode) {
                this.areaName = "";
                this.areaId = "";
            } else {
                if (!this.areaTree) {
                    this.getAreaTree();
                }
                var node = this.areaTree.getNodeByParam("areaCode", areaCode);
                if (node != null) {
                    this.areaTree.selectNode(node);
                    this.areaName = this.showFullName ? node.areaFullName : node.areaName;
                    this.areaId = node.id;
                }
            }
            this.$emit('area-id', this.areaId);
            this.$emit('area-name', this.areaName);
            this.$emit('input', areaCode)
        },
        showTree: function () {
            if (this.disabled) {
                return;
            }
            var that = this;
            layer.open({
                type: 1,
                offset: '50px',
                title: "选择区域",
                area: ['300px', '450px'],
                shade: 0.3,
                zIndex: this.zIndex,
                shadeClose: false,
                content: jQuery("#area_layer_" + that.finalId),
                btn: ['确定', '取消'],
                btn1: function (index) {
                    var node = that.areaTree.getSelectedNodes();
                    if (node == null || node.length == 0) {
                        alert("请选择一个区域");
                        return;
                    } else {
                        that.areaName = that.showFullName ? node[0].areaFullName : node[0].areaName;
                        that.areaId = node[0].id;
                        that.$emit('input', node[0].areaCode);
                        that.$emit('area-id', that.areaId);
                        that.$emit('area-name', that.areaName);
                        layer.close(index);
                    }
                }
            });
        }
    },
    watch: {
        "value": function (nVal) {
            this.setAreaName(nVal);
            if ($("#div_" + this.finalId).is(":visible")) {
                if (typeof (nVal) != "undefined" && this.rules.length > 0) {
                    validateInputByValue(this.areaName, this.finalId, this.rules);
                }
            } else {
                $("label[for='" + this.finalId + "']").removeClass("error-label text-danger").addClass("normal-label");
            }
        },
    },
    computed: {
        finalId: function () {
            var finalId = this.id ? this.id : 'id_' + Math.round(COST_MULTIPLE * Math.random());
            //隐藏域放到body最后，防止定位不到元素
            $("body").append('<div id="area_layer_' + finalId + '" style="display: none;padding:10px;">' +
                '    <ul id="area_tree_' + finalId + '" class="ztree"></ul>' +
                "</div>");
            return finalId;
        }
    },
    mounted: function () {
        this.getAreaTree();
        if (this.value) {
            this.setAreaName(this.value)
        }
    },
    //组件销毁时移除辅助隐藏域
    destroyed: function () {
        $("#area_layer_" + this.finalId).remove();
    }
})
/**
 *<li>功能描述：分页组件，使用jqgrid样式自定义分页</li>
 * events:
 * pageSize改变时触发：this.$emit('size-change',currPage,pageSize)
 * currPage改变时触发：this.$emit('current-change',currPage,pageSize)
 *
 * @author: <EMAIL>
 * @date:  2021/12/20 10:45
 */
Vue.component("my-pager", {
    props: $.extend({}, publicProp, {
        pageSize: {
            default: 10
        },
        currPage: {
            default: 1
        },
        totalCount: {
            default: 0
        },
        totalPage: {
            default: 0,
        },
        page: {
            default: function () {
                return {
                    list: [],
                    totalCount: 0,
                    pageSize: 10,
                    totalPage: 0,
                    currPage: 1
                }
            }
        },
        pageSizes: {
            type: Array, //layer弹窗的层次
            default: function () {
                return [10, 30, 50, 100];
            }
        }
    }),
    data: function () {
        return {
            innerPageSize: 10,
            innerCurrPage: 1,
            innerTotalCount: 0,
            innerTotalPage: 0,
            inputCurrPage: 10,
            msg: '',
        };
    },
    template: '<table class="table" style="width: 100%;height: 36px;border: 1px solid rgb(221,221,221)">' +
        '            <tbody>' +
        '            <tr>' +
        '                <td  style="width:33%;padding:0 8px;"></td>' +
        '                <td  style="align:center;white-space: pre; width: 33%; padding:0 8px;">' +
        '                    <table class="ui-pg-table ui-common-table ui-paging-pager">' +
        '                        <tbody>' +
        '                        <tr>' +
        '                            <td :class="innerCurrPage<=1?\'ui-disabled\':\'\'" @click="innerCurrPage<=1?false:firstPage()" title="First Page"' +
        '                                style="cursor: default;width: 28px;padding: 0 4px;"><span class="glyphicon glyphicon-step-backward"></span>' +
        '                            </td>' +
        '                            <td :class="innerCurrPage<=1?\'ui-disabled\':\'\'" @click="innerCurrPage<=1?false:prePage()" title="Previous Page"' +
        '                                style="cursor: default;width: 28px;padding: 0 4px;"><span class="glyphicon glyphicon-backward"></span></td>' +
        '                            <td class="ui-disabled" style="cursor: default;width:8px; padding: 0 3px;"><span' +
        '                                        class="ui-separator">|</span></td>' +
        '                            <td >' +
        '                                <div style="height: 36px;width:120px;padding-top: 8px">' +
        '                                    <input @keyup.enter="inputCurrChange" class="form-control" style="display: table-cell;padding: 0 3px;height: 18px;width: 47px;" type="text" size="2" maxlength="7"' +
        '                                           v-model="inputCurrPage"> 共 <span>{{innerTotalPage}}</span> 页' +
        '                                </div>' +
        '                            </td>' +
        '                            <td class="ui-disabled" style="cursor: default;"><span' +
        '                                        class="ui-separator">|</span></td>' +
        '                            <td :class="innerCurrPage>=innerTotalPage?\'ui-disabled\':\'\'" @click="innerCurrPage>=innerTotalPage?false:nextPage()" title="Next Page"' +
        '                                style="cursor: default;width: 28px;padding: 0 5px;"><span class="glyphicon glyphicon-forward"></span></td>' +
        '                            <td :class="innerCurrPage>=innerTotalPage?\'ui-disabled\':\'\'" @click="innerCurrPage>=innerTotalPage?false:lastPage()" title="Last Page"' +
        '                                style="cursor: default;width: 28px;padding: 0 5px;"><span class="glyphicon glyphicon-step-forward"></span>' +
        '                            </td>' +
        '                            <td dir="ltr">' +
        '                                <div style="height: 36px;padding-top: 8px"><select v-model="innerPageSize" @change="sizeChange" class="form-control" style="display: table-cell;padding: 0 3px;height: 18px;width: 47px;" title="Records per Page">' +
        '                                        <option  v-for="item in pageSizes" :value="item">{{item}}</option>' +
        '                                    </select></div>' +
        '                            </td>' +
        '                        </tr>' +
        '                        </tbody>' +
        '                    </table>' +
        '                </td>' +
        '                <td  style="width: 33%; padding:0 8px;">' +
        '                    <div dir="ltr" style="text-align:center;height: 36px;padding-top: 8px;" class="ui-paging-info">{{msg}}</div>' +
        '                </td>' +
        '            </tr>' +
        '            </tbody>' +
        '        </table>',
    methods: {
        currentChange: function () {
            this.$nextTick(function () {
                this.$emit("current-change", this.innerCurrPage, this.innerPageSize);
                this.innerCurrPage = 0;
                this.msg = "";
            });
        },
        inputCurrChange: function () {
            this.inputCurrPage = Number(this.inputCurrPage);
            if (!this.inputCurrPage || this.inputCurrPage < 1) {
                this.inputCurrPage = 1;
            } else if (this.inputCurrPage > this.innerTotalPage) {
                this.inputCurrPage = this.innerTotalPage == 0 ? 1 : this.innerTotalPage;
            }
            this.$emit("current-change", this.inputCurrPage, this.innerPageSize);
        },
        sizeChange: function () {
            this.$nextTick(function () {
                this.$emit("size-change", this.innerCurrPage, this.innerPageSize);
            });
        },
        //点击第一页
        firstPage: function () {
            this.innerCurrPage = 1;
            this.currentChange();
        },
        //上一页
        prePage: function () {
            if (this.innerCurrPage > 1) {
                this.innerCurrPage--;
                this.currentChange();
            }
        },
        //下一页
        nextPage: function () {
            if (this.innerCurrPage < this.innerTotalPage) {
                this.innerCurrPage++;
                this.currentChange();
            }
        },
        //最后一页
        lastPage: function () {
            if (this.innerCurrPage < this.innerTotalPage) {
                this.innerCurrPage = this.innerTotalPage;
                this.currentChange();
            }
        },
        //加载提示信息
        reloadMsg: function () {
            if (this.innerTotalCount === 0) {
                this.msg = "无数据显示";
                return;
            }
            var max = this.innerPageSize * this.innerCurrPage > this.innerTotalCount ? this.innerTotalCount : this.innerPageSize * this.innerCurrPage;
            this.msg = [this.innerPageSize * (this.innerCurrPage - 1) + 1, " - ", max, "  共", this.innerTotalCount, "条"].join("");
        }

    },
    watch: {
        page: {
            handler: function (page, oldPage) {
                this.innerPageSize = page.pageSize;
                this.innerCurrPage = page.currPage;
                this.inputCurrPage = page.currPage;
                this.innerTotalCount = page.totalCount;
                this.innerTotalPage = page.totalPage;
                this.reloadMsg();
            },
            deep: true,
            immediate: true
        },
        pageSize: function (newValue) {
            if (this.page) {
                return;
            }
            this.innerPageSize = Number(newValue);
        },
        currPage: function (newValue, oldValue) {
            if (this.page) {
                return;
            }
            this.innerCurrPage = Number(newValue);
            this.inputCurrPage = Number(newValue);
            if (newValue != oldValue) {
                this.reloadMsg();
            }
        },
        totalCount: function (newValue) {
            if (this.page) {
                return;
            }
            this.innerTotalCount = Number(newValue);
        },
        totalPage: function (newValue) {
            if (this.page) {
                return;
            }
            this.innerTotalPage = Number(newValue);
        }
    },
    computed: {
        finalId: function () {
            var finalId = this.id ? this.id : 'id_' + Math.round(COST_MULTIPLE * Math.random());
            return finalId;
        },
    },
    mounted: function () {
        this.innerPageSize = Number(this.pageSize);
        this.innerCurrPage = Number(this.currPage);
        this.inputCurrPage = Number(this.currPage);
        this.innerTotalCount = Number(this.totalCount);
        this.innerTotalPage = Number(this.totalPage);
        this.reloadMsg();
    }
});

Vue.component('my-date-picker', {
    name: 'my-date-picker',
    props: $.extend({}, publicProp, {
        placeholder: {
            type: String
        },
        type: {
            type: String,
            default: 'date'
        },
        format: {
            type: String,
            default: 'yyyy-MM-dd',
        },
        readonly: {
            type: Boolean,
            default: false
        }
    }),
    template:
        '<div>' +
        '<el-date-picker\n' +
        '    :value="value"\n' +
        '    :type="type"\n' + // 默认值 date
        '    :placeholder="placeholder"\n' +
        '    :format="format"\n' +
        '    :readonly="readonly"' +
        '    popper-class="my-date-time-picker"\n' +
        '    :value-format="format"\n' +
        '    @input="$emit(\'input\', $event)"\n></el-date-picker>' +
        '<input :id="finalId" type="hidden" v-model="value" :rules="rules">' +
        '<label :for="finalId" class="normal-label"></label>' +
        '</div>',
    watch: {
        value: function (nVal) {
            if ($("#" + this.finalId).is(":visible")) {
                if (typeof (nVal) != "undefined" && this.rules.length > 0) {
                    validateInputByValue(nVal, this.finalId, this.rules);
                }
            } else {
                $("label[for='" + this.finalId + "']").removeClass("error-label text-danger").addClass("normal-label");
            }
        }
    },
    computed: {
        finalId: function () {
            return this.id ? this.id : 'id_' + Math.round(COST_MULTIPLE * Math.random());
        }
    }
});


Vue.component('my-datetime-picker', {
    name: 'my-datetime-picker',
    props: $.extend({}, publicProp, {
        placeholder: {
            type: String
        },
        type: {
            type: String,
            default: 'datetime'
        },
        format: {
            type: String,
            default: 'yyyy-MM-dd HH:mm:ss',
        },
        readonly: {
            type: Boolean,
            default: false
        }
    }),
    template:
        '<div>' +
        '<el-date-picker\n' +
        '    :value="value"\n' +
        '    :type="type"\n' + // 默认值 date
        '    :placeholder="placeholder"\n' +
        '    :format="format"\n' +
        '    :readonly="readonly"' +
        '    popper-class="my-date-time-picker"\n' +
        '    :value-format="format"\n' +
        '    @input="$emit(\'input\', $event)"\n></el-date-picker>' +
        '<input :id="finalId" type="hidden" v-model="value" :rules="rules">' +
        '<label :for="finalId" class="normal-label"></label>' +
        '</div>',
    watch: {
        value: function (nVal) {
            if ($("#" + this.finalId).is(":visible")) {
                if (typeof (nVal) != "undefined" && this.rules.length > 0) {
                    validateInputByValue(nVal, this.finalId, this.rules);
                }
            } else {
                $("label[for='" + this.finalId + "']").removeClass("error-label text-danger").addClass("normal-label");
            }
        }
    },
    computed: {
        finalId: function () {
            return this.id ? this.id : 'id_' + Math.round(COST_MULTIPLE * Math.random());
        }
    }
});

var basicTableMixin = {
    methods: {
        query: function (param) {
            if (param === 'drawer') {
                this.drawer = false;
            }
            this.reload('query');
        },
        queryAll: function () {
            //清空查询条件
            this.clearQ();
            this.reload('query');
        },
        clearQ: function () {
            for (var key in this.q) {
                if (key === 'page' || key === 'limit') {
                    continue;
                }
                this.q[key] = '';
            }
        },
    }
};

/**
 * <li>功能描述：table组件，封装分页等查询方法，简化写法。支持跨页选择、自动高度、行点击选中、后端排序</li>
 * 当后端排序时，需要设置el-table-column的属性 sortable="custom" 并通过column-key指定排序的字段名称。
 *
 * props: 支持el-table的所有props属性，另外添加了一些属性。除url属性外，其他属性都有默认值
 * <li>dataType: 数据类型 json-根据设置的url去后台获取;local-本地数据 需要通过pageData属性，传递page对象</li>
 * <li>pageData: 外部传递分页对象,当dataType=local时需要设置这个值</li>
 * <li>url: 请求后端的url，当dataType=json时，需要设置这个值。约定需要分页时后端返回page对象，不分页时后端返回list对象</li>
 * <li>multiselect: 是否显示复选框</li>
 * <li>height: 表格高度，不设置时为自动高度</li>
 * <li>offset: 自动高度的高度偏差，不设置时组件自动计算，设置时使用设置的值</li>
 * <li>showPager：是否显示分页</li>
 * <li>pageSize：初始加载的分页大小</li>
 * <li>pageSizes：可选分页大小数组</li>
 * <li>rowKey: 当需要复选框操作时，或初始加载选中某些行数时，指定行主键，默认为id</li>
 * <li>selectKeys：组件加载时需要选中的数据主键数组</li>
 * <li>selectObjs： 组件加载时需要选中的数据列表，给定数据列表时，将覆盖selectKeys的值，这两个设置一个就可以了</li>
 * <li>fixed： 是否固定左侧序号列和多选框列,默认值false</li>
 * <li>showRowNumber： 是否显示序号列,默认值true</li>
 * <li>defaultQueryParams： 默认查询条件，请求后台时固定不变的查询条件，默认值：{}</li>
 * <li>localSort： 是否前端排序，默认是指false</li>
 * <li>selectable： Function(row,index),类型为 Function，Function 的返回值用来决定这一行的 CheckBox 是否可以勾选</li>
 *
 * 已支持的el-table事件：sort-change,my-selection-change,my-row-click，current-change
 * 已支持的el-pagination事件： page-size-change，current-page-change
 * 自定义事件：query-success(r),参数为后端返回的响应体
 *
 * 为外部提供的方法：
 * <li>search(q,restart) : 发起查询请求，第一个参数为key-value的查询条件，第二个参数为是否从第一页开始</li>
 * <li>getSelectRowKeys(): 获取已选中的数据主键数组，支持跨页选择</li>
 * <li>getSelectRowObjs(): 获取已选中的数据数组，支持跨页选择</li>
 * <li>getPageData(): 获取当前表格数据对应的page对象</li>
 *
 * @author: <EMAIL>
 * @date: 2022/1/28 17:11
 */
Vue.component("my-table", {
    props: $.extend({}, publicProp, {
        //数据类型 json-根据设置的url去后台获取，local-本地数据 需要通过pageData属性，传递page对象
        dataType: {
            type: String,
            default: 'json'
        },
        //请求后端的url，约定需要分页是后端返回page对象，不分页时后端返回list对象
        url: {
            type: String,
            default: ""
        },
        //是否显示复选框
        multiselect: {
            type: Boolean,
            default: true
        },
        //单选时是否显示radio，当multiselect=false 时生效
        showRadio: {
            type: Boolean,
            default: true,
        },
        //是否显示行序号列
        showRowNumber: {
            type: Boolean,
            default: true
        },
        //自动高度的高度偏差，不设置时组件自动计算，设置时使用设置的值
        offset: {
            type: Number,
            default: null
        },
        //外部传递分页对象
        pageData: {
            type: [Object,Array],
            default: null
        },
        //是否分页
        showPager: {
            type: Boolean,
            default: true
        },
        //高度
        height: {
            type: Number,
            default: null
        },
        //可选分页组
        pageSizes: {
            type: Array,
            default: function () {
                return [10, 20, 30, 50, 100];
            }
        },
        //默认分页大小
        pageSize: {
            type: Number,
            default: 10,
        },
        //默认查询条件
        defaultQueryParams: {
            type: Object,
            default: function () {
                return {};
            },
        },
        //唯一标识，
        rowKey: {
            type: String,
            default: 'id'
        },
        //组件加载时需要选中的数据唯一标识
        selectKeys: {
            type: Array,
            default: null
        },
        //组件加载时需要选中的数据列表，给定数据列表时，将覆盖selectKeys的值
        selectObjs: {
            type: Array,
            default: null
        },
        //是否固定左侧序号列和多选框列
        fixed: {
            type: Boolean,
            default: false,
        },
        //是否前端排序
        localSort: {
            type: Boolean,
            default: false,
        },
        //类型为 Function，Function 的返回值用来决定这一行的 CheckBox 是否可以勾选
        selectable: {
            type: Function,
            default: null
        }

    }),
    template: '<div>' +
        '<el-table ref="innerTable" :data="page.list" border v-bind="$attrs" v-on="$listeners"' +
        ' v-loading="loading" :height="inTableHeight" :highlight-current-row="!multiselect"' +
        ' @row-click="handleRowClick" @select="handleSelectionChange" @select-all="handleSelectionChange"' +
        ' @current-change="handleCurrentChange" @sort-change="handleSortChange">' +
        '   <el-table-column :fixed="fixed" v-if="multiselect" align="center" type="selection" :selectable="selectable"></el-table-column>' +
        '   <el-table-column :fixed="fixed"  v-if="!multiselect&&showRadio" label="选择" width="50" align="center" :selectable="selectable">' +
        '       <template slot-scope="scope">' +
        '           <el-radio v-model="radioCheckObj" :label="scope.row" style="margin-left: 7px">{{\'\'}}</el-radio>' +
        '       </template>' +
        '   </el-table-column>' +
        '   <el-table-column :fixed="fixed" v-if="showRowNumber" label="序号" width="55" align="center" :index="page.pageSize * (page.currPage - 1) + 1" type="index"></el-table-column>' +
        '   <slot></slot>' +
        '</el-table>' +
        '<el-pagination style="margin-top: 5px;margin-right: 30px;text-align: right;"' +
        '      v-if="showPager" background' +
        '      @size-change="handleSizeChange"' +
        '      @current-change="handleCurrentPageChange"' +
        '      :current-page="page.currPage"' +
        '      :page-sizes="pageSizes"' +
        '      :page-size="page.pageSize"' +
        '      layout="total, sizes, prev, pager, next, jumper"' +
        '      :total="page.totalCount">' +
        '    </el-pagination>' +
        '</div>',
    data: function () {
        return {
            //分页数据
            page: {
                list: [],
                totalCount: 0,
                pageSize: 15,
                totalPage: 0,
                currPage: 1
            },
            //加载数据标记
            loading: false,
            //表格高度
            inTableHeight: 300,
            //查询参数，q对象
            queryParams: {},
            //选中的rowKey,支持跨页选择
            finalSelectRowKeys: [],
            //选中的数据，支持跨页选择。
            finalSelectObjs: [],
            radioCheckObj: {},
        };
    },
    methods: {
        //给外部调用的，发起请求方法
        search: function (q, restart) {
            // if (typeof (restart) == "boolean" && restart) {
            if (this.selectObjs) {
                this.finalSelectObjs = this.selectObjs;
                this.finalSelectRowKeys = this.selectObjs.map(function (e) {
                    return e[this.rowKey];
                });
            } else if (this.selectKeys) {
                this.finalSelectRowKeys = this.selectKeys;
            } else {
                this.finalSelectObjs = [];
                this.finalSelectRowKeys = [];
            }
            // }
            this.queryParams = q || {};
            this.page.currPage = typeof (restart) == "boolean" && restart ? 1 : this.page.currPage;
            this.queryPageData();
        },
        //给外部提供一个获取选中id的方法
        getSelectRowKeys: function () {
            return this.finalSelectRowKeys.length == 0 ? null : this.finalSelectRowKeys;
        },
        //给外部提供一个获取选中对象的方法
        getSelectRowObjs: function () {
            return this.finalSelectObjs.length == 0 ? null : this.finalSelectObjs;
        },
        //给外部提供获取page对象的方法
        getPageData: function () {
            return this.page;
        },
        //点击排序
        handleSortChange: function (opt) {
            if (this.localSort) {
                return;
            }
            if (opt.order == 'descending') {
                this.queryParams.sidx = opt.column.columnKey || opt.prop;
                this.queryParams.order = "desc";
            } else if (opt.order == 'ascending') {
                this.queryParams.sidx = opt.column.columnKey || opt.prop;
                this.queryParams.order = "asc";
            } else {
                this.queryParams.sidx = "";
                this.queryParams.order = "";
            }
            this.page.currPage = 1;
            this.queryPageData();
            this.$emit("sort-change", opt);
        },
        //行选择发生变化。selection-change
        handleSelectionChange: function (selection) {
            var that = this;
            //选中的加入到最终列表
            var checkRowKeys = [];
            for (var i = 0; i < selection.length; i++) {
                var rowKey = selection[i][that.rowKey];
                checkRowKeys.push(rowKey);
                //不重复添加
                if (that.finalSelectRowKeys.indexOf(rowKey) < 0) {
                    that.finalSelectRowKeys.push(rowKey);
                    that.finalSelectObjs.push(selection[i]);
                }
            }
            //没选中的移出最终列表，
            var pageRowKeys = that.page.list.map(function (e) {
                return e[that.rowKey];
            });
            that.finalSelectRowKeys = that.finalSelectRowKeys.filter(function (e) {
                return pageRowKeys.indexOf(e) < 0 ? true : checkRowKeys.indexOf(e) >= 0;
            });
            that.finalSelectObjs = that.finalSelectObjs.filter(function (e) {
                return that.finalSelectRowKeys.indexOf(e[that.rowKey]) >= 0;
            });
            //最终数据抛出到父组件
            that.$emit("my-selection-change", that.finalSelectObjs);
        },
        //行点击事件
        handleRowClick: function (row, column, event) {
            if (column.columnKey == 'caozuo' || column.label == '操作') {
                return;
            }
            var that = this;
            if (this.multiselect) {
                if (this.selectable && !this.selectable(row)) {
                    return;
                }

                var rowKey = row[this.rowKey];
                if (this.finalSelectRowKeys.indexOf(rowKey) < 0) {
                    this.$refs.innerTable.toggleRowSelection(row, true);
                    this.finalSelectRowKeys.push(rowKey);
                    this.finalSelectObjs.push(row);
                } else {
                    this.$refs.innerTable.toggleRowSelection(row, false);
                    this.finalSelectRowKeys = this.finalSelectRowKeys.filter(function (e) {
                        return e != rowKey;
                    });
                    that.finalSelectObjs = that.finalSelectObjs.filter(function (e) {
                        return that.finalSelectRowKeys.indexOf(e[that.rowKey]) >= 0;
                    });
                }
                that.$emit("my-selection-change", that.finalSelectObjs);
            }else{
                that.radioCheckObj = row;
                that.finalSelectRowKeys = [row[that.rowKey]];
                that.finalSelectObjs = [row];
                that.$emit("my-selection-change", that.finalSelectObjs);
            }
            this.$emit("my-row-click", row, column, event);
        },
        //单选时，选中行
        handleCurrentChange: function (row, oldRow) {
            if (!this.multiselect) {
                this.finalSelectObjs = [row];
                this.finalSelectRowKeys = [row[this.rowKey]];
            }
            this.$emit("current-change", row, oldRow);
        },
        //分页大小变化
        handleSizeChange: function (pageSize) {
            this.page.pageSize = pageSize;
            this.queryPageData();
            this.$emit("page-size-change", pageSize);
        },
        //当前页变化
        handleCurrentPageChange: function (currPage) {
            this.page.currPage = currPage;
            this.queryPageData();
            this.$emit("current-page-change", currPage);
        },
        //请求后台获取数据
        queryPageData: function () {
            if (!this.url) {
                return;
            }
            var that = this;
            that.loading = true;
            this.queryParams.page = this.page.currPage;
            this.queryParams.limit = this.page.pageSize;
            //为了兼容旧版本后台，这里查询条件通过请求体，请求参数两种方式发送，后台推荐用Query类@RequestBody接收数据
            var url = "";
            if (this.url.indexOf("?") != -1) {
                url = this.url + "&_=" + new Date().getTime();
            }else{
                url = this.url + "?_=" + new Date().getTime();
            }
            this.$http.post(url, this.queryParams, {params: this.queryParams})
                .then(function (r) {
                    // 出发请求成功事假
                    that.$emit("query-success",r)
                    that.loading = false;
                    if (that.showPager) {
                        that.page = r.page;
                    } else {
                        that.page.list = r.list;
                    }
                    that.$nextTick(function () {
                        that.toggleRowSelection();
                    });
                }).catch(function (r) {
                that.loading = false;
            });
        },
        //处理数据反选
        toggleRowSelection: function () {
            var that=this;
            //选中以前选的数
            for (var i = 0; i < that.page.list.length; i++) {
                var rowKey = that.page.list[i][that.rowKey];
                if (!rowKey) {
                    alert("row-key属性设置错误")
                    break
                }
                if (that.finalSelectRowKeys.indexOf(rowKey) >= 0) {
                    that.$refs.innerTable.toggleRowSelection(that.page.list[i], true);
                    //当传递select-keys时，有可能在finalSelectObjs中没有值，这里处理一次，
                    let find=that.finalSelectObjs.find(function (e) {
                        return e[that.rowKey] == rowKey;
                    });
                    if(!find) {
                        that.finalSelectObjs.push(that.page.list[i]);
                    }
                    //单选的选中
                    that.radioCheckObj = that.page.list[i];

                }else{
                    that.$refs.innerTable.toggleRowSelection(that.page.list[i], false);
                }
            }
            that.changeTableHeight();

        },
        //设置表格高度
        changeTableHeight: function () {
            if (!this.$refs.innerTable) {
                return;
            }
            if (this.height) {
                //如果有传进来高度就取消自适应
                this.inTableHeight = this.height;
                this.$refs.innerTable.doLayout();
                return;
            }
            var tableHeight = window.innerHeight || document.body.clientHeight;
            if (this.offset) {
                //prop给了偏差高度，按照给定的设置
                tableHeight -= this.offset;
                this.inTableHeight = tableHeight < 300 ? 300 : tableHeight;
                this.$refs.innerTable.doLayout();
                return;
            }
            //计算表格上方的元素高度，和底部的元素高度，自动计算一个值
            var disTop = this.$refs.innerTable.$el;

            //如果表格上方有元素则减去这些高度适应窗口，66是底下留白部分
            tableHeight -= disTop.offsetTop + 60;
            if (disTop.offsetParent) tableHeight -= disTop.offsetParent.offsetTop;
            this.inTableHeight = tableHeight < 300 ? 300 : tableHeight;
            //重绘表格
            this.$refs.innerTable.doLayout();
        }
    },
    watch: {
        url: function (nVal) {
            this.queryPageData();
        },
        pageData: {
            handler: function (nValue) {
                if (nValue) {
                    if (AppUtils.isArray(nValue)) {
                        this.page = {
                            list: nValue,
                            totalCount: nValue.length,
                            pageSize: nValue.length,
                            totalPage: 1,
                            currPage: 1
                        };
                    } else {
                        this.page = nValue;
                    }
                    this.$nextTick(function () {
                        this.changeTableHeight();
                    });
                }
            },
            deep: true
        },
        height: function (nval) {
            if (nval) {
                this.inTableHeight = nval;
                this.$nextTick(function () {
                    this.changeTableHeight();
                });
            }
        },
        selectKeys: function (n) {
            this.finalSelectRowKeys = n;
            this.toggleRowSelection();
        },
        selectObjs: function (n) {
            var that=this;
            this.finalSelectObjs = n;
            if (n) {
                this.finalSelectRowKeys = n.map(function (ele) {
                    return ele[that.rowKey];
                });
            }else{
                this.finalSelectRowKeys = [];
            }
            var that=this;
            this.$nextTick(function () {
                that.toggleRowSelection();
            });
        }
    },
    mounted: function () {
        var that = this;
        //允许外部指定分页大小
        if (this.pageSize) {
            this.page.pageSize = this.pageSize;
        }

        //获取行数据
        if (this.dataType == 'json') {
            this.queryPageData();
        } else if (this.dataType == 'local' && this.pageData) {
            if (AppUtils.isArray(this.pageData)) {
                this.page = {
                    list: this.pageData,
                    totalCount: this.pageData.length,
                    pageSize: this.pageData.length,
                    totalPage: 1,
                    currPage: 1
                };
            } else {
                this.page = this.pageData;
            }
        }
        this.$nextTick(function () {
            //加载时需要选中的数据
            if (that.selectObjs) {
                that.finalSelectObjs = that.selectObjs;
                that.finalSelectRowKeys = that.selectObjs.map(function (e) {
                    return e[that.rowKey];
                });
                that.toggleRowSelection();
            } else if (that.selectKeys) {
                that.finalSelectRowKeys = that.selectKeys;
                that.toggleRowSelection();
            }
            if (this.height) {
                this.inTableHeight = this.height;
                this.changeTableHeight();
            }

            //表格高度自适应浏览器大小
            if (!window.resizeHandlers) {
                window.resizeHandlers = [];
            }
            window.resizeHandlers.push(function () {
                that.changeTableHeight();
            });
            window.onresize = function () {
                for (var i = 0; i < window.resizeHandlers.length; i++) {
                    window.resizeHandlers[i].call();
                }
            };
        });
        //监听浏览器标签页切换
        document.addEventListener("visibilitychange", function () {
            if (!document.hidden) {
                that.changeTableHeight();
            }
        });
    }
})

var downloadAFileMixin = {
    methods: {
        //按文件 id 下载一个文件
        downloadAFile: function (fileId) {
            if (fileId) {
                window.location.href = baseUrl + "com/uploadfile/download/" + fileId;
            } else {
                alert("无文件可下载");
            }
        },
    }
};

