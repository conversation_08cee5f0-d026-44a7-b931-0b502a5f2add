<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.reports.dao.DiversionResponsiblePersonDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.reports.entity.DiversionResponsiblePerson" id="diversionResponsiblePersonMap">
        <result property="id" column="ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="reverName" column="REVER_NAME"/>
        <result property="areaCode" column="AREA_CODE"/>
        <result property="status" column="STATUS"/>
        <result property="startAddr" column="START_ADDR"/>
        <result property="endAddr" column="END_ADDR"/>
        <result property="responsibleXian" column="RESPONSIBLE_XIAN"/>
        <result property="positionXian" column="POSITION_XIAN"/>
        <result property="responsibleXiang" column="RESPONSIBLE_XIANG"/>
        <result property="positionXiang" column="POSITION_XIANG"/>
        <result property="responsibleCun" column="RESPONSIBLE_CUN"/>
        <result property="positionCun" column="POSITION_CUN"/>
        <result property="responsibleCompetentDept" column="RESPONSIBLE_COMPETENT_DEPT"/>
        <result property="positionCompetentDept" column="POSITION_COMPETENT_DEPT"/>
        <result property="responsibleSupervise" column="RESPONSIBLE_SUPERVISE"/>
        <result property="positionSupervise" column="POSITION_SUPERVISE"/>
        <result property="responsibleEnforce" column="RESPONSIBLE_ENFORCE"/>
        <result property="positionEnforce" column="POSITION_ENFORCE"/>
        <result property="responsibleDiversion" column="RESPONSIBLE_DIVERSION"/>
        <result property="positionDiversion" column="POSITION_DIVERSION"/>
    </resultMap>
    <select id="list"  resultType="com.ybkj.smm.modules.reports.entity.DiversionResponsiblePerson">
        select
            persion.*
        from smm_reports_diversion_responsible_person persion
        left join smm_reports_diversion_person_report report on persion.report_id = report.id
        <where>
            ${ew.sqlSegment}
        </where>
        order by AREA_CODE asc,REVER_NAME asc
    </select>


</mapper>
