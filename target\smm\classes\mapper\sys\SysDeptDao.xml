<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.sys.dao.SysDeptDao">
    <update id="updateUserDeptId">
        update sys_user u,sys_dept d
            set u.DEPT_ID=#{deptId}
            <where>
                ${ew.sqlSegment}
            </where>
    </update>

    <select id="queryDetpIdList">
		select dept_id from sys_dept where  del_flag = 0
		<if test="parentId!=null and parentId!=''">
            and parent_id = #{parentId}
        </if>
	</select>
    <select id="queryList" resultType="com.sloth.modules.sys.entity.SysDeptEntity">
        SELECT dept.*, parent.NAME parentName
        FROM sys_dept dept
            LEFT JOIN sys_dept parent ON dept.PARENT_ID = parent.DEPT_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="selectDeptIdList" resultType="java.lang.String">
        select DEPT_ID from sys_dept
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="selectMaxCode" resultType="java.lang.String">
        select SHORT_CODE from sys_dept where PARENT_ID=#{parentId}  order by SHORT_CODE desc limit 1
    </select>
    <select id="getByIdWithDeleted" resultType="com.sloth.modules.sys.entity.SysDeptEntity">
        select *
        from sys_dept
        where
        DEPT_ID=#{deptId}
        <if test="!withDeleted">and !DEL_FLAG</if>
    </select>
    <select id="getSubDeptIdList" resultType="java.lang.String">
        with RECURSIVE dept as (
            select a.* from sys_dept a
            where a.parent_id in (
                <foreach collection="deptIds" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            union all
            select b.* from sys_dept b join dept on dept.dept_id=b.parent_id
        )
        select * from dept where del_flag=0

    </select>
</mapper>
