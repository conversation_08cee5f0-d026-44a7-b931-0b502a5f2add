.el-tabs__header{
    padding: 0 40px 0 0;
    background-color: #FFFFFF;
    color: #303133;
    margin-bottom: 5px;
}
.el-tabs__nav-wrap {
    /*background-color: #FFFFFF;*/
    /*coler: #303133;*/
    padding-left: 15px;
    /*padding-right:45px;*/
}
.el-tabs__item{
    padding: 0 10px;
    margin-left: 1px;
}
.el-tabs--top .el-tabs__item.is-top:nth-child(2) {
    padding-left: 10px;
}
.el-tabs--top .el-tabs__item.is-top:last-child {
    padding-right: 10px;
}
.el-tabs__item.is-active {
    background-color: #f5f7fa;
}
.el-tabs__item.is-active::after {
    display: block;
    position: absolute;
    bottom: 0;
    left: 0;
    content: "";
    width: 100%;
    height: 2px;
    background-color: #409EFF;
}
.el-tabs__item:hover{
   background-color: #f5f7fa;
}
.el-tabs__item:hover::after {
    display: block;
}
.el-tabs__item::after {
    display: none;
    position: absolute;
    bottom: 0;
    left: 0;
    content: "";
    width: 100%;
    height: 2px;
    background-color: #409EFF;
}
.tab-tools{
    position: fixed;
    top: 50px;
    right: 0;
    z-index: 931;
    min-width: 40px;
    height: 40px;
    padding: 0 12px;
    text-align: center;
    font-size: 16px;
    line-height: 40px;
    background-color: #f5f7fa;
    cursor: pointer
}
.tab-tools::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background-color: #E4E7ED;
    z-index: 1;
}