::
:: windows下java项目启动的通用脚本，配合winsm实现把java项目注册为系统服务。
::
@echo off
:: 设置窗口title
:: title 权限管理系统
:: 设置变量延迟赋值
setlocal enabledelayedexpansion
cls
cd ..
::Java应用根目录
set APP_HOME=%cd%

::JAVA_HOME目录
::set JAVA_HOME=%APP_HOME%\jre

::需要启动的Java类
set APP_MAINCLASS=com.ybkj.smm.SmmApplication

::classpath参数，包括指定lib目录下所有的jar
set CLASSPATH=%APP_HOME%\classes
::For /r "%APP_HOME%\lib" %%f in (*.jar) do (
::	set CLASSPATH=!CLASSPATH!;%%f::
::)
set CLASSPATH=%CLASSPATH%;%APP_HOME%\lib\*;

::java虚拟机启动参数, MaxPermSize=128m; support was removed in 8.0
set JAVA_OPTS=-Xms512m -Xmx512m -Xmn256m -Djava.awt.headless=true -XX:MaxPermSize=128m -Dlog4j2.formatMsgNoLookups=true

::打印环境信息
echo System Information:
echo ********************************************************
echo COMPUTERNAME=%COMPUTERNAME%
echo OS=%OS%
echo.
echo APP_HOME=%APP_HOME%
echo JAVA_HOME=%JAVA_HOME%
echo APP_MAINCLASS=%APP_MAINCLASS%
echo CLASSPATH=%CLASSPATH%
echo CURRENT_DATE=%date% %time%:~0,8%
echo ********************************************************

::执行java
echo Starting %APP_MAINCLASS% ...
echo.
"%JAVA_HOME%\bin\java" -classpath %CLASSPATH% %JAVA_OPTS% %APP_MAINCLASS%
:exit
pause
