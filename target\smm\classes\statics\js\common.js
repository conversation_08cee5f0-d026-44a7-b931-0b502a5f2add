var __self = window.self;
var __top = window.top; // 有时浏览器报错， window、 top、 location 未定义的问题。

//jqGrid的配置信息
if (typeof ($.jgrid) != "undefined") {
    $.jgrid.defaults = {
        width: 1000,
        responsive: true,
        styleUI: 'Bootstrap',
        viewrecords: true,
        height: 'auto',
        rowNum: 10,
        rowList: [10, 30, 50, 100],
        rownumbers: true,
        rownumWidth: 55,
        autowidth: true,
        multiselect: true,
        pager: "#jqGridPager",
        jsonReader: {
            root: "page.list",
            page: "page.currPage",
            total: "page.totalPage",
            records: "page.totalCount"
        },
        prmNames: {
            page: "page",
            rows: "limit",
            order: "order"
        },
        //禁用操作按钮行点击事件
        beforeSelectRow: function (rowid, e) {
            var isBtn = $(e.target).hasClass("btn") || $(e.target).hasClass("fa");
            return !isBtn;
        },
        gridComplete: function () {
            //隐藏grid底部滚动条，和最小高度
            $(this).closest(".ui-jqgrid-bdiv").css({"overflow-x": "hidden", "min-height": "300px"});
            //显示排序图标
            addSortAbleCss(this);
        }
    }
    $(function () {
        /**
         * 解决jqgrid 复选框和行点击时间不同步问题 v5.1.0版本需要此方法
         */
        $('body').on("click", ".ui-jqgrid-bdiv input[type='checkbox']", function () {
            var checked = $(this).is(':checked');
            var thisId = $(this).attr("id");
            if (!thisId) {
                return;
            }

            var arr = thisId.split("_");
            var jqGridId = arr[1];
            var indexOf = thisId.indexOf(jqGridId);
            var rowId = thisId.substring(indexOf + jqGridId.length + 1, thisId.length);
            $("#" + jqGridId).jqGrid("setSelection", rowId, true);//第三个参数为true，表示会触发onSelectRow事件
        });
        /**
         * 给jqGrid表头加上title
         */
        $("body").on("DOMNodeInserted", ".ui-th-column", function () {
            $(this).attr("title", $(this).text());
        });
    });

    /**
     * 处理排序箭头的hover显示
     */
    function addSortAbleCss(e) {
        var colArr = $(e).jqGrid("getGridParam", "colModel");
        for (var i = 0; i < colArr.length; i++) {
            var col = colArr[i];
            if (col.sortable) {
                $("#jqgh_" + $(e).attr("id") + "_" + col.name).hover(
                    function () {
                        $(this).find(".s-ico").css("display", "inline");
                    },
                    function () {
                        var arr = $(this).find(".s-ico").find(".ui-disabled");
                        if (arr.length == 2) {
                            $(this).find(".s-ico").css("display", "none")
                        }
                    }
                );
            }
        }
    }

}

var baseURL = "../../";
var baseUrl = baseURL;

//工具集合
window.AppUtils = top.AppUtils || {};
//字典工具集合
window.DictUtils = top.DictUtils || {};
//区域工具集合
window.AreaUtils = top.AreaUtils || {};
//字典本地存储的前缀
var DICT_PREFIX = "sloth.dict.";
//区域数据本地缓存前缀
var AREA_PREFIX = "sloth.area.";
var storage = top.storage || window.sessionStorage || {};


// 获取请求参数
// 使用示例
// location.href = http://localhost:8080/index.html?id=123
// AppUtils.p('id') --> 123;
AppUtils.p = function (name, defaultValue) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) {
        return unescape(r[2]);
    }
    return defaultValue == null ? "" : defaultValue;
};

//获取url传递的参数，使用encodeURI/decodeURI 编码/解码
AppUtils.getUrlParam = function (paramName, defaultValue) {
    var url = document.location.toString();
    return AppUtils.getParam(url, paramName, defaultValue);
};
AppUtils.getParam = function (url, paramName, defaultValue) {
    var arrObj = url.split("?");

    if (arrObj.length > 1) {
        var arrPara = arrObj[1].split("&");
        var arr;

        for (var i = 0; i < arrPara.length; i++) {
            arr = arrPara[i].split("=");

            if (arr != null && arr[0] == paramName) {
                return decodeURI(arr[1]);
            }
        }
        return defaultValue == null ? "" : defaultValue;
    } else {
        return defaultValue == null ? "" : defaultValue;
    }
};
//获取所有url参数，放到一个对象中
AppUtils.getUrlParams = function () {
    var map = {};
    var url = document.location.toString();
    var arrObj = url.split("?");
    if (arrObj.length > 1) {
        var arrPara = arrObj[1].split("&");
        var arr;
        for (var i = 0; i < arrPara.length; i++) {
            arr = arrPara[i].split("=");
            if (arr != null && arr.length > 1) {
                map[arr[0]] = decodeURI(arr[1]);
            }
        }
    }
    return map;
};
/**
 * 对象复制，根据目标对象的属性，进行复制
 * @param source
 * @param target
 */
AppUtils.copyProperties = function (source, target) {
    for (var i in target) {
        if (source[i] != null && typeof (source[i]) != "undefined") {
            target[i] = source[i];
        }
    }
};
/**
 *<li>功能描述：通过url中的参数，来初始化map对象的属性值</li>
 * @author: <EMAIL>
 * @date:  2020/8/7 12:52
 */
AppUtils.getQueryParamsByUrl = function (map) {
    AppUtils.copyProperties(AppUtils.getUrlParams(), map);
    return map;
}
//页面跳转
AppUtils.toPage = function (url) {
    if (self != top) {
        url = url.replace(/\.\.\//g, "");
        // top.location.hash = url;
        window.location.href = "/"+url;
    } else {
        // top.location.hash = url;
        window.location.href = "/"+url;
    }
};


/**
 * 由于typeof 在判断数组时不靠谱，添加这个方法
 * @param o
 */
AppUtils.isArray = function (o) {
    return Object.prototype.toString.call(o) === '[object Array]';
};

//js对象深度克隆方法
AppUtils.cloneObj = function (val) {
    return JSON.parse(JSON.stringify(val));
};
//js Date 格式化方法
AppUtils.formatDate = function (date, format) {
    var time = {
        "M+": date.getMonth() + 1
        , "d+": date.getDate()
        , "h+": date.getHours()
        , "m+": date.getMinutes()
        , "s+": date.getSeconds()
        , "q+": Math.floor((date.getMonth() + 3) / 3)
        , "S+": date.getMilliseconds()
    };
    if (/(y+)/i.test(format)) {
        format = format.replace(RegExp.$1, (date.getFullYear() + '')).substr(4 - RegExp.$1.length);
    }
    for (var k in time) {
        if (new RegExp('(' + k + ')').test(format)) {
            format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? time[k] : ('00' + time[k]).substr(('' + time[k]).length));
        }
    }
    return format;

}
/**
 * 把时间字符串转为js Date对象，注意：按照位数截取的，可能并不通用
 */
AppUtils.parseDate = function (dateString) {
    var year = dateString.substr(0, 4);
    var month = dateString.substr(5, 2);
    var day = dateString.substr(8, 2);
    if (dateString.length > 10) {
        var hour = dateString.substr(11, 2);
        var min = dateString.substr(14, 2);
        var sec = dateString.substr(17, 2);
        return new Date(year, parseInt(month) - 1, day, hour, min, sec);
    } else {
        return new Date(year, parseInt(month) - 1, day);
    }
}

/**
 * 拼接列表页的查询条件，用于页面携参跳转
 * @param obj 如：vm.q
 */
AppUtils.getListParam = function (obj) {
    var paramStr = '';
    for (var key in obj) {
        paramStr += key + "=" + encodeURI(obj[key]) + "&";
    }
    return paramStr;
}
//显示加载遮罩
AppUtils.loadingStart = function (msg, callback) {
    if ($(window.parent.document).find("#loadingDiv").length == 0) {
        var $loadingDiv = "<div id=\"loadingDiv\" class=\"el-loading-mask is-fullscreen\" style=\"background-color: rgba(0, 0, 0, 0.3);z-index:20120915\">\n" +
            "    <div class=\"el-loading-spinner\"><i class=\"el-icon-loading\"></i>\n" +
            "        <p id='loadingMsg' class=\"el-loading-text\">拼命加载中</p>\n" +
            "\t</div>\n" +
            "</div>";
        $(window.parent.document).find("body").append($loadingDiv);
    }
    if (typeof (msg) != "undefined" && msg != '') {
        $(window.parent.document).find("#loadingMsg").text(msg);
    } else {
        $(window.parent.document).find("#loadingMsg").text("拼命加载中");
    }
    if (typeof (callback) === "function") {
        $(window.parent.document).find("#loadingDiv").show(function () {
            callback()
        });
    } else {
        $(window.parent.document).find("#loadingDiv").show();
    }

}
//隐藏加载遮罩
AppUtils.loadingEnd = function () {
    $(window.parent.document).find("#loadingDiv").hide();
}

//弹出错误信息
AppUtils.error = function (r) {
    console.log("r:" + r);
    alert("错误码:&nbsp;" + r.code + "；<br>" + "错误详情:&nbsp;" + r.msg);
}

//判断是移动端还是PC访问，true-移动端，false-pc端
AppUtils.isMobile = function () {
    return /iOS|Android|webOS|iPhone|iPod|BlackBerry|MicroMessenger/i.test(navigator.userAgent);
}

//格式化为千分符两位小数的字符串
AppUtils.currencyFormat = function (num) {
    if (typeof num == 'number') {
        return num.toLocaleString('zh-CN', {maximumFractionDigits: 2, minimumFractionDigits: 2});
    } else {
        return num;
    }
    // num = parseFloat(num);
    // num = num.toFixed(2);
    // num = parseFloat(num);
    // return num.toLocaleString('zh-CN', {maximumFractionDigits: 2, minimumFractionDigits: 2});;
};

//格式化为千分符的字符串
AppUtils.thousandsFormat = function (num, minDecimalPlaces, maxDecimalPlaces) {
    if (typeof num == 'number') {
        var options = {};
        if (minDecimalPlaces !== undefined) {
            options.minimumFractionDigits = minDecimalPlaces;
        }
        if (maxDecimalPlaces !== undefined) {
            options.maximumFractionDigits = maxDecimalPlaces;
        } else {
            options.maximumFractionDigits = 16;
        }
        return num.toLocaleString('zh-CN', options);
    } else {
        return num;
    }
    // num = parseFloat(num);
    // num = num.toFixed(2);
    // num = parseFloat(num);
    // return num.toLocaleString('zh-CN', {maximumFractionDigits: 2, minimumFractionDigits: 2});;
};

//千分符转换为数字
AppUtils.fromThousands = function (num) {
    num = num.replace(/,/g, '');
    return parseFloat(num);
};
//子页面更新待办任务个数
AppUtils.updateTodoCount = function (url) {
    if (self != top) {
        if (typeof (url) == "undefined") {
            url = top.location.hash.replace("#", '');
        }
        top.vm.getMenuTodoCountByUrl(url);
    }
};
//获取uuid，带“-”
AppUtils.uuid = function () {
    var s = [];
    var hexDigits = "0123456789abcdef";
    for (var i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
    }
    s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
    s[8] = s[13] = s[18] = s[23] = "-";
    return s.join("");
}
//获取uuid，不带-
AppUtils.uuid2 = function () {
    var s = [];
    var hexDigits = "0123456789abcdef";
    for (var i = 0; i < 32; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
    }
    s[12] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
    return s.join("");
}

//另一个 js对象，深度克隆方法
function cloneObj(obj) {
    var newObj = {};
    if (obj instanceof Array) {
        newObj = [];
    }
    for (var key in obj) {
        var val = obj[key];
        newObj[key] = typeof val === 'object' ? cloneObj(val) : val;
    }
    return newObj;
}

//全局配置
$.ajaxSetup({
    beforeSend: function (XMLHttpRequest) {
        XMLHttpRequest.setRequestHeader("appId", AppUtils.getUrlParam("appId"));
    },
    //设置ajax请求结束后的执行动作
    complete: function (XMLHttpRequest, textStatus) {
        // 通过XMLHttpRequest取得响应头，REDIRECT
        var redirect = XMLHttpRequest.getResponseHeader("REDIRECT");//若HEADER中含有REDIRECT说明后端想重定向
        if (redirect == "REDIRECT") {
            //将后端重定向的地址取出来,使用win.location.href去实现重定向的要求
            confirm("您的登录信息已超时，请重新登录", function () {
                top.location.href = XMLHttpRequest.getResponseHeader("CONTEXTPATH");
            });
        }
    },
    dataType: "json",
    cache: false
});

//重写alert
window.alert = function (msg, callback) {
    top.vm.$alert(msg, '信息', {
        confirmButtonText: '确定',
        callback: callback,
        type: 'info',
    });
   /* parent.layer.alert(msg, {scrollbar: false}, function (index) {
        parent.layer.close(index);
        if (typeof (callback) === "function") {
            callback("ok");
        }
    });*/
}

//重写confirm式样框
window.confirm = function (msg, callback) {
    top.vm.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        callback: function (action, instance) {
            if (action == 'confirm') {
                if (typeof (callback) === "function") {
                    callback("ok");
                }
            }
        }
    });
    /*parent.layer.confirm(msg, {btn: ['确定', '取消'], scrollbar: false},
        function (index) {//确定事件
            parent.layer.close(index);
            if (typeof (callback) === "function") {
                callback("ok");
            }
        });*/
}

//选择一条记录
function getSelectedRow(jgGridId) {
    if (typeof (jgGridId) == "undefined" || jgGridId == '') {
        var grid = $("#jqGrid");
    } else {
        var grid = $("#" + jgGridId);
    }
    var rowKey = grid.getGridParam("selrow");
    if (!rowKey) {
        alert("请选择一条记录");
        return;
    }

    var selectedIDs = grid.getGridParam("selarrrow");
    if (selectedIDs.length > 1) {
        alert("只能选择一条记录");
        return;
    }

    return selectedIDs[0];
}

//选择多条记录
function getSelectedRows(jgGridId) {
    if (typeof (jgGridId) == "undefined" || jgGridId == '') {
        var grid = $("#jqGrid");
    } else {
        var grid = $("#" + jgGridId);
    }
    var rowKey = grid.getGridParam("selrow");
    if (!rowKey) {
        alert("请选择一条记录");
        return;
    }
    return grid.getGridParam("selarrrow");
}

//判断是否为空
function isBlank(value) {
    return !value || !/\S/.test(value)
}

/**
 * 判断字符串是否以指定字符串开始
 * @param str
 * @returns {boolean}
 */
String.prototype.startWith = function (str) {
    var reg = new RegExp("^" + str);
    return reg.test(this);
}

/**
 * 判断字符串是否以指定字符串结束
 * @param str
 * @returns {boolean}
 */
String.prototype.endWith = function (str) {
    var reg = new RegExp(str + "$");
    return reg.test(this);
}

/**
 * <li>功能描述：通过字典的pvalue 获取子项字典list，优先从本地缓存中获取</li>
 * @author: <EMAIL>
 * @date: 2021/1/25 9:06
 * @param pValues 字典的pvalue 支持数组或多个值逗号分隔
 * @param callback 回调函数，参数为子项字典的map，以pvalue为key
 */
DictUtils.cList = function (pValues, callback) {
    var result = {};
    var pValueArr = new Array();
    if (typeof (pValues) == 'array') {
        pValueArr = pValues;
    } else if (pValues.indexOf(",") != -1) {
        pValueArr = pValues.split(",");
    } else {
        pValueArr.push(pValues);
    }
    var notExistsPvalue = "";
    pValueArr.map(function (pValue) {
        var subList = storage.getItem(DICT_PREFIX + pValue);
        if (subList) {
            result[pValue] = JSON.parse(subList);
        } else {
            notExistsPvalue += pValue + ",";
        }
    });
    if (notExistsPvalue.length > 0) {
        notExistsPvalue = notExistsPvalue.substr(0, notExistsPvalue.length - 1)
        $.ajax({
            type: "POST",
            url: baseURL + "sys/dict/cList?pValues=" +notExistsPvalue ,
            contentType: "application/json",
            async: false,
            success: function (r) {
                if (r.code == 0) {
                    for (var key in r) {
                        if (key == 'code' || key == 'msg') {
                            continue;
                        }
                        storage.setItem(DICT_PREFIX + key, JSON.stringify(r[key]));
                        result[key] = r[key];
                    }
                } else {
                    alert(r.msg);
                }
            }
        });
    }
    if (typeof (callback) == "function") {
        callback(result);
    }
    return result;
}
/**
 * <li>功能描述：通过pvalue 获取子项字典的map，优先从本地缓存获取</li>
 * @author: <EMAIL>
 * @date: 2021/1/25 9:56
 */
DictUtils.cMap = function (pValues, callback) {
    var result = {};
    DictUtils.cList(pValues, function (r) {
        for (var key in r) {
            var dictMap = {};
            var dictList = r[key];
            for (var index in dictList) {
                dictMap[dictList[index].value] = dictList[index];
            }
            result[key] = AppUtils.cloneObj(dictMap);
        }
    })
    if (typeof (callback) == "function") {
        callback(result);
    }
    return result;
}

/**
 *<li>功能描述：获取子项字典的名称</li>
 * @author: <EMAIL>
 * @date:  2021/12/21 10:10
 */
DictUtils.getSubDict = function (pvalue, value) {
    let cMap = DictUtils.cMap(pvalue);
    if (!cMap || !cMap[pvalue] || !cMap[pvalue][value]) {
        return "--";
    }
    return cMap[pvalue][value]['name'];
};

/**
 *<li>功能描述：获取子区域列表，优先从本地缓存获取</li>
 * @author: <EMAIL>
 * @date:  2021/10/13 17:38
 */
AreaUtils.getSubList = function (areaCode, callback) {
    var result = [];
    var subList = storage.getItem(AREA_PREFIX + areaCode);
    if (subList) {
        result = JSON.parse(subList);
    } else {
        $.ajax({
            type: "POST",
            url: baseURL + "sys/area/getAreaList?areaCode=" + areaCode,
            contentType: "application/json",
            async: false,
            success: function (r) {
                if (r.code == 0) {
                    result = r.areaList;
                    storage.setItem(AREA_PREFIX + areaCode, JSON.stringify(r.areaList));
                } else {
                    alert(r.msg);
                }
            }
        });
    }
    if (typeof (callback) == "function") {
        callback(result);
    }
    return result;

}
