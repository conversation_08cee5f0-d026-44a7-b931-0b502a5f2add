package com.ybkj.smm.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 项目信息申报表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-03-03 09:52:32
 */
@TableName("smm_project_project_declare")
@Data
public class ProjectDeclare extends Project implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 采区坐标
     */
    @TableField(exist = false)
    private String coords;

    /**
     * 采区geoJson
     */
    @TableField(exist = false)
    private String geoJson;

    /**
     * 每日授权开单数量
     */
    @TableField(exist = false)
    private Integer dailyBillCount;

    /**
     * 删除标记
     */
    @TableLogic
    private Boolean deleted;


    @TableField(exist = false)
    private String status;


    /**
     * 审核状态;字典值：waiting-待审核、pass-审核通过、reject-审核不通过
     */
    @Size(max = 64, message = "审核状态;字典值：waiting-待审核、pass-审核通过、reject-审核不通过最大允许长度为64")
    private String auditStatus;

    /**
     * 数据版本
     */
    @Version
    private Integer version;


    /**
     * 视频监控是否正常使用
     */
    private Boolean video;

    /**
     * 喷淋设备是否正常使用
     */
    private Boolean spray;

    /**
     * 公示牌是否正常使用
     */
    private Boolean billboard;

    /**
     * 监理是否到位
     */
    private Boolean supervisor;

    /**
     * 其他设备是否正常使用
     */
    private Boolean other;

    /**
     * 视频监控未正常使用原因
     */
    private String videoCause;

    /**
     * 喷淋设备未正常使用原因
     */
    private String sprayCause;

    /**
     * 公示牌未正常使用原因
     */
    private String billboardCause;

    /**
     * 监理未到位原因
     */
    private String supervisorCause;

    /**
     * 其他设备未正常使用原因
     */
    private String otherCause;

    /**
     * 市级审核人
     */
    private String reviewer;
    /**
     * 参数和存储是否满足技术要求
     */
    private Boolean technology;

    /**
     * 参数和存储是否满足技术要求原因
     */
    private String technologyCause;

    /**
     * 是否按照方案要求的点位、数量布设
     */
    private Boolean layout;

    /**
     * 是否按照方案要求的点位、数量布设原因
     */
    private String layoutCause;

    /**
     * 省厅审核时间
     */
    private String departmentAuditTime;

    /**
     * 弃砂方案是否报备，true : false
     */
    @TableField("REPORTED_FLAG")
    private String reportedFlag;

    /**
     * 获取 采区坐标
     *
     * @return coords 采区坐标
     */
    public String getCoords() {
        return this.coords;
    }

    /**
     * 设置 采区坐标
     *
     * @param coords 采区坐标
     */
    public void setCoords(String coords) {
        this.coords = coords;
    }

    /**
     * 获取 采区geoJson
     *
     * @return geoJson 采区geoJson
     */
    public String getGeoJson() {
        return this.geoJson;
    }

    /**
     * 设置 采区geoJson
     *
     * @param geoJson 采区geoJson
     */
    public void setGeoJson(String geoJson) {
        this.geoJson = geoJson;
    }

    /**
     * 获取 删除标记
     *
     * @return deleted 删除标记
     */
    public Boolean getDeleted() {
        return this.deleted;
    }

    /**
     * 设置 删除标记
     *
     * @param deleted 删除标记
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * 获取 审核状态;字典值：waiting-待审核、pass-审核通过、reject-审核不通过
     *
     * @return auditStatus 审核状态;字典值：waiting-待审核、pass-审核通过、reject-审核不通过
     */
    public String getAuditStatus() {
        return this.auditStatus;
    }

    /**
     * 设置 审核状态;字典值：waiting-待审核、pass-审核通过、reject-审核不通过
     *
     * @param auditStatus 审核状态;字典值：waiting-待审核、pass-审核通过、reject-审核不通过
     */
    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    /**
     * 获取 数据版本
     *
     * @return version 数据版本
     */
    public Integer getVersion() {
        return this.version;
    }

    /**
     * 设置 数据版本
     *
     * @param version 数据版本
     */
    public void setVersion(Integer version) {
        this.version = version;
    }

    /**
     * 获取 每日授权开单数量
     *
     * @return dailyBillCount 每日授权开单数量
     */
    public Integer getDailyBillCount() {
        return this.dailyBillCount;
    }

    /**
     * 设置 每日授权开单数量
     *
     * @param dailyBillCount 每日授权开单数量
     */
    public void setDailyBillCount(Integer dailyBillCount) {
        this.dailyBillCount = dailyBillCount;
    }
}
