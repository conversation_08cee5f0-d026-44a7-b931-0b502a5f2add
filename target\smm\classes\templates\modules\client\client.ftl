<#import "/macro/macro.ftl" as my />
<!DOCTYPE html>
<html>
<@my.head jqgrid=true component=true vueValidate=true elementUI=true ztree=true  ueditor=true>
    <title>客户端信息表</title>
</@my.head>
<body>
<div id="app" v-cloak>
    <div v-show="showList">
        <div class="grid-btn form-inline">
            <div class="form-group">
                <a class="btn btn-default" @click="query"><i class="fa fa-search"></i>&nbsp;查询</a>
                <a class="btn btn-default" @click="queryAll"><i class="fa fa-refresh"></i>&nbsp;全部</a>
            </div>
			<#if shiro.hasPermission("client:client:save")>
            <div class="form-group">
            <a class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
            </div>
			</#if>
			<#if shiro.hasPermission("client:client:delete")>
            <div class="form-group">
            <a class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;批量删除</a>
            </div>
			</#if>
        </div>
        <a class="pull-right" @click="drawer=true">更多查询<i class="fa fa-plus"></i></a>
        <div class="clearfix"></div>
        <el-drawer
                :visible.sync="drawer"
                :with-header="false"
                direction="rtl">
            <div class="form-horizontal" style="width:100%;padding: 10px">
                <div class="form-group">
                </div>
                <div class="form-group">
                    <a class="btn btn-default" @click="query('drawer')"><i class="fa fa-search"></i>&nbsp;查询</a>
                    <a class="btn btn-default" @click="clearQ"><i class="fa fa-refresh"></i>&nbsp;清空</a>
                </div>
            </div>
        </el-drawer>
        <my-table url="${request.contextPath}/client/client/list" ref="clientTable" row-key="id">
            <el-table-column  label="id 主键" min-width="80" prop="id" sortable="custom" column-key="ID"></el-table-column>
            <el-table-column  label="创建人id" min-width="80" prop="createUserId" sortable="custom" column-key="CREATE_USER_ID"></el-table-column>
            <el-table-column  label="修改人id" min-width="80" prop="updateUserId" sortable="custom" column-key="UPDATE_USER_ID"></el-table-column>
            <el-table-column  label="创建时间" min-width="80" prop="createTime" sortable="custom" column-key="CREATE_TIME"></el-table-column>
            <el-table-column  label="修改时间" min-width="80" prop="updateTime" sortable="custom" column-key="UPDATE_TIME"></el-table-column>
            <el-table-column  label="项目ID" min-width="80" prop="projectId" sortable="custom" column-key="PROJECT_ID"></el-table-column>
            <el-table-column  label="磅站ID" min-width="80" prop="stationId" sortable="custom" column-key="STATION_ID"></el-table-column>
            <el-table-column  label="客户端编号(sn号=md5(mac地址))" min-width="80" prop="sn" sortable="custom" column-key="SN"></el-table-column>
            <el-table-column  label="客户端密码" min-width="80" prop="pwd" sortable="custom" column-key="PWD"></el-table-column>
            <el-table-column  label="mac地址" min-width="80" prop="mac" sortable="custom" column-key="MAC"></el-table-column>
            <el-table-column  label="ip地址" min-width="80" prop="ip" sortable="custom" column-key="IP"></el-table-column>
            <el-table-column  label="串口号" min-width="80" prop="com" sortable="custom" column-key="COM"></el-table-column>
            <el-table-column  label="波特率" min-width="80" prop="baudRate" sortable="custom" column-key="BAUD_RATE"></el-table-column>
            <el-table-column  label="数据位（5、6、7、8）" min-width="80" prop="dataBit" sortable="custom" column-key="DATA_BIT"></el-table-column>
            <el-table-column  label="校验位（N、E、O、M、S）" min-width="80" prop="verifyBit" sortable="custom" column-key="VERIFY_BIT"></el-table-column>
            <el-table-column  label="停止位（1、1.5、2）" min-width="80" prop="stopBit" sortable="custom" column-key="STOP_BIT"></el-table-column>
            <el-table-column  label="流控" min-width="80" prop="flowControl" sortable="custom" column-key="FLOW_CONTROL"></el-table-column>
            <el-table-column  label="终端类型;KL2008柯力2008" min-width="80" prop="type" sortable="custom" column-key="TYPE"></el-table-column>
            <el-table-column  label="有效期（起）" min-width="80" prop="startTime" sortable="custom" column-key="START_TIME"></el-table-column>
            <el-table-column  label="有效期（止）" min-width="80" prop="endTime" sortable="custom" column-key="END_TIME"></el-table-column>
            <el-table-column  label="状态;use-弃用，stop-停用" min-width="80" prop="status" sortable="custom" column-key="STATUS"></el-table-column>
            <el-table-column label="操作" column-key="caozuo" min-width="80" fixed="right">
                <template slot-scope="scope">
                    <button @click="update(scope.row.id)" class="btn btn-xs btn-success" title="编辑" type="button"><i
                            class="fa fa-edit" aria-hidden="true"></i></button>
                    <button @click="delOne(scope.row.id)" class="btn btn-xs btn-success" title="删除" type="button"><i
                            class="fa fa-trash" aria-hidden="true"></i></button>
                </template>
            </el-table-column>
        </my-table>
    </div>

    <div v-show="!showList" class="panel panel-default">
        <div class="panel-heading">{{title}}</div>
        <form class="form-horizontal" id="frm">
														                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">创建人id</div>
                <div class="col-sm-9">
                    <my-input type="text" id="createUserId" v-model.trim="client.createUserId" placeholder="创建人id"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">修改人id</div>
                <div class="col-sm-9">
                    <my-input type="text" id="updateUserId" v-model.trim="client.updateUserId" placeholder="修改人id"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">创建时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="createTime" v-model.trim="client.createTime" placeholder="创建时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">修改时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="updateTime" v-model.trim="client.updateTime" placeholder="修改时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">项目ID</div>
                <div class="col-sm-9">
                    <my-input type="text" id="projectId" v-model.trim="client.projectId" placeholder="项目ID"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">磅站ID</div>
                <div class="col-sm-9">
                    <my-input type="text" id="stationId" v-model.trim="client.stationId" placeholder="磅站ID"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">客户端编号(sn号=md5(mac地址))</div>
                <div class="col-sm-9">
                    <my-input type="text" id="sn" v-model.trim="client.sn" placeholder="客户端编号(sn号=md5(mac地址))"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">客户端密码</div>
                <div class="col-sm-9">
                    <my-input type="text" id="pwd" v-model.trim="client.pwd" placeholder="客户端密码"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">mac地址</div>
                <div class="col-sm-9">
                    <my-input type="text" id="mac" v-model.trim="client.mac" placeholder="mac地址"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">ip地址</div>
                <div class="col-sm-9">
                    <my-input type="text" id="ip" v-model.trim="client.ip" placeholder="ip地址"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">串口号</div>
                <div class="col-sm-9">
                    <my-input type="text" id="com" v-model.trim="client.com" placeholder="串口号"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">波特率</div>
                <div class="col-sm-9">
                    <my-input type="number" id="baudRate" v-model.trim="client.baudRate" placeholder="波特率"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">数据位（5、6、7、8）</div>
                <div class="col-sm-9">
                    <my-input type="text" id="dataBit" v-model.trim="client.dataBit" placeholder="数据位（5、6、7、8）"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">校验位（N、E、O、M、S）</div>
                <div class="col-sm-9">
                    <my-input type="text" id="verifyBit" v-model.trim="client.verifyBit" placeholder="校验位（N、E、O、M、S）"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">停止位（1、1.5、2）</div>
                <div class="col-sm-9">
                    <my-input type="text" id="stopBit" v-model.trim="client.stopBit" placeholder="停止位（1、1.5、2）"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">流控</div>
                <div class="col-sm-9">
                    <my-input type="text" id="flowControl" v-model.trim="client.flowControl" placeholder="流控"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">终端类型;KL2008柯力2008</div>
                <div class="col-sm-9">
                    <my-input type="text" id="type" v-model.trim="client.type" placeholder="终端类型;KL2008柯力2008"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">有效期（起）</div>
                <div class="col-sm-9">
                    <my-input type="text" id="startTime" v-model.trim="client.startTime" placeholder="有效期（起）"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">有效期（止）</div>
                <div class="col-sm-9">
                    <my-input type="text" id="endTime" v-model.trim="client.endTime" placeholder="有效期（止）"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">状态;use-弃用，stop-停用</div>
                <div class="col-sm-9">
                    <my-input type="text" id="status" v-model.trim="client.status" placeholder="状态;use-弃用，stop-停用"></my-input>
                </div>
            </div>
							
            <div class="form-group center">
                <input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
                &nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
            </div>
        </form>
    </div>
</div>
<script src="${request.contextPath}/statics/js/modules/client/client.js?_${sloth.version()}"></script>
</body>
</html>
