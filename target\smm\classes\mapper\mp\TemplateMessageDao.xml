<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.mp.dao.TemplateMessageDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sloth.modules.mp.entity.TemplateMessage" id="templateMessageMap">
        <result property="id" column="ID"/>
        <result property="userId" column="USER_ID"/>
        <result property="openId" column="OPEN_ID"/>
        <result property="url" column="URL"/>
        <result property="postData" column="POST_DATA"/>
        <result property="msgId" column="MSG_ID"/>
        <result property="errCode" column="ERR_CODE"/>
        <result property="errMsg" column="ERR_MSG"/>
        <result property="feildName" column="FEILD_NAME"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
    </resultMap>


</mapper>