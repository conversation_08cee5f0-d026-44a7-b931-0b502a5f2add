<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.reports.dao.ResponsiblePersionReportDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.reports.entity.ResponsiblePersionReport" id="responsiblePersionReportMap">
        <result property="id" column="ID"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="deptId" column="DEPT_ID"/>
        <result property="deptCode" column="DEPT_CODE"/>
        <result property="areaCode" column="AREA_CODE"/>
        <result property="year" column="YEAR"/>
        <result property="quarter" column="QUARTER"/>
        <result property="status" column="STATUS"/>
        <result property="reportUserId" column="REPORT_USER_ID"/>
        <result property="reportTime" column="REPORT_TIME"/>
        <result property="version" column="VERSION"/>
    </resultMap>


</mapper>