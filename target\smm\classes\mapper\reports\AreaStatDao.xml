<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.reports.dao.AreaStatDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.reports.entity.AreaStat" id="areaStatMap">
        <result property="id" column="ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="deptId" column="DEPT_ID"/>
        <result property="deptCode" column="DEPT_CODE"/>
        <result property="areaCode" column="AREA_CODE"/>
        <result property="yearMonth" column="YEAR_MONTH"/>
        <result property="year" column="YEAR"/>
        <result property="month" column="MONTH"/>
        <result property="sandProjectCount" column="SAND_PROJECT_COUNT"/>
        <result property="abandonProjectCount" column="ABANDON_PROJECT_COUNT"/>
        <result property="totalYield" column="TOTAL_YIELD"/>
        <result property="totalReverLength" column="TOTAL_REVER_LENGTH"/>
        <result property="sandBillCount" column="SAND_BILL_COUNT"/>
        <result property="abandonBillCount" column="ABANDON_BILL_COUNT"/>
        <result property="sandTotalVehicleLoad" column="SAND_TOTAL_VEHICLE_LOAD"/>
        <result property="abandonTotalVehicleLoad" column="ABANDON_TOTAL_VEHICLE_LOAD"/>
    </resultMap>

    <select id="queryProjectAreaStat" resultType="com.ybkj.smm.modules.reports.entity.AreaStat">
        select
        result.* ,
        SUM( CASE WHEN a.TYPE = 'riverSand' THEN 1 ELSE 0 END ) sandProjectCount,
        SUM( CASE WHEN a.TYPE = 'abandonSand' THEN 1 ELSE 0 END ) abandonProjectCount
        from (
        SELECT
        dept.DEPT_ID deptId,
        dept.DEPT_CODE deptCode,
        dept.AREA_CODE areaCode,
        b.PARENT_ID,
        SUM( CASE WHEN b.TYPE = 'riverSand' THEN b.TOTAL_YIELD ELSE 0 END ) sandTotalYield,
        SUM( CASE WHEN b.TYPE = 'abandonSand' THEN b.TOTAL_YIELD ELSE 0 END ) abandonTotalYield,
        SUM( CASE WHEN b.TYPE = 'riverSand' THEN b.INVESTMENT ELSE 0 END ) sandInvestment,
        SUM( CASE WHEN b.TYPE = 'abandonSand' THEN b.INVESTMENT ELSE 0 END ) abandonInvestment,
        SUM( b.REVER_LENGTH ) totalReverLength,
        SUM( CASE WHEN b.TYPE = 'riverSand' THEN b.billCount ELSE 0 END ) sandBillCount,
        SUM( CASE WHEN b.TYPE = 'abandonSand' THEN b.billCount ELSE 0 END ) abandonBillCount,
        SUM( CASE WHEN b.TYPE = 'riverSand' THEN b.totalVehicleLoad ELSE 0 END ) sandTotalVehicleLoad,
        SUM( CASE WHEN b.TYPE = 'abandonSand' THEN b.totalVehicleLoad ELSE 0 END ) abandonTotalVehicleLoad
        FROM (
        SELECT
        pro.DEPT_ID,
        pro.PARENT_ID,
        pro.TYPE,
		SUM(pro.TOTAL_YIELD) TOTAL_YIELD,
        SUM(pro.REVER_LENGTH) REVER_LENGTH,
        SUM(pro.INVESTMENT) INVESTMENT,
        COUNT( bill.ID ) billCount,
        SUM( bill.VEHICLE_LOAD ) totalVehicleLoad
        FROM
        smm_project_project pro
        LEFT JOIN smm_bill_bill bill ON bill.PROJECT_ID = pro.DEPT_ID
        WHERE
        pro.DELETED = 0 and
        bill.CREATE_TIME &gt;= #{startTime}
        AND bill.CREATE_TIME &lt;= #{endTime}
        GROUP BY
        pro.DEPT_ID
        ) b
        LEFT JOIN sys_dept dept ON b.PARENT_ID = dept.DEPT_ID
        where dept.DEPT_ID is not NULL
        GROUP BY
        b.PARENT_ID ) result

        LEFT JOIN(
        SELECT
        pro.PARENT_ID,
        pro.TYPE
        FROM
        smm_project_project pro
        WHERE
        pro.DELETED = 0 and pro.CREATE_TIME &lt;= #{endTime}
        GROUP BY pro.`NAME`
        ) a on a.PARENT_ID = result.PARENT_ID
        GROUP BY result.PARENT_ID
    </select>


</mapper>
