<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.client.dao.CameraDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.client.entity.Camera" id="cameraMap">
        <result property="id" column="ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="clientId" column="CLIENT_ID"/>
        <result property="sn" column="SN"/>
        <result property="ip" column="IP"/>
        <result property="port" column="PORT"/>
        <result property="status" column="STATUS"/>
    </resultMap>
    <select id="selectCameraPage" resultType="com.ybkj.smm.modules.client.entity.Camera">
        select
        camera.ID,
        camera.SN,
        camera.IP,
        camera.PORT,
        camera.STATUS,
        camera.CREATE_TIME,
        camera.UPDATE_TIME,
        camera.CLIENT_ID,
        camera.CREATE_USER_ID,
        camera.UPDATE_USER_ID,
        client.STATION_NAME AS stationName,
        client.PROJECT_NAME AS projectName,
        client.SECTION_NAME AS sectionName
        from
        smm_client_camera camera
        left join smm_client_client client on camera.CLIENT_ID = client.ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

</mapper>
