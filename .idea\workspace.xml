<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c0be4a8a-5a92-41fe-90f1-b220744802d3" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/ybkj/smm/modules/project/entity/ProjectDeclare.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/ybkj/smm/modules/project/entity/ProjectDeclare.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-dev.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="$PROJECT_DIR$/../../fengfeng/WORK/apache-maven-3.8.3" />
        <option name="localRepository" value="E:\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\fengfeng\WORK\apache-maven-3.8.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="vmOptionsForImporter" value="-Xmx768m" />
        <option name="workspaceImportForciblyTurnedOn" value="true" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="30zsRndUOLj1WMeeGEcJQ1yHX0E" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
    <option name="showVisibilityIcons" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.smm [package].executor": "Run",
    "Maven.smm [verify].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.SmmApplication.executor": "Debug",
    "git-widget-placeholder": "six__shiwei",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/111/watercpcs-platform-services",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "项目",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "configurable.group.appearance",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager">
    <configuration name="SmmApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="smm" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ybkj.smm.SmmApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ybkj.smm.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.SmmApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-18abd8497189-intellij.indexing.shared.core-IU-241.14494.240" />
        <option value="bundled-js-predefined-1d06a55b98c1-74d2a5396914-JavaScript-IU-241.14494.240" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="c0be4a8a-5a92-41fe-90f1-b220744802d3" name="更改" comment="" />
      <created>1754641731580</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754641731580</updated>
      <workItem from="1754641732699" duration="5664000" />
      <workItem from="1754699308923" duration="12994000" />
      <workItem from="1754872213306" duration="4674000" />
      <workItem from="1754884908421" duration="136000" />
      <workItem from="1754958634692" duration="11858000" />
      <workItem from="1755045736590" duration="6066000" />
      <workItem from="1755070017935" duration="4461000" />
      <workItem from="1755160511639" duration="3002000" />
      <workItem from="1755217913402" duration="8766000" />
      <workItem from="1755329123366" duration="1345000" />
      <workItem from="1755505296414" duration="752000" />
      <workItem from="1755563366392" duration="6013000" />
      <workItem from="1755649839552" duration="9366000" />
      <workItem from="1755823001761" duration="9479000" />
      <workItem from="1755909256752" duration="21492000" />
      <workItem from="1756081894824" duration="9383000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="COLUMN_ID_WIDTH">
                <map>
                  <entry key="Table.Default.Author.ColumnIdWidth" value="77" />
                  <entry key="Table.Default.Date.ColumnIdWidth" value="95" />
                  <entry key="Table.GitHub.CommitStatus.ColumnIdWidth" value="94" />
                </map>
              </option>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="six_shiwei" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>