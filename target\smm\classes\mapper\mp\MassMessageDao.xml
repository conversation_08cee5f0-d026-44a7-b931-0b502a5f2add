<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.mp.dao.MassMessageDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sloth.modules.mp.entity.MassMessage" id="massMessageMap">
        <result property="id" column="ID"/>
        <result property="toUserCount" column="TO_USER_COUNT"/>
        <result property="msgType" column="MSG_TYPE"/>
        <result property="content" column="CONTENT"/>
        <result property="status" column="STATUS"/>
        <result property="msgId" column="MSG_ID"/>
        <result property="resultJson" column="RESULT_JSON"/>
    </resultMap>


</mapper>