<#import "/macro/macro.ftl" as my />
<!DOCTYPE html>
<html>
<@my.head jqgrid=true component=true vueValidate=true elementUI=true ztree=true  ueditor=true>
    <title>车辆管理</title>
</@my.head>
<body>
<div id="app" v-cloak>
    <div v-show="showList">
        <div class="grid-btn form-inline">
            <div class="form-group">
                <a class="btn btn-default" @click="query"><i class="fa fa-search"></i>&nbsp;查询</a>
                <a class="btn btn-default" @click="queryAll"><i class="fa fa-refresh"></i>&nbsp;全部</a>
            </div>
			<#if shiro.hasPermission("project:car:save")>
            <div class="form-group">
            <a class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
            </div>
			</#if>
			<#if shiro.hasPermission("project:car:delete")>
            <div class="form-group">
            <a class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;批量删除</a>
            </div>
			</#if>
        </div>
        <a class="pull-right" @click="drawer=true">更多查询<i class="fa fa-plus"></i></a>
        <div class="clearfix"></div>
        <el-drawer
                :visible.sync="drawer"
                :with-header="false"
                direction="rtl">
            <div class="form-horizontal" style="width:100%;padding: 10px">
                <div class="form-group">
                </div>
                <div class="form-group">
                    <a class="btn btn-default" @click="query('drawer')"><i class="fa fa-search"></i>&nbsp;查询</a>
                    <a class="btn btn-default" @click="clearQ"><i class="fa fa-refresh"></i>&nbsp;清空</a>
                </div>
            </div>
        </el-drawer>
        <my-table url="${request.contextPath}/project/car/list" ref="carTable" row-key="id">
            <el-table-column  label="id 主键" min-width="80" prop="id" sortable="custom" column-key="ID"></el-table-column>
            <el-table-column  label="创建人id" min-width="80" prop="createUserId" sortable="custom" column-key="CREATE_USER_ID"></el-table-column>
            <el-table-column  label="修改人id" min-width="80" prop="updateUserId" sortable="custom" column-key="UPDATE_USER_ID"></el-table-column>
            <el-table-column  label="创建时间" min-width="80" prop="createTime" sortable="custom" column-key="CREATE_TIME"></el-table-column>
            <el-table-column  label="修改时间" min-width="80" prop="updateTime" sortable="custom" column-key="UPDATE_TIME"></el-table-column>
            <el-table-column  label="所属公司/个人" min-width="80" prop="company" sortable="custom" column-key="COMPANY"></el-table-column>
            <el-table-column  label="车牌号" min-width="80" prop="number" sortable="custom" column-key="NUMBER"></el-table-column>
            <el-table-column  label="品牌型号" min-width="80" prop="brand" sortable="custom" column-key="BRAND"></el-table-column>
            <el-table-column  label="车主姓名" min-width="80" prop="ownerName" sortable="custom" column-key="OWNER_NAME"></el-table-column>
            <el-table-column  label="自重（吨）" min-width="80" prop="kerbWeight" sortable="custom" column-key="KERB_WEIGHT"></el-table-column>
            <el-table-column  label="最大载重（吨）" min-width="80" prop="maxPayload" sortable="custom" column-key="MAX_PAYLOAD"></el-table-column>
            <el-table-column  label="车次？" min-width="80" prop="times" sortable="custom" column-key="TIMES"></el-table-column>
            <el-table-column  label="数据来源、app、pc、小程序" min-width="80" prop="inputType" sortable="custom" column-key="INPUT_TYPE"></el-table-column>
            <el-table-column label="操作" column-key="caozuo" min-width="80" fixed="right">
                <template slot-scope="scope">
                    <button @click="update(scope.row.id)" class="btn btn-xs btn-success" title="编辑" type="button"><i
                            class="fa fa-edit" aria-hidden="true"></i></button>
                    <button @click="delOne(scope.row.id)" class="btn btn-xs btn-success" title="删除" type="button"><i
                            class="fa fa-trash" aria-hidden="true"></i></button>
                </template>
            </el-table-column>
        </my-table>
    </div>

    <div v-show="!showList" class="panel panel-default">
        <div class="panel-heading">{{title}}</div>
        <form class="form-horizontal" id="frm">
														                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">创建人id</div>
                <div class="col-sm-9">
                    <my-input type="text" id="createUserId" v-model.trim="car.createUserId" placeholder="创建人id"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">修改人id</div>
                <div class="col-sm-9">
                    <my-input type="text" id="updateUserId" v-model.trim="car.updateUserId" placeholder="修改人id"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">创建时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="createTime" v-model.trim="car.createTime" placeholder="创建时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">修改时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="updateTime" v-model.trim="car.updateTime" placeholder="修改时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">所属公司/个人</div>
                <div class="col-sm-9">
                    <my-input type="text" id="company" v-model.trim="car.company" placeholder="所属公司/个人"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">车牌号</div>
                <div class="col-sm-9">
                    <my-input type="text" id="number" v-model.trim="car.number" placeholder="车牌号"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">品牌型号</div>
                <div class="col-sm-9">
                    <my-input type="text" id="brand" v-model.trim="car.brand" placeholder="品牌型号"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">车主姓名</div>
                <div class="col-sm-9">
                    <my-input type="text" id="ownerName" v-model.trim="car.ownerName" placeholder="车主姓名"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">自重（吨）</div>
                <div class="col-sm-9">
                    <my-input type="number" id="kerbWeight" v-model.trim="car.kerbWeight" placeholder="自重（吨）"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">最大载重（吨）</div>
                <div class="col-sm-9">
                    <my-input type="number" id="maxPayload" v-model.trim="car.maxPayload" placeholder="最大载重（吨）"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">车次？</div>
                <div class="col-sm-9">
                    <my-input type="number" id="times" v-model.trim="car.times" placeholder="车次？"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">数据来源、app、pc、小程序</div>
                <div class="col-sm-9">
                    <my-input type="text" id="inputType" v-model.trim="car.inputType" placeholder="数据来源、app、pc、小程序"></my-input>
                </div>
            </div>
							
            <div class="form-group center">
                <input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
                &nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
            </div>
        </form>
    </div>
</div>
<script src="${request.contextPath}/statics/js/modules/project/car.js?_${sloth.version()}"></script>
</body>
</html>
