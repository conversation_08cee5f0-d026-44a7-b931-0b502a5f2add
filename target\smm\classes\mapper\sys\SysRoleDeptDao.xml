<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.sys.dao.SysRoleDeptDao">
    <insert id="insertBatch">
		insert into sys_role_dept(id, role_id, DEPT_ID) VALUES
        <foreach collection="entityList" item="item" separator=",">
			(#{item.id},#{item.roleId},#{item.deptId})
		</foreach>
	</insert>

    <select id="queryDeptIdList" resultType="String">
		select dept_id from sys_role_dept where role_id in
		<foreach item="roleId" collection="array" open="(" separator="," close=")">
			#{roleId}
		</foreach>
	</select>

	<delete id="deleteBatch">
		delete from sys_role_dept where role_id in
		<foreach item="roleId" collection="array" open="(" separator="," close=")">
			#{roleId}
		</foreach>
	</delete>

</mapper>