<#import "/macro/macro.ftl" as my />
<!DOCTYPE html>
<html>
<@my.head jqgrid=true component=true vueValidate=true elementUI=true ztree=true  ueditor=true>
    <title>微信扫码跳转小程序页面</title>
</@my.head>
<body>

</body>
<script>
    //H5或微信扫一扫跳转小程序页面
    //http://localhost:8080/page/ma/wxScanQRCode.html?id=01HZM04MK62QNT85YP2GRSTVCG&flag=true
    var params = location.search.substring(1) //id=01HZM04MK62QNT85YP2GRSTVCG&flag=true
    //【必填】appid 小程序的 appid
    //【必填】path 需要跳转的程序的页面
    //【选填】query 携带的参数 需要encodeURIComponent
    //【选填】env_version 要打开的小程序版本,正式版为release，体验版为trial，开发版为develop，默认打开正式版
    location.href = 'weixin://dl/business/?appid=wxb9670fc5d4dc10b8&path=pages/invoices/index&query=' + encodeURIComponent(params) + '&env_version=develop'
</script>
</html>
