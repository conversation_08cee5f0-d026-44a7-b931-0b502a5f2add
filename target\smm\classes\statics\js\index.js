//生成菜单
var menuItem = Vue.extend({
    name: 'menu-item',
    props:{item:{}},
    template:[
        '<li>',
        '	<a v-if="item.type === 0" href="javascript:;">',
        '		<i v-if="item.icon != null" :class="item.icon"></i>',
        '		<span>{{item.name}}</span>',
        '       <span class="pull-right-container">',
        '		    <i class="fa fa-angle-left pull-right"></i>',
        '            <small v-if="item.todoCount!=0" class="label pull-right bg-yellow">NEW</small>',
        '       </span>',
        '	</a>',
        '	<ul v-if="item.type === 0" class="treeview-menu">',
        '		<menu-item :item="item" v-for="item in item.list"></menu-item>',
        '	</ul>',

        '	<a v-if="item.type === 1 && item.parentId === \'00000000000000000000000000000000\'" href="javascript:;" @click="vm.toPage(item.url)" :url="item.url">',
        '		<i v-if="item.icon != null" :class="item.icon"></i>',
        '       <i v-else class="fa fa-circle-o"></i> ',
        '		<span>{{item.name}}</span>',
        '       <span v-if="item.todoCount!=0" class="pull-right-container">',
        '            <small class="label pull-right bg-yellow">{{item.todoCount}}</small>',
        '       </span>',
        '	</a>',

        '	<a v-if="item.type === 1 && item.parentId != \'00000000000000000000000000000000\'" href="javascript:;" @click="vm.toPage(item.url)" :url="item.url">',
        '       <i v-if="item.icon != null" :class="item.icon"></i>',
        '       <i v-else class="fa fa-circle-o"></i> ',
        '		<span>{{item.name}}</span>',
        '       <span v-if="item.todoCount!=0" class="pull-right-container">',
        '            <small class="label pull-right bg-yellow" >{{item.todoCount}}</small>',
        '       </span>',
        '   </a>',
        '</li>'
    ].join(''),
	mounted:function () {
        //刷新页面时定位到hash对应菜单
        var url = window.location.hash.replace("#", '');
        vm.openMenu(url);
    }
});

// iframe自适应
$(window).on('resize', function() {
	var $content = $('.content');
	$content.height($(this).height() - 155);
	$content.find('iframe').each(function() {
		$(this).height($content.height());
	});
}).resize();

//注册菜单组件
Vue.component('menuItem',menuItem);

var vm = new Vue({
	el:'#app',
	data:{
		user:{},
		menuList:{},
		main:"",
		password:'',
		newPassword:'',
		conPassword:'',
        navTitle:""
		,pp:''
		, msgCount: 0
		, showCount: false
        , enableEdit: true
	},
	methods: {
        toPage: function (url) {
            if (vm.main == url) {
            	//点击菜单，刷新iframe
                vm.createIframe(url);
            }else{
                vm.main = url;
                window.location.hash = url;
            }
            this.getMenuTodoCountByUrl(url);
        },
        //根据url获取对应menu对象
        getMenuByUrl: function (url,menuList) {
            for (var i = 0; i < menuList.length; i++) {
                var menu = menuList[i];
                if (menu.type === 1&&menu.url == url) {
                    return menu;
                }else if (menu.type === 0) {
                    var result = vm.getMenuByUrl(url, menu.list);
                    if (result.url ==url) {
                        return result;
                    }
                }
            }
            return {};
        },
        //根据url展开菜单
        openMenu: function (url) {
            var a = $("a[url='" + url + "']");
            if ((a == undefined || a.length==0) && url.indexOf('?') != -1) {
                var preUrl = url.split("?")[0] + "?";
                var params = url.split("?")[1].split("&");
                for(var i=0;i<params.length;i++){
                    preUrl += params[i] + "&";
                    url = preUrl.substring(0, preUrl.length - 1);
                    a = $("a[url='" + url + "']");
                    if (a != undefined && a.length > 0) {
                        break;
                    }
                }
            }
            if ((a == undefined || a.length == 0)) {
                return;
            }
            $(".sidebar-menu li").removeClass("active");
            $(".sidebar-menu ul").removeClass("menu-open");
            $("ul .treeview-menu").css("display", "none");
            $(a).parents("ul .treeview-menu").addClass("menu-open");
            $(a).parents("ul .treeview-menu").css("display","block");
            $(a).parents("li").addClass("active");
            vm.navTitle = $("ul[class='sidebar-menu'] a[url='"+url+"'] span:first").text();
        },
        getMenuList: function (event) {
            $.getJSON("sys/menu/nav?_"+$.now(), function(r){
                if (r.code == 0) {
                    $.each(r.menuList,function () {
                        vm.initMenuTodoCount(this);
                    })
                    vm.menuList = r.menuList;
                    vm.router(true);
                    vm.$nextTick(function () {
                        $.each(r.menuList,function () {
                            vm.getMenuTodoCount(this)
                        })
                    })
                }
            });
        },
        //初始化todoCount
        initMenuTodoCount:function(menu) {
            menu.todoCount = 0;
            if (menu.type === 0) {
                $.each(menu.list,function () {
                    vm.initMenuTodoCount(this)
                })
            }
        },
        //通过url获取待办任务个数
        getMenuTodoCountByUrl:function(url) {
            var menu = vm.getMenuByUrl(url,vm.menuList);
            if (menu.url ==url) {
                vm.getMenuTodoCount(menu);
            }
        },
        //获取待办任务个数
        getMenuTodoCount:function(menu) {
            if (menu.type === 1 &&menu.todoUrl!=null && menu.todoUrl!='') {
                $.ajax({
                    type: "POST",
                    url: menu.todoUrl,
                    contentType: "application/json",
                    async: false,
                    success: function(r){
                        if(r.code == 0){
                            menu.todoCount=r.count;
                        }else{
                           console.log("error:"+r.msg);
                        }
                    }
                });
            }else if (menu.type === 0) {
                menu.todoCount = 0;
                for(var i=0;i<menu.list.length;i++){
                    menu.todoCount += vm.getMenuTodoCount(menu.list[i]);
                }
            }
            return menu.todoCount;
        },
		getUser: function(){
			$.getJSON("sys/user/info?_"+$.now(), function(r){
				vm.user = r.user;
			});
		},
		updatePassword: function(){
			$(".error-label").remove();
			layer.open({
				type: 1,
				title: "修改密码",
				area: ['550px', '400px'],
				shadeClose: false,
				content: jQuery("#passwordLayer"),
				btn: ['修改','取消'],
				btn1: function (index) {
					if(!validateForm("frm")){
						return
					}
					var data = "password="+vm.password+"&newPassword="+vm.newPassword;
					$.ajax({
						type: "POST",
					    url: "sys/user/password",
					    data: data,
					    dataType: "json",
					    success: function(result){
							if(result.code == 0){
								layer.close(index);
								layer.alert('修改成功', function(index){
									location.reload();
								});
							}else{
								layer.alert(result.msg);
							}
						}
					});
	            },
                btn2: function () {
                    vm.password="";
                    vm.newPassword="";
                    vm.conPassword="";
                },
                cancel: function(){
                    vm.password="";
                    vm.newPassword="";
                    vm.conPassword="";
                }
            });
		},
        //路由，监听hashchange
        router:function (e) {
            var hash = window.location.hash.replace("#", '');
            //初始化页面
            if (e && typeof (e) == 'boolean') {
                window.addEventListener("hashchange",vm.router);
                if (hash != '') {
                    vm.main = hash;
                    vm.createIframe(hash);
                    return;
                }
                //默认打开第一个二级菜单
                if (vm.menuList.length == 0) {
                    alert("无可显示菜单");
                }else if(vm.menuList[0].type==1&& vm.menuList[0].url!=''){//是菜单
                    hash = vm.menuList[0].url;
                } else if (vm.menuList[0].type == 0 && vm.menuList[0].list.length != 0) {//是目录
                    hash = vm.menuList[0].list[0].url;
                } else {
                    alert("菜单配置有误！")
                }
                window.location.hash = hash;
            }
            vm.main = hash;
            vm.createIframe(hash);
            vm.openMenu(hash);
        },
        //创建iframe
        createIframe: function (url) {
            var iframe="<iframe id=\"mainIframe\" scrolling=\"yes\" frameborder=\"0\" style=\"width:100%;min-height:200px;overflow:visible;background:#fff;\" src=\""+url+"\"></iframe>"
            $("#mainIframe").remove();
            $("#content").html(iframe);
            $("#content").height($(window).height() - 155);
            $("#mainIframe").height($("#content").height())
        }
	},
	created: function(){
		this.getMenuList();
		this.getUser();
	}
});

$(function () {
	validateInputs("password,newPassword,conPassword");

})
