!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).CodeMirror=t()}(this,function(){"use strict";var e=navigator.userAgent,t=navigator.platform,g=/gecko\/\d/i.test(e),n=/MSIE \d/.test(e),r=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),i=/Edge\/(\d+)/.exec(e),x=n||r||i,C=x&&(n?document.documentMode||6:+(i||r)[1]),b=!i&&/WebKit\//.test(e),o=b&&/Qt\/\d+\.\d+/.test(e),l=!i&&/Chrome\//.test(e),m=/Opera\//.test(e),c=/Apple Computer/.test(navigator.vendor),s=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),v=/PhantomJS/.test(e),a=!i&&/AppleWebKit/.test(e)&&/Mobile\/\w+/.test(e),u=/Android/.test(e),h=a||u||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),w=a||/Mac/.test(t),d=/\bCrOS\b/.test(e),f=/win/i.test(t),p=m&&e.match(/Version\/(\d*\.\d*)/);(p=p&&Number(p[1]))&&15<=p&&(b=!(m=!1));var y=w&&(o||m&&(null==p||p<12.11)),S=g||x&&9<=C;function L(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var k,T=function(e,t){var n,r=e.className,i=L(t).exec(r);i&&(n=r.slice(i.index+i[0].length),e.className=r.slice(0,i.index)+(n?i[1]+n:""))};function M(e){for(var t=e.childNodes.length;0<t;--t)e.removeChild(e.firstChild);return e}function N(e,t){return M(e).appendChild(t)}function O(e,t,n,r){var i=document.createElement(e);if(n&&(i.className=n),r&&(i.style.cssText=r),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function A(e,t,n,r){var i=O(e,t,n,r);return i.setAttribute("role","presentation"),i}function D(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function W(){var t;try{t=document.activeElement}catch(e){t=document.body||null}for(;t&&t.shadowRoot&&t.shadowRoot.activeElement;)t=t.shadowRoot.activeElement;return t}function H(e,t){var n=e.className;L(t).test(n)||(e.className+=(n?" ":"")+t)}function F(e,t){for(var n=e.split(" "),r=0;r<n.length;r++)n[r]&&!L(n[r]).test(t)&&(t+=" "+n[r]);return t}k=document.createRange?function(e,t,n,r){var i=document.createRange();return i.setEnd(r||e,n),i.setStart(e,t),i}:function(e,t,n){var r=document.body.createTextRange();try{r.moveToElementText(e.parentNode)}catch(e){return r}return r.collapse(!0),r.moveEnd("character",n),r.moveStart("character",t),r};var P=function(e){e.select()};function E(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function I(e,t,n){for(var r in t=t||{},e)!e.hasOwnProperty(r)||!1===n&&t.hasOwnProperty(r)||(t[r]=e[r]);return t}function R(e,t,n,r,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=r||0,l=i||0;;){var s=e.indexOf("\t",o);if(s<0||t<=s)return l+(t-o);l+=s-o,l+=n-l%n,o=s+1}}a?P=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:x&&(P=function(e){try{e.select()}catch(e){}});var z=function(){this.id=null,this.f=null,this.time=0,this.handler=E(this.onTimeout,this)};function B(e,t){for(var n=0;n<e.length;++n)if(e[n]==t)return n;return-1}z.prototype.onTimeout=function(e){e.id=0,e.time<=+new Date?e.f():setTimeout(e.handler,e.time-new Date)},z.prototype.set=function(e,t){this.f=t;var n=+new Date+e;(!this.id||n<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,e),this.time=n)};var G=50,U={toString:function(){return"CodeMirror.Pass"}},V={scroll:!1},K={origin:"*mouse"},j={origin:"+move"};function X(e,t,n){for(var r=0,i=0;;){var o=e.indexOf("\t",r);-1==o&&(o=e.length);var l=o-r;if(o==e.length||t<=i+l)return r+Math.min(l,t-i);if(i+=o-r,r=o+1,t<=(i+=n-i%n))return r}}var Y=[""];function _(e){for(;Y.length<=e;)Y.push($(Y)+" ");return Y[e]}function $(e){return e[e.length-1]}function q(e,t){for(var n=[],r=0;r<e.length;r++)n[r]=t(e[r],r);return n}function Z(){}function Q(e,t){var n=Object.create?Object.create(e):(Z.prototype=e,new Z);return t&&I(t,n),n}var J=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function ee(e){return/\w/.test(e)||""<e&&(e.toUpperCase()!=e.toLowerCase()||J.test(e))}function te(e,t){return t?!!(-1<t.source.indexOf("\\w")&&ee(e))||t.test(e):ee(e)}function ne(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return;return 1}var re=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function ie(e){return 768<=e.charCodeAt(0)&&re.test(e)}function oe(e,t,n){for(;(n<0?0<t:t<e.length)&&ie(e.charAt(t));)t+=n;return t}function le(e,t,n){for(var r=n<t?-1:1;;){if(t==n)return t;var i=(t+n)/2,o=r<0?Math.ceil(i):Math.floor(i);if(o==t)return e(o)?t:n;e(o)?n=o:t=o+r}}var se=null;function ae(e,t,n){var r;se=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==n?r=i:se=i),o.from==t&&(o.from!=o.to&&"before"!=n?r=i:se=i)}return null!=r?r:se}var ue,ce,he,de,fe,pe,ge,me=(ue="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",ce="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111",he=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,de=/[stwN]/,fe=/[LRr]/,pe=/[Lb1n]/,ge=/[1n]/,function(e,t){var n="ltr"==t?"L":"R";if(0==e.length||"ltr"==t&&!he.test(e))return!1;for(var r,i=e.length,o=[],l=0;l<i;++l)o.push((r=e.charCodeAt(l))<=247?ue.charAt(r):1424<=r&&r<=1524?"R":1536<=r&&r<=1785?ce.charAt(r-1536):1774<=r&&r<=2220?"r":8192<=r&&r<=8203?"w":8204==r?"b":"L");for(var s=0,a=n;s<i;++s){var u=o[s];"m"==u?o[s]=a:a=u}for(var c=0,h=n;c<i;++c){var d=o[c];"1"==d&&"r"==h?o[c]="n":fe.test(d)&&"r"==(h=d)&&(o[c]="R")}for(var f=1,p=o[0];f<i-1;++f){var g=o[f];"+"==g&&"1"==p&&"1"==o[f+1]?o[f]="1":","!=g||p!=o[f+1]||"1"!=p&&"n"!=p||(o[f]=p),p=g}for(var m=0;m<i;++m){var v=o[m];if(","==v)o[m]="N";else if("%"==v){for(var y=void 0,y=m+1;y<i&&"%"==o[y];++y);for(var b=m&&"!"==o[m-1]||y<i&&"1"==o[y]?"1":"N",w=m;w<y;++w)o[w]=b;m=y-1}}for(var x=0,C=n;x<i;++x){var S=o[x];"L"==C&&"1"==S?o[x]="L":fe.test(S)&&(C=S)}for(var L=0;L<i;++L)if(de.test(o[L])){for(var k=void 0,k=L+1;k<i&&de.test(o[k]);++k);for(var T="L"==(L?o[L-1]:n),M=T==("L"==(k<i?o[k]:n))?T?"L":"R":n,N=L;N<k;++N)o[N]=M;L=k-1}for(var A,O=[],D=0;D<i;)if(pe.test(o[D])){var W=D;for(++D;D<i&&pe.test(o[D]);++D);O.push(new ve(0,W,D))}else{var H=D,F=O.length,P="rtl"==t?1:0;for(++D;D<i&&"L"!=o[D];++D);for(var E=H;E<D;)if(ge.test(o[E])){H<E&&(O.splice(F,0,new ve(1,H,E)),F+=P);var I=E;for(++E;E<D&&ge.test(o[E]);++E);O.splice(F,0,new ve(2,I,E)),F+=P,H=E}else++E;H<D&&O.splice(F,0,new ve(1,H,D))}return"ltr"==t&&(1==O[0].level&&(A=e.match(/^\s+/))&&(O[0].from=A[0].length,O.unshift(new ve(0,0,A[0].length))),1==$(O).level&&(A=e.match(/\s+$/))&&($(O).to-=A[0].length,O.push(new ve(0,i-A[0].length,i)))),"rtl"==t?O.reverse():O});function ve(e,t,n){this.level=e,this.from=t,this.to=n}function ye(e,t){var n=e.order;return null==n&&(n=e.order=me(e.text,t)),n}var be=[],we=function(e,t,n){var r;e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent?e.attachEvent("on"+t,n):(r=e._handlers||(e._handlers={}))[t]=(r[t]||be).concat(n)};function xe(e,t){return e._handlers&&e._handlers[t]||be}function Ce(e,t,n){var r,i,o;e.removeEventListener?e.removeEventListener(t,n,!1):e.detachEvent?e.detachEvent("on"+t,n):!(i=(r=e._handlers)&&r[t])||-1<(o=B(i,n))&&(r[t]=i.slice(0,o).concat(i.slice(o+1)))}function Se(e,t){var n=xe(e,t);if(n.length)for(var r=Array.prototype.slice.call(arguments,2),i=0;i<n.length;++i)n[i].apply(null,r)}function Le(e,t,n){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),Se(e,n||t.type,e,t),Oe(t)||t.codemirrorIgnore}function ke(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var n=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),r=0;r<t.length;++r)-1==B(n,t[r])&&n.push(t[r])}function Te(e,t){return 0<xe(e,t).length}function Me(e){e.prototype.on=function(e,t){we(this,e,t)},e.prototype.off=function(e,t){Ce(this,e,t)}}function Ne(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function Ae(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function Oe(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function De(e){Ne(e),Ae(e)}function We(e){return e.target||e.srcElement}function He(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),w&&e.ctrlKey&&1==t&&(t=3),t}var Fe,Pe,Ee=function(){if(x&&C<9)return!1;var e=O("div");return"draggable"in e||"dragDrop"in e}();function Ie(e){var t;null==Fe&&(t=O("span","​"),N(e,O("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(Fe=t.offsetWidth<=1&&2<t.offsetHeight&&!(x&&C<8)));var n=Fe?O("span","​"):O("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return n.setAttribute("cm-text",""),n}function Re(e){if(null!=Pe)return Pe;var t=N(e,document.createTextNode("AخA")),n=k(t,0,1).getBoundingClientRect(),r=k(t,1,2).getBoundingClientRect();return M(e),n&&n.left!=n.right&&(Pe=r.right-n.right<3)}var ze,Be=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,n=[],r=e.length;t<=r;){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),l=o.indexOf("\r");-1!=l?(n.push(o.slice(0,l)),t+=l+1):(n.push(o),t=i+1)}return n}:function(e){return e.split(/\r\n?|\n/)},Ge=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(e){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(e){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},Ue="oncopy"in(ze=O("div"))||(ze.setAttribute("oncopy","return;"),"function"==typeof ze.oncopy),Ve=null;var Ke={},je={};function Xe(e){if("string"==typeof e&&je.hasOwnProperty(e))e=je[e];else if(e&&"string"==typeof e.name&&je.hasOwnProperty(e.name)){var t=je[e.name];"string"==typeof t&&(t={name:t}),(e=Q(t,e)).name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return Xe("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return Xe("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function Ye(e,t){t=Xe(t);var n=Ke[t.name];if(!n)return Ye(e,"text/plain");var r=n(e,t);if(_e.hasOwnProperty(t.name)){var i=_e[t.name];for(var o in i)i.hasOwnProperty(o)&&(r.hasOwnProperty(o)&&(r["_"+o]=r[o]),r[o]=i[o])}if(r.name=t.name,t.helperType&&(r.helperType=t.helperType),t.modeProps)for(var l in t.modeProps)r[l]=t.modeProps[l];return r}var _e={};function $e(e,t){I(t,_e.hasOwnProperty(e)?_e[e]:_e[e]={})}function qe(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var n={};for(var r in t){var i=t[r];i instanceof Array&&(i=i.concat([])),n[r]=i}return n}function Ze(e,t){for(var n;e.innerMode&&(n=e.innerMode(t))&&n.mode!=e;)t=n.state,e=n.mode;return n||{mode:e,state:t}}function Qe(e,t,n){return!e.startState||e.startState(t,n)}var Je=function(e,t,n){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=n};function et(e,t){if((t-=e.first)<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var n=e;!n.lines;)for(var r=0;;++r){var i=n.children[r],o=i.chunkSize();if(t<o){n=i;break}t-=o}return n.lines[t]}function tt(e,n,r){var i=[],o=n.line;return e.iter(n.line,r.line+1,function(e){var t=e.text;o==r.line&&(t=t.slice(0,r.ch)),o==n.line&&(t=t.slice(n.ch)),i.push(t),++o}),i}function nt(e,t,n){var r=[];return e.iter(t,n,function(e){r.push(e.text)}),r}function rt(e,t){var n=t-e.height;if(n)for(var r=e;r;r=r.parent)r.height+=n}function it(e){if(null==e.parent)return null;for(var t=e.parent,n=B(t.lines,e),r=t.parent;r;r=(t=r).parent)for(var i=0;r.children[i]!=t;++i)n+=r.children[i].chunkSize();return n+t.first}function ot(e,t){var n=e.first;e:do{for(var r=0;r<e.children.length;++r){var i=e.children[r],o=i.height;if(t<o){e=i;continue e}t-=o,n+=i.chunkSize()}return n}while(!e.lines);for(var l=0;l<e.lines.length;++l){var s=e.lines[l].height;if(t<s)break;t-=s}return n+l}function lt(e,t){return t>=e.first&&t<e.first+e.size}function st(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function at(e,t,n){if(void 0===n&&(n=null),!(this instanceof at))return new at(e,t,n);this.line=e,this.ch=t,this.sticky=n}function ut(e,t){return e.line-t.line||e.ch-t.ch}function ct(e,t){return e.sticky==t.sticky&&0==ut(e,t)}function ht(e){return at(e.line,e.ch)}function dt(e,t){return ut(e,t)<0?t:e}function ft(e,t){return ut(e,t)<0?e:t}function pt(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function gt(e,t){if(t.line<e.first)return at(e.first,0);var n,r,i,o=e.first+e.size-1;return t.line>o?at(o,et(e,o).text.length):(r=et(e,(n=t).line).text.length,null==(i=n.ch)||r<i?at(n.line,r):i<0?at(n.line,0):n)}function mt(e,t){for(var n=[],r=0;r<t.length;r++)n[r]=gt(e,t[r]);return n}Je.prototype.eol=function(){return this.pos>=this.string.length},Je.prototype.sol=function(){return this.pos==this.lineStart},Je.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},Je.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},Je.prototype.eat=function(e){var t=this.string.charAt(this.pos),n="string"==typeof e?t==e:t&&(e.test?e.test(t):e(t));if(n)return++this.pos,t},Je.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},Je.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},Je.prototype.skipToEnd=function(){this.pos=this.string.length},Je.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(-1<t)return this.pos=t,!0},Je.prototype.backUp=function(e){this.pos-=e},Je.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=R(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?R(this.string,this.lineStart,this.tabSize):0)},Je.prototype.indentation=function(){return R(this.string,null,this.tabSize)-(this.lineStart?R(this.string,this.lineStart,this.tabSize):0)},Je.prototype.match=function(e,t,n){if("string"!=typeof e){var r=this.string.slice(this.pos).match(e);return r&&0<r.index?null:(r&&!1!==t&&(this.pos+=r[0].length),r)}function i(e){return n?e.toLowerCase():e}if(i(this.string.substr(this.pos,e.length))==i(e))return!1!==t&&(this.pos+=e.length),!0},Je.prototype.current=function(){return this.string.slice(this.start,this.pos)},Je.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},Je.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},Je.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var vt=function(e,t){this.state=e,this.lookAhead=t},yt=function(e,t,n,r){this.state=t,this.doc=e,this.line=n,this.maxLookAhead=r||0,this.baseTokens=null,this.baseTokenPos=1};function bt(t,n,r,e){var a=[t.state.modeGen],i={};Nt(t,n.text,t.doc.mode,r,function(e,t){return a.push(e,t)},i,e);for(var u=r.state,o=function(e){r.baseTokens=a;var o=t.state.overlays[e],l=1,s=0;r.state=!0,Nt(t,n.text,o.mode,r,function(e,t){for(var n=l;s<e;){var r=a[l];e<r&&a.splice(l,1,e,a[l+1],r),l+=2,s=Math.min(e,r)}if(t)if(o.opaque)a.splice(n,l-n,e,"overlay "+t),l=n+2;else for(;n<l;n+=2){var i=a[n+1];a[n+1]=(i?i+" ":"")+"overlay "+t}},i),r.state=u,r.baseTokens=null,r.baseTokenPos=1},l=0;l<t.state.overlays.length;++l)o(l);return{styles:a,classes:i.bgClass||i.textClass?i:null}}function wt(e,t,n){var r,i,o;return t.styles&&t.styles[0]==e.state.modeGen||(r=xt(e,it(t)),i=t.text.length>e.options.maxHighlightLength&&qe(e.doc.mode,r.state),o=bt(e,t,r),i&&(r.state=i),t.stateAfter=r.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),n===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))),t.styles}function xt(n,r,e){var t=n.doc,i=n.display;if(!t.mode.startState)return new yt(t,!0,r);var o=function(e,t,n){for(var r,i,o=e.doc,l=n?-1:t-(e.doc.mode.innerMode?1e3:100),s=t;l<s;--s){if(s<=o.first)return o.first;var a=et(o,s-1),u=a.stateAfter;if(u&&(!n||s+(u instanceof vt?u.lookAhead:0)<=o.modeFrontier))return s;var c=R(a.text,null,e.options.tabSize);(null==i||c<r)&&(i=s-1,r=c)}return i}(n,r,e),l=o>t.first&&et(t,o-1).stateAfter,s=l?yt.fromSaved(t,l,o):new yt(t,Qe(t.mode),o);return t.iter(o,r,function(e){Ct(n,e.text,s);var t=s.line;e.stateAfter=t==r-1||t%5==0||t>=i.viewFrom&&t<i.viewTo?s.save():null,s.nextLine()}),e&&(t.modeFrontier=s.line),s}function Ct(e,t,n,r){var i=e.doc.mode,o=new Je(t,e.options.tabSize,n);for(o.start=o.pos=r||0,""==t&&St(i,n.state);!o.eol();)Lt(i,o,n.state),o.start=o.pos}function St(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var n=Ze(e,t);return n.mode.blankLine?n.mode.blankLine(n.state):void 0}}function Lt(e,t,n,r){for(var i=0;i<10;i++){r&&(r[0]=Ze(e,n).mode);var o=e.token(t,n);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}yt.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},yt.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},yt.prototype.nextLine=function(){this.line++,0<this.maxLookAhead&&this.maxLookAhead--},yt.fromSaved=function(e,t,n){return t instanceof vt?new yt(e,qe(e.mode,t.state),n,t.lookAhead):new yt(e,qe(e.mode,t),n)},yt.prototype.save=function(e){var t=!1!==e?qe(this.doc.mode,this.state):this.state;return 0<this.maxLookAhead?new vt(t,this.maxLookAhead):t};var kt=function(e,t,n){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=n};function Tt(e,t,n,r){var i,o,l=e.doc,s=l.mode,a=et(l,(t=gt(l,t)).line),u=xt(e,t.line,n),c=new Je(a.text,e.options.tabSize,u);for(r&&(o=[]);(r||c.pos<t.ch)&&!c.eol();)c.start=c.pos,i=Lt(s,c,u.state),r&&o.push(new kt(c,i,qe(l.mode,u.state)));return r?o:new kt(c,i,u.state)}function Mt(e,t){if(e)for(;;){var n=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!n)break;e=e.slice(0,n.index)+e.slice(n.index+n[0].length);var r=n[1]?"bgClass":"textClass";null==t[r]?t[r]=n[2]:new RegExp("(?:^|\\s)"+n[2]+"(?:$|\\s)").test(t[r])||(t[r]+=" "+n[2])}return e}function Nt(e,t,n,r,i,o,l){var s=n.flattenSpans;null==s&&(s=e.options.flattenSpans);var a=0,u=null,c=new Je(t,e.options.tabSize,r),h=e.options.addModeClass&&[null];for(""==t&&Mt(St(n,r.state),o);!c.eol();){var d,f=c.pos>e.options.maxHighlightLength?(s=!1,l&&Ct(e,t,r,c.pos),c.pos=t.length,null):Mt(Lt(n,c,r.state,h),o);if(!h||(d=h[0].name)&&(f="m-"+(f?d+" "+f:d)),!s||u!=f){for(;a<c.start;)i(a=Math.min(c.start,a+5e3),u);u=f}c.start=c.pos}for(;a<c.pos;){var p=Math.min(c.pos,a+5e3);i(p,u),a=p}}var At=!1,Ot=!1;function Dt(e,t,n){this.marker=e,this.from=t,this.to=n}function Wt(e,t){if(e)for(var n=0;n<e.length;++n){var r=e[n];if(r.marker==t)return r}}function Ht(e,t){for(var n,r=0;r<e.length;++r)e[r]!=t&&(n=n||[]).push(e[r]);return n}function Ft(e,t){if(t.full)return null;var n=lt(e,t.from.line)&&et(e,t.from.line).markedSpans,r=lt(e,t.to.line)&&et(e,t.to.line).markedSpans;if(!n&&!r)return null;var i=t.from.ch,o=t.to.ch,l=0==ut(t.from,t.to),s=function(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o,l=e[i],s=l.marker;!(null==l.from||(s.inclusiveLeft?l.from<=t:l.from<t))&&(l.from!=t||"bookmark"!=s.type||n&&l.marker.insertLeft)||(o=null==l.to||(s.inclusiveRight?l.to>=t:l.to>t),(r=r||[]).push(new Dt(s,l.from,o?null:l.to)))}return r}(n,i,l),a=function(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o,l=e[i],s=l.marker;!(null==l.to||(s.inclusiveRight?l.to>=t:l.to>t))&&(l.from!=t||"bookmark"!=s.type||n&&!l.marker.insertLeft)||(o=null==l.from||(s.inclusiveLeft?l.from<=t:l.from<t),(r=r||[]).push(new Dt(s,o?null:l.from-t,null==l.to?null:l.to-t)))}return r}(r,o,l),u=1==t.text.length,c=$(t.text).length+(u?i:0);if(s)for(var h=0;h<s.length;++h){var d,f=s[h];null==f.to&&((d=Wt(a,f.marker))?u&&(f.to=null==d.to?null:d.to+c):f.to=i)}if(a)for(var p=0;p<a.length;++p){var g=a[p];null!=g.to&&(g.to+=c),null==g.from?Wt(s,g.marker)||(g.from=c,u&&(s=s||[]).push(g)):(g.from+=c,u&&(s=s||[]).push(g))}s=s&&Pt(s),a&&a!=s&&(a=Pt(a));var m=[s];if(!u){var v,y=t.text.length-2;if(0<y&&s)for(var b=0;b<s.length;++b)null==s[b].to&&(v=v||[]).push(new Dt(s[b].marker,null,null));for(var w=0;w<y;++w)m.push(v);m.push(a)}return m}function Pt(e){for(var t=0;t<e.length;++t){var n=e[t];null!=n.from&&n.from==n.to&&!1!==n.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function Et(e){var t=e.markedSpans;if(t){for(var n=0;n<t.length;++n)t[n].marker.detachLine(e);e.markedSpans=null}}function It(e,t){if(t){for(var n=0;n<t.length;++n)t[n].marker.attachLine(e);e.markedSpans=t}}function Rt(e){return e.inclusiveLeft?-1:0}function zt(e){return e.inclusiveRight?1:0}function Bt(e,t){var n=e.lines.length-t.lines.length;if(0!=n)return n;var r=e.find(),i=t.find(),o=ut(r.from,i.from)||Rt(e)-Rt(t);if(o)return-o;var l=ut(r.to,i.to)||zt(e)-zt(t);return l||t.id-e.id}function Gt(e,t){var n,r=Ot&&e.markedSpans;if(r)for(var i=void 0,o=0;o<r.length;++o)(i=r[o]).marker.collapsed&&null==(t?i.from:i.to)&&(!n||Bt(n,i.marker)<0)&&(n=i.marker);return n}function Ut(e){return Gt(e,!0)}function Vt(e){return Gt(e,!1)}function Kt(e,t){var n,r=Ot&&e.markedSpans;if(r)for(var i=0;i<r.length;++i){var o=r[i];o.marker.collapsed&&(null==o.from||o.from<t)&&(null==o.to||o.to>t)&&(!n||Bt(n,o.marker)<0)&&(n=o.marker)}return n}function jt(e,t,n,r,i){var o=et(e,t),l=Ot&&o.markedSpans;if(l)for(var s=0;s<l.length;++s){var a=l[s];if(a.marker.collapsed){var u=a.marker.find(0),c=ut(u.from,n)||Rt(a.marker)-Rt(i),h=ut(u.to,r)||zt(a.marker)-zt(i);if(!(0<=c&&h<=0||c<=0&&0<=h)&&(c<=0&&(a.marker.inclusiveRight&&i.inclusiveLeft?0<=ut(u.to,n):0<ut(u.to,n))||0<=c&&(a.marker.inclusiveRight&&i.inclusiveLeft?ut(u.from,r)<=0:ut(u.from,r)<0)))return 1}}}function Xt(e){for(var t;t=Ut(e);)e=t.find(-1,!0).line;return e}function Yt(e,t){var n=et(e,t),r=Xt(n);return n==r?t:it(r)}function _t(e,t){if(t>e.lastLine())return t;var n,r=et(e,t);if(!$t(e,r))return t;for(;n=Vt(r);)r=n.find(1,!0).line;return it(r)+1}function $t(e,t){var n=Ot&&t.markedSpans;if(n)for(var r=void 0,i=0;i<n.length;++i)if((r=n[i]).marker.collapsed){if(null==r.from)return!0;if(!r.marker.widgetNode&&0==r.from&&r.marker.inclusiveLeft&&qt(e,t,r))return!0}}function qt(e,t,n){if(null==n.to){var r=n.marker.find(1,!0);return qt(e,r.line,Wt(r.line.markedSpans,n.marker))}if(n.marker.inclusiveRight&&n.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if((i=t.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==n.to&&(null==i.to||i.to!=n.from)&&(i.marker.inclusiveLeft||n.marker.inclusiveRight)&&qt(e,t,i))return!0}function Zt(e){for(var t=0,n=(e=Xt(e)).parent,r=0;r<n.lines.length;++r){var i=n.lines[r];if(i==e)break;t+=i.height}for(var o=n.parent;o;o=(n=o).parent)for(var l=0;l<o.children.length;++l){var s=o.children[l];if(s==n)break;t+=s.height}return t}function Qt(e){if(0==e.height)return 0;for(var t,n=e.text.length,r=e;t=Ut(r);){var i=t.find(0,!0),r=i.from.line;n+=i.from.ch-i.to.ch}for(r=e;t=Vt(r);){var o=t.find(0,!0);n-=r.text.length-o.from.ch,n+=(r=o.to.line).text.length-o.to.ch}return n}function Jt(e){var n=e.display,t=e.doc;n.maxLine=et(t,t.first),n.maxLineLength=Qt(n.maxLine),n.maxLineChanged=!0,t.iter(function(e){var t=Qt(e);t>n.maxLineLength&&(n.maxLineLength=t,n.maxLine=e)})}var en=function(e,t,n){this.text=e,It(this,t),this.height=n?n(this):1};en.prototype.lineNo=function(){return it(this)},Me(en);var tn={},nn={};function rn(e,t){if(!e||/^\s*$/.test(e))return null;var n=t.addModeClass?nn:tn;return n[e]||(n[e]=e.replace(/\S+/g,"cm-$&"))}function on(e,t){var n=A("span",null,null,b?"padding-right: .1px":null),r={pre:A("pre",[n],"CodeMirror-line"),content:n,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var i,o=0;o<=(t.rest?t.rest.length:0);o++){var l=o?t.rest[o-1]:t.line,s=void 0;r.pos=0,r.addToken=sn,Re(e.display.measure)&&(s=ye(l,e.doc.direction))&&(r.addToken=an(r.addToken,s)),r.map=[],cn(l,r,wt(e,l,t!=e.display.externalMeasured&&it(l))),l.styleClasses&&(l.styleClasses.bgClass&&(r.bgClass=F(l.styleClasses.bgClass,r.bgClass||"")),l.styleClasses.textClass&&(r.textClass=F(l.styleClasses.textClass,r.textClass||""))),0==r.map.length&&r.map.push(0,0,r.content.appendChild(Ie(e.display.measure))),0==o?(t.measure.map=r.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(r.map),(t.measure.caches||(t.measure.caches=[])).push({}))}return b&&(i=r.content.lastChild,(/\bcm-tab\b/.test(i.className)||i.querySelector&&i.querySelector(".cm-tab"))&&(r.content.className="cm-tab-wrap-hack")),Se(e,"renderLine",e,t.line,r.pre),r.pre.className&&(r.textClass=F(r.pre.className,r.textClass||"")),r}function ln(e){var t=O("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function sn(e,t,n,r,i,o,l){if(t){var s,a=e.splitSpaces?function(e,t){if(1<e.length&&!/  /.test(e))return e;for(var n=t,r="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!n||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),r+=o,n=" "==o}return r}(t,e.trailingSpace):t,u=e.cm.state.specialChars,c=!1;if(u.test(t)){s=document.createDocumentFragment();for(var h=0;;){u.lastIndex=h;var d,f=u.exec(t),p=f?f.index-h:t.length-h;if(p&&(d=document.createTextNode(a.slice(h,h+p)),x&&C<9?s.appendChild(O("span",[d])):s.appendChild(d),e.map.push(e.pos,e.pos+p,d),e.col+=p,e.pos+=p),!f)break;h+=1+p;var g,m,v=void 0;"\t"==f[0]?(m=(g=e.cm.options.tabSize)-e.col%g,(v=s.appendChild(O("span",_(m),"cm-tab"))).setAttribute("role","presentation"),v.setAttribute("cm-text","\t"),e.col+=m):("\r"==f[0]||"\n"==f[0]?(v=s.appendChild(O("span","\r"==f[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",f[0]):((v=e.cm.options.specialCharPlaceholder(f[0])).setAttribute("cm-text",f[0]),x&&C<9?s.appendChild(O("span",[v])):s.appendChild(v)),e.col+=1),e.map.push(e.pos,e.pos+1,v),e.pos++}}else e.col+=t.length,s=document.createTextNode(a),e.map.push(e.pos,e.pos+t.length,s),x&&C<9&&(c=!0),e.pos+=t.length;if(e.trailingSpace=32==a.charCodeAt(t.length-1),n||r||i||c||o){var y=n||"";r&&(y+=r),i&&(y+=i);var b=O("span",[s],y,o);if(l)for(var w in l)l.hasOwnProperty(w)&&"style"!=w&&"class"!=w&&b.setAttribute(w,l[w]);return e.content.appendChild(b)}e.content.appendChild(s)}}function an(h,d){return function(e,t,n,r,i,o,l){n=n?n+" cm-force-border":"cm-force-border";for(var s=e.pos,a=s+t.length;;){for(var u=void 0,c=0;c<d.length&&!((u=d[c]).to>s&&u.from<=s);c++);if(u.to>=a)return h(e,t,n,r,i,o,l);h(e,t.slice(0,u.to-s),n,r,null,o,l),r=null,t=t.slice(u.to-s),s=u.to}}}function un(e,t,n,r){var i=!r&&n.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!r&&e.cm.display.input.needsContentAttribute&&(i=i||e.content.appendChild(document.createElement("span"))).setAttribute("cm-marker",n.id),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function cn(e,t,n){var r=e.markedSpans,i=e.text,o=0;if(r)for(var l,s,a,u,c,h,d,f=i.length,p=0,g=1,m="",v=0;;){if(v==p){a=u=c=s="",h=d=null,v=1/0;for(var y=[],b=void 0,w=0;w<r.length;++w){var x=r[w],C=x.marker;if("bookmark"==C.type&&x.from==p&&C.widgetNode)y.push(C);else if(x.from<=p&&(null==x.to||x.to>p||C.collapsed&&x.to==p&&x.from==p)){if(null!=x.to&&x.to!=p&&v>x.to&&(v=x.to,u=""),C.className&&(a+=" "+C.className),C.css&&(s=(s?s+";":"")+C.css),C.startStyle&&x.from==p&&(c+=" "+C.startStyle),C.endStyle&&x.to==v&&(b=b||[]).push(C.endStyle,x.to),C.title&&((d=d||{}).title=C.title),C.attributes)for(var S in C.attributes)(d=d||{})[S]=C.attributes[S];C.collapsed&&(!h||Bt(h.marker,C)<0)&&(h=x)}else x.from>p&&v>x.from&&(v=x.from)}if(b)for(var L=0;L<b.length;L+=2)b[L+1]==v&&(u+=" "+b[L]);if(!h||h.from==p)for(var k=0;k<y.length;++k)un(t,0,y[k]);if(h&&(h.from||0)==p){if(un(t,(null==h.to?f+1:h.to)-p,h.marker,null==h.from),null==h.to)return;h.to==p&&(h=!1)}}if(f<=p)break;for(var T=Math.min(f,v);;){if(m){var M,N=p+m.length;if(h||(M=T<N?m.slice(0,T-p):m,t.addToken(t,M,l?l+a:a,c,p+M.length==v?u:"",s,d)),T<=N){m=m.slice(T-p),p=T;break}p=N,c=""}m=i.slice(o,o=n[g++]),l=rn(n[g++],t.cm.options)}}else for(var A=1;A<n.length;A+=2)t.addToken(t,i.slice(o,o=n[A]),rn(n[A+1],t.cm.options))}function hn(e,t,n){this.line=t,this.rest=function(e){for(var t,n;t=Vt(e);)e=t.find(1,!0).line,(n=n||[]).push(e);return n}(t),this.size=this.rest?it($(this.rest))-n+1:1,this.node=this.text=null,this.hidden=$t(e,t)}function dn(e,t,n){for(var r=[],i=t;i<n;i=l){var o=new hn(e.doc,et(e.doc,i),i),l=i+o.size;r.push(o)}return r}var fn=null;function pn(e,t){var n=e.ownsGroup;if(n)try{!function(e){var t=e.delayedCallbacks,n=0;do{for(;n<t.length;n++)t[n].call(null);for(var r=0;r<e.ops.length;r++){var i=e.ops[r];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(n<t.length)}(n)}finally{fn=null,t(n)}}var gn=null;function mn(e,t){var n=xe(e,t);if(n.length){var r,i=Array.prototype.slice.call(arguments,2);fn?r=fn.delayedCallbacks:gn?r=gn:(r=gn=[],setTimeout(vn,0));for(var o=function(e){r.push(function(){return n[e].apply(null,i)})},l=0;l<n.length;++l)o(l)}}function vn(){var e=gn;gn=null;for(var t=0;t<e.length;++t)e[t]()}function yn(e,t,n,r){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?xn(e,t):"gutter"==o?Sn(e,t,n,r):"class"==o?Cn(e,t):"widget"==o&&Ln(e,t,r)}t.changes=null}function bn(e){return e.node==e.text&&(e.node=O("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),x&&C<8&&(e.node.style.zIndex=2)),e.node}function wn(e,t){var n=e.display.externalMeasured;return n&&n.line==t.line?(e.display.externalMeasured=null,t.measure=n.measure,n.built):on(e,t)}function xn(e,t){var n=t.text.className,r=wn(e,t);t.text==t.node&&(t.node=r.pre),t.text.parentNode.replaceChild(r.pre,t.text),t.text=r.pre,r.bgClass!=t.bgClass||r.textClass!=t.textClass?(t.bgClass=r.bgClass,t.textClass=r.textClass,Cn(e,t)):n&&(t.text.className=n)}function Cn(e,t){var n,r,i,o;n=e,(o=(r=t).bgClass?r.bgClass+" "+(r.line.bgClass||""):r.line.bgClass)&&(o+=" CodeMirror-linebackground"),r.background?o?r.background.className=o:(r.background.parentNode.removeChild(r.background),r.background=null):o&&(i=bn(r),r.background=i.insertBefore(O("div",null,o),i.firstChild),n.display.input.setUneditable(r.background)),t.line.wrapClass?bn(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var l=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=l||""}function Sn(e,t,n,r){var i;t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass&&(i=bn(t),t.gutterBackground=O("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px; width: "+r.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),i.insertBefore(t.gutterBackground,t.text));var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var l=bn(t),s=t.gutter=O("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px");if(e.display.input.setUneditable(s),l.insertBefore(s,t.text),t.line.gutterClass&&(s.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=s.appendChild(O("div",st(e.options,n),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+r.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var a=0;a<e.display.gutterSpecs.length;++a){var u=e.display.gutterSpecs[a].className,c=o.hasOwnProperty(u)&&o[u];c&&s.appendChild(O("div",[c],"CodeMirror-gutter-elt","left: "+r.gutterLeft[u]+"px; width: "+r.gutterWidth[u]+"px"))}}}function Ln(e,t,n){t.alignable&&(t.alignable=null);for(var r=L("CodeMirror-linewidget"),i=t.node.firstChild,o=void 0;i;i=o)o=i.nextSibling,r.test(i.className)&&t.node.removeChild(i);kn(e,t,n)}function kn(e,t,n){if(Tn(e,t.line,t,n,!0),t.rest)for(var r=0;r<t.rest.length;r++)Tn(e,t.rest[r],t,n,!1)}function Tn(e,t,n,r,i){if(t.widgets)for(var o=bn(n),l=0,s=t.widgets;l<s.length;++l){var a=s[l],u=O("div",[a.node],"CodeMirror-linewidget"+(a.className?" "+a.className:""));a.handleMouseEvents||u.setAttribute("cm-ignore-events","true"),Mn(a,u,n,r),e.display.input.setUneditable(u),i&&a.above?o.insertBefore(u,n.gutter||n.text):o.appendChild(u),mn(a,"redraw")}}function Mn(e,t,n,r){var i;e.noHScroll&&((n.alignable||(n.alignable=[])).push(t),i=r.wrapperWidth,t.style.left=r.fixedPos+"px",e.coverGutter||(i-=r.gutterTotalWidth,t.style.paddingLeft=r.gutterTotalWidth+"px"),t.style.width=i+"px"),e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-r.gutterTotalWidth+"px"))}function Nn(e){if(null!=e.height)return e.height;var t,n=e.doc.cm;return n?(D(document.body,e.node)||(t="position: relative;",e.coverGutter&&(t+="margin-left: -"+n.display.gutters.offsetWidth+"px;"),e.noHScroll&&(t+="width: "+n.display.wrapper.clientWidth+"px;"),N(n.display.measure,O("div",[e.node],null,t))),e.height=e.node.parentNode.offsetHeight):0}function An(e,t){for(var n=We(t);n!=e.wrapper;n=n.parentNode)if(!n||1==n.nodeType&&"true"==n.getAttribute("cm-ignore-events")||n.parentNode==e.sizer&&n!=e.mover)return 1}function On(e){return e.lineSpace.offsetTop}function Dn(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function Wn(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=N(e.measure,O("pre","x","CodeMirror-line-like")),n=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,r={left:parseInt(n.paddingLeft),right:parseInt(n.paddingRight)};return isNaN(r.left)||isNaN(r.right)||(e.cachedPaddingH=r),r}function Hn(e){return G-e.display.nativeBarWidth}function Fn(e){return e.display.scroller.clientWidth-Hn(e)-e.display.barWidth}function Pn(e){return e.display.scroller.clientHeight-Hn(e)-e.display.barHeight}function En(e,t,n){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};for(var r=0;r<e.rest.length;r++)if(e.rest[r]==t)return{map:e.measure.maps[r],cache:e.measure.caches[r]};for(var i=0;i<e.rest.length;i++)if(it(e.rest[i])>n)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}function In(e,t,n,r){return Bn(e,zn(e,t),n,r)}function Rn(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[gr(e,t)];var n=e.display.externalMeasured;return n&&t>=n.lineN&&t<n.lineN+n.size?n:void 0}function zn(e,t){var n=it(t),r=Rn(e,n);r&&!r.text?r=null:r&&r.changes&&(yn(e,r,n,cr(e)),e.curOp.forceUpdate=!0);var i=En(r=r||function(e,t){var n=it(t=Xt(t)),r=e.display.externalMeasured=new hn(e.doc,t,n);r.lineN=n;var i=r.built=on(e,r);return r.text=i.pre,N(e.display.lineMeasure,i.pre),r}(e,t),t,n);return{line:t,view:r,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function Bn(e,t,n,r,i){t.before&&(n=-1);var o,l=n+(r||"");return t.cache.hasOwnProperty(l)?o=t.cache[l]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(function(e,t,n){var r=e.options.lineWrapping,i=r&&Fn(e);if(!t.measure.heights||r&&t.measure.width!=i){var o=t.measure.heights=[];if(r){t.measure.width=i;for(var l=t.text.firstChild.getClientRects(),s=0;s<l.length-1;s++){var a=l[s],u=l[s+1];2<Math.abs(a.bottom-u.bottom)&&o.push((a.bottom+u.top)/2-n.top)}}o.push(n.bottom-n.top)}}(e,t.view,t.rect),t.hasHeights=!0),(o=function(e,t,n,r){var i,o,l=Vn(t.map,n,r),s=l.node,a=l.start,u=l.end,c=l.collapse;if(3==s.nodeType){for(var h=0;h<4;h++){for(;a&&ie(t.line.text.charAt(l.coverStart+a));)--a;for(;l.coverStart+u<l.coverEnd&&ie(t.line.text.charAt(l.coverStart+u));)++u;if((i=x&&C<9&&0==a&&u==l.coverEnd-l.coverStart?s.parentNode.getBoundingClientRect():Kn(k(s,a,u).getClientRects(),r)).left||i.right||0==a)break;u=a,--a,c="right"}x&&C<11&&(i=function(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!function(e){if(null!=Ve)return Ve;var t=N(e,O("span","x")),n=t.getBoundingClientRect(),r=k(t,0,1).getBoundingClientRect();return Ve=1<Math.abs(n.left-r.left)}(e))return t;var n=screen.logicalXDPI/screen.deviceXDPI,r=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*n,right:t.right*n,top:t.top*r,bottom:t.bottom*r}}(e.display.measure,i))}else 0<a&&(c=r="right"),i=e.options.lineWrapping&&1<(o=s.getClientRects()).length?o["right"==r?o.length-1:0]:s.getBoundingClientRect();{var d;!(x&&C<9)||a||i&&(i.left||i.right)||(d=s.parentNode.getClientRects()[0],i=d?{left:d.left,right:d.left+ur(e.display),top:d.top,bottom:d.bottom}:Un)}for(var f=i.top-t.rect.top,p=i.bottom-t.rect.top,g=(f+p)/2,m=t.view.measure.heights,v=0;v<m.length-1&&!(g<m[v]);v++);var y=v?m[v-1]:0,b=m[v],w={left:("right"==c?i.right:i.left)-t.rect.left,right:("left"==c?i.left:i.right)-t.rect.left,top:y,bottom:b};i.left||i.right||(w.bogus=!0);e.options.singleCursorHeightPerLine||(w.rtop=f,w.rbottom=p);return w}(e,t,n,r)).bogus||(t.cache[l]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var Gn,Un={left:0,right:0,top:0,bottom:0};function Vn(e,t,n){for(var r,i,o,l,s,a,u=0;u<e.length;u+=3)if(s=e[u],a=e[u+1],t<s?(i=0,o=1,l="left"):t<a?o=(i=t-s)+1:(u==e.length-3||t==a&&e[u+3]>t)&&(i=(o=a-s)-1,a<=t&&(l="right")),null!=i){if(r=e[u+2],s==a&&n==(r.insertLeft?"left":"right")&&(l=n),"left"==n&&0==i)for(;u&&e[u-2]==e[u-3]&&e[u-1].insertLeft;)r=e[2+(u-=3)],l="left";if("right"==n&&i==a-s)for(;u<e.length-3&&e[u+3]==e[u+4]&&!e[u+5].insertLeft;)r=e[(u+=3)+2],l="right";break}return{node:r,start:i,end:o,collapse:l,coverStart:s,coverEnd:a}}function Kn(e,t){var n=Un;if("left"==t)for(var r=0;r<e.length&&(n=e[r]).left==n.right;r++);else for(var i=e.length-1;0<=i&&(n=e[i]).left==n.right;i--);return n}function jn(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function Xn(e){e.display.externalMeasure=null,M(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)jn(e.display.view[t])}function Yn(e){Xn(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function _n(){return l&&u?-(document.body.getBoundingClientRect().left-parseInt(getComputedStyle(document.body).marginLeft)):window.pageXOffset||(document.documentElement||document.body).scrollLeft}function $n(){return l&&u?-(document.body.getBoundingClientRect().top-parseInt(getComputedStyle(document.body).marginTop)):window.pageYOffset||(document.documentElement||document.body).scrollTop}function qn(e){var t=0;if(e.widgets)for(var n=0;n<e.widgets.length;++n)e.widgets[n].above&&(t+=Nn(e.widgets[n]));return t}function Zn(e,t,n,r,i){var o;if(i||(o=qn(t),n.top+=o,n.bottom+=o),"line"==r)return n;r=r||"local";var l,s,a=Zt(t);return"local"==r?a+=On(e.display):a-=e.display.viewOffset,"page"!=r&&"window"!=r||(a+=(l=e.display.lineSpace.getBoundingClientRect()).top+("window"==r?0:$n()),s=l.left+("window"==r?0:_n()),n.left+=s,n.right+=s),n.top+=a,n.bottom+=a,n}function Qn(e,t,n){if("div"==n)return t;var r,i=t.left,o=t.top;"page"==n?(i-=_n(),o-=$n()):"local"!=n&&n||(i+=(r=e.display.sizer.getBoundingClientRect()).left,o+=r.top);var l=e.display.lineSpace.getBoundingClientRect();return{left:i-l.left,top:o-l.top}}function Jn(e,t,n,r,i){return Zn(e,r=r||et(e.doc,t.line),In(e,r,t.ch,i),n)}function er(r,e,i,o,l,s){function a(e,t){var n=Bn(r,l,e,t?"right":"left",s);return t?n.left=n.right:n.right=n.left,Zn(r,o,n,i)}o=o||et(r.doc,e.line),l=l||zn(r,o);var u=ye(o,r.doc.direction),t=e.ch,n=e.sticky;if(t>=o.text.length?(t=o.text.length,n="before"):t<=0&&(t=0,n="after"),!u)return a("before"==n?t-1:t,"before"==n);function c(e,t,n){return a(n?e-1:e,1==u[t].level!=n)}var h=ae(u,t,n),d=se,f=c(t,h,"before"==n);return null!=d&&(f.other=c(t,d,"before"!=n)),f}function tr(e,t){var n=0;t=gt(e.doc,t),e.options.lineWrapping||(n=ur(e.display)*t.ch);var r=et(e.doc,t.line),i=Zt(r)+On(e.display);return{left:n,right:n,top:i,bottom:i+r.height}}function nr(e,t,n,r,i){var o=at(e,t,n);return o.xRel=i,r&&(o.outside=r),o}function rr(e,t,n){var r=e.doc;if((n+=e.display.viewOffset)<0)return nr(r.first,0,null,-1,-1);var i=ot(r,n),o=r.first+r.size-1;if(o<i)return nr(r.first+r.size-1,et(r,o).text.length,null,1,1);t<0&&(t=0);for(var l=et(r,i);;){var s=sr(e,l,i,t,n),a=Kt(l,s.ch+(0<s.xRel||0<s.outside?1:0));if(!a)return s;var u=a.find(1);if(u.line==i)return u;l=et(r,i=u.line)}}function ir(t,e,n,r){r-=qn(e);var i=e.text.length,o=le(function(e){return Bn(t,n,e-1).bottom<=r},i,0);return{begin:o,end:i=le(function(e){return Bn(t,n,e).top>r},o,i)}}function or(e,t,n,r){return ir(e,t,n=n||zn(e,t),Zn(e,t,Bn(e,n,r),"line").top)}function lr(e,t,n,r){return!(e.bottom<=n)&&(e.top>n||(r?e.left:e.right)>t)}function sr(n,e,t,r,i){i-=Zt(e);var o,l=zn(n,e),s=qn(e),a=0,u=e.text.length,c=!0,h=ye(e,n.doc.direction);h&&(a=(c=1!=(o=(n.options.lineWrapping?function(e,t,n,r,i,o,l){var s=ir(e,t,r,l),a=s.begin,u=s.end;/\s/.test(t.text.charAt(u-1))&&u--;for(var c=null,h=null,d=0;d<i.length;d++){var f,p,g,m=i[d];m.from>=u||m.to<=a||(f=1!=m.level,p=Bn(e,r,f?Math.min(u,m.to)-1:Math.max(a,m.from)).right,g=p<o?o-p+1e9:p-o,(!c||g<h)&&(c=m,h=g))}c=c||i[i.length-1];c.from<a&&(c={from:a,to:c.to,level:c.level});c.to>u&&(c={from:c.from,to:u,level:c.level});return c}:function(r,i,o,l,s,a,u){var e=le(function(e){var t=s[e],n=1!=t.level;return lr(er(r,at(o,n?t.to:t.from,n?"before":"after"),"line",i,l),a,u,!0)},0,s.length-1),t=s[e];{var n,c;0<e&&(n=1!=t.level,lr(c=er(r,at(o,n?t.from:t.to,n?"after":"before"),"line",i,l),a,u,!0)&&c.top>u&&(t=s[e-1]))}return t})(n,e,t,l,h,r,i)).level)?o.from:o.to-1,u=c?o.to:o.from-1);var d,f,p,g,m,v=null,y=null,b=le(function(e){var t=Bn(n,l,e);return t.top+=s,t.bottom+=s,lr(t,r,i,!1)&&(t.top<=i&&t.left<=r&&(v=e,y=t),1)},a,u),w=!1;return y?(d=r-y.left<y.right-r,b=v+((f=d==c)?0:1),p=f?"after":"before",g=d?y.left:y.right):(c||b!=u&&b!=a||b++,p=0==b||b!=e.text.length&&Bn(n,l,b-(c?1:0)).bottom+s<=i==c?"after":"before",g=(m=er(n,at(t,b,p),"line",e,l)).left,w=i<m.top?-1:i>=m.bottom?1:0),nr(t,b=oe(e.text,b,1),p,w,r-g)}function ar(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==Gn){Gn=O("pre",null,"CodeMirror-line-like");for(var t=0;t<49;++t)Gn.appendChild(document.createTextNode("x")),Gn.appendChild(O("br"));Gn.appendChild(document.createTextNode("x"))}N(e.measure,Gn);var n=Gn.offsetHeight/50;return 3<n&&(e.cachedTextHeight=n),M(e.measure),n||1}function ur(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=O("span","xxxxxxxxxx"),n=O("pre",[t],"CodeMirror-line-like");N(e.measure,n);var r=t.getBoundingClientRect(),i=(r.right-r.left)/10;return 2<i&&(e.cachedCharWidth=i),i||10}function cr(e){for(var t=e.display,n={},r={},i=t.gutters.clientLeft,o=t.gutters.firstChild,l=0;o;o=o.nextSibling,++l){var s=e.display.gutterSpecs[l].className;n[s]=o.offsetLeft+o.clientLeft+i,r[s]=o.clientWidth}return{fixedPos:hr(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:n,gutterWidth:r,wrapperWidth:t.wrapper.clientWidth}}function hr(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function dr(r){var i=ar(r.display),o=r.options.lineWrapping,l=o&&Math.max(5,r.display.scroller.clientWidth/ur(r.display)-3);return function(e){if($t(r.doc,e))return 0;var t=0;if(e.widgets)for(var n=0;n<e.widgets.length;n++)e.widgets[n].height&&(t+=e.widgets[n].height);return o?t+(Math.ceil(e.text.length/l)||1)*i:t+i}}function fr(e){var t=e.doc,n=dr(e);t.iter(function(e){var t=n(e);t!=e.height&&rt(e,t)})}function pr(e,t,n,r){var i=e.display;if(!n&&"true"==We(t).getAttribute("cm-not-content"))return null;var o,l,s=i.lineSpace.getBoundingClientRect();try{o=t.clientX-s.left,l=t.clientY-s.top}catch(e){return null}var a,u,c=rr(e,o,l);return r&&0<c.xRel&&(a=et(e.doc,c.line).text).length==c.ch&&(u=R(a,a.length,e.options.tabSize)-a.length,c=at(c.line,Math.max(0,Math.round((o-Wn(e.display).left)/ur(e.display))-u))),c}function gr(e,t){if(t>=e.display.viewTo)return null;if((t-=e.display.viewFrom)<0)return null;for(var n=e.display.view,r=0;r<n.length;r++)if((t-=n[r].size)<0)return r}function mr(e,t,n,r){null==t&&(t=e.doc.first),null==n&&(n=e.doc.first+e.doc.size),r=r||0;var i,o,l,s,a=e.display;r&&n<a.viewTo&&(null==a.updateLineNumbers||a.updateLineNumbers>t)&&(a.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=a.viewTo?Ot&&Yt(e.doc,t)<a.viewTo&&yr(e):n<=a.viewFrom?Ot&&_t(e.doc,n+r)>a.viewFrom?yr(e):(a.viewFrom+=r,a.viewTo+=r):t<=a.viewFrom&&n>=a.viewTo?yr(e):t<=a.viewFrom?(i=br(e,n,n+r,1))?(a.view=a.view.slice(i.index),a.viewFrom=i.lineN,a.viewTo+=r):yr(e):n>=a.viewTo?(o=br(e,t,t,-1))?(a.view=a.view.slice(0,o.index),a.viewTo=o.lineN):yr(e):(l=br(e,t,t,-1),s=br(e,n,n+r,1),l&&s?(a.view=a.view.slice(0,l.index).concat(dn(e,l.lineN,s.lineN)).concat(a.view.slice(s.index)),a.viewTo+=r):yr(e));var u=a.externalMeasured;u&&(n<u.lineN?u.lineN+=r:t<u.lineN+u.size&&(a.externalMeasured=null))}function vr(e,t,n){e.curOp.viewChanged=!0;var r,i,o=e.display,l=e.display.externalMeasured;l&&t>=l.lineN&&t<l.lineN+l.size&&(o.externalMeasured=null),t<o.viewFrom||t>=o.viewTo||(null==(r=o.view[gr(e,t)]).node||-1==B(i=r.changes||(r.changes=[]),n)&&i.push(n))}function yr(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function br(e,t,n,r){var i,o=gr(e,t),l=e.display.view;if(!Ot||n==e.doc.first+e.doc.size)return{index:o,lineN:n};for(var s=e.display.viewFrom,a=0;a<o;a++)s+=l[a].size;if(s!=t){if(0<r){if(o==l.length-1)return null;i=s+l[o].size-t,o++}else i=s-t;t+=i,n+=i}for(;Yt(e.doc,n)!=n;){if(o==(r<0?0:l.length-1))return null;n+=r*l[o-(r<0?1:0)].size,o+=r}return{index:o,lineN:n}}function wr(e){for(var t=e.display.view,n=0,r=0;r<t.length;r++){var i=t[r];i.hidden||i.node&&!i.changes||++n}return n}function xr(e){e.display.input.showSelection(e.display.input.prepareSelection())}function Cr(e,t){void 0===t&&(t=!0);for(var n,r,i=e.doc,o={},l=o.cursors=document.createDocumentFragment(),s=o.selection=document.createDocumentFragment(),a=0;a<i.sel.ranges.length;a++){!t&&a==i.sel.primIndex||((n=i.sel.ranges[a]).from().line>=e.display.viewTo||n.to().line<e.display.viewFrom||(((r=n.empty())||e.options.showCursorWhenSelecting)&&Sr(e,n.head,l),r||kr(e,n,s)))}return o}function Sr(e,t,n){var r,i=er(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),o=n.appendChild(O("div"," ","CodeMirror-cursor"));o.style.left=i.left+"px",o.style.top=i.top+"px",o.style.height=Math.max(0,i.bottom-i.top)*e.options.cursorHeight+"px",i.other&&((r=n.appendChild(O("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"))).style.display="",r.style.left=i.other.left+"px",r.style.top=i.other.top+"px",r.style.height=.85*(i.other.bottom-i.other.top)+"px")}function Lr(e,t){return e.top-t.top||e.left-t.left}function kr(l,e,t){var n=l.display,r=l.doc,i=document.createDocumentFragment(),o=Wn(l.display),T=o.left,M=Math.max(n.sizerWidth,Fn(l)-n.sizer.offsetLeft)-o.right,N="ltr"==r.direction;function A(e,t,n,r){t<0&&(t=0),t=Math.round(t),r=Math.round(r),i.appendChild(O("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==n?M-e:n)+"px;\n                             height: "+(r-t)+"px"))}function s(n,y,b){var w,x,o=et(r,n),C=o.text.length;function S(e,t){return Jn(l,at(n,e),"div",o,t)}function L(e,t,n){var r=or(l,o,null,e),i="ltr"==t==("after"==n)?"left":"right";return S("after"==n?r.begin:r.end-(/\s/.test(o.text.charAt(r.end-1))?2:1),i)[i]}var k=ye(o,r.direction);return function(e,t,n,r){if(!e)return r(t,n,"ltr",0);for(var i=!1,o=0;o<e.length;++o){var l=e[o];(l.from<n&&l.to>t||t==n&&l.to==t)&&(r(Math.max(l.from,t),Math.min(l.to,n),1==l.level?"rtl":"ltr",o),i=!0)}i||r(t,n,"ltr")}(k,y||0,null==b?C:b,function(e,t,n,r){var i,o,l,s,a,u,c,h="ltr"==n,d=S(e,h?"left":"right"),f=S(t-1,h?"right":"left"),p=null==y&&0==e,g=null==b&&t==C,m=0==r,v=!k||r==k.length-1;f.top-d.top<=3?(i=(N?g:p)&&v,o=(N?p:g)&&m?T:(h?d:f).left,l=i?M:(h?f:d).right,A(o,d.top,l-o,d.bottom)):(c=h?(s=N&&p&&m?T:d.left,a=N?M:L(e,n,"before"),u=N?T:L(t,n,"after"),N&&g&&v?M:f.right):(s=N?L(e,n,"before"):T,a=!N&&p&&m?M:d.right,u=!N&&g&&v?T:f.left,N?L(t,n,"after"):M),A(s,d.top,a-s,d.bottom),d.bottom<f.top&&A(T,d.bottom,null,f.top),A(u,f.top,c-u,f.bottom)),(!w||Lr(d,w)<0)&&(w=d),Lr(f,w)<0&&(w=f),(!x||Lr(d,x)<0)&&(x=d),Lr(f,x)<0&&(x=f)}),{start:w,end:x}}var a,u,c,h,d,f=e.from(),p=e.to();f.line==p.line?s(f.line,f.ch,p.ch):(a=et(r,f.line),u=et(r,p.line),c=Xt(a)==Xt(u),h=s(f.line,f.ch,c?a.text.length+1:null).end,d=s(p.line,c?0:null,p.ch).start,c&&(h.top<d.top-2?(A(h.right,h.top,null,h.bottom),A(T,d.top,d.left,d.bottom)):A(h.right,h.top,d.left-h.right,h.bottom)),h.bottom<d.top&&A(T,h.bottom,null,d.top)),t.appendChild(i)}function Tr(e){var t,n;e.state.focused&&(t=e.display,clearInterval(t.blinker),n=!0,t.cursorDiv.style.visibility="",0<e.options.cursorBlinkRate?t.blinker=setInterval(function(){return t.cursorDiv.style.visibility=(n=!n)?"":"hidden"},e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden"))}function Mr(e){e.state.focused||(e.display.input.focus(),Ar(e))}function Nr(e){e.state.delayingBlurEvent=!0,setTimeout(function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,Or(e))},100)}function Ar(e,t){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(Se(e,"focus",e,t),e.state.focused=!0,H(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),b&&setTimeout(function(){return e.display.input.reset(!0)},20)),e.display.input.receivedFocus()),Tr(e))}function Or(e,t){e.state.delayingBlurEvent||(e.state.focused&&(Se(e,"blur",e,t),e.state.focused=!1,T(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout(function(){e.state.focused||(e.display.shift=!1)},150))}function Dr(e){for(var t=e.display,n=t.lineDiv.offsetTop,r=0;r<t.view.length;r++){var i,o,l=t.view[r],s=e.options.lineWrapping,a=void 0,u=0;if(!l.hidden){x&&C<8?(a=(i=l.node.offsetTop+l.node.offsetHeight)-n,n=i):(a=(o=l.node.getBoundingClientRect()).bottom-o.top,!s&&l.text.firstChild&&(u=l.text.firstChild.getBoundingClientRect().right-o.left-1));var c,h=l.line.height-a;if((.005<h||h<-.005)&&(rt(l.line,a),Wr(l.line),l.rest))for(var d=0;d<l.rest.length;d++)Wr(l.rest[d]);u>e.display.sizerWidth&&((c=Math.ceil(u/ur(e.display)))>e.display.maxLineLength&&(e.display.maxLineLength=c,e.display.maxLine=l.line,e.display.maxLineChanged=!0))}}}function Wr(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var n=e.widgets[t],r=n.node.parentNode;r&&(n.height=r.offsetHeight)}}function Hr(e,t,n){var r,i,o=n&&null!=n.top?Math.max(0,n.top):e.scroller.scrollTop,o=Math.floor(o-On(e)),l=n&&null!=n.bottom?n.bottom:o+e.wrapper.clientHeight,s=ot(t,o),a=ot(t,l);return n&&n.ensure&&(r=n.ensure.from.line,i=n.ensure.to.line,r<s?a=ot(t,Zt(et(t,s=r))+e.wrapper.clientHeight):Math.min(i,t.lastLine())>=a&&(s=ot(t,Zt(et(t,i))-e.wrapper.clientHeight),a=i)),{from:s,to:Math.max(a,s+1)}}function Fr(e,t){var n=e.display,r=ar(e.display);t.top<0&&(t.top=0);var i=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:n.scroller.scrollTop,o=Pn(e),l={};t.bottom-t.top>o&&(t.bottom=t.top+o);var s,a=e.doc.height+Dn(n),u=t.top<r,c=t.bottom>a-r;t.top<i?l.scrollTop=u?0:t.top:t.bottom>i+o&&((s=Math.min(t.top,(c?a:t.bottom)-o))!=i&&(l.scrollTop=s));var h=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:n.scroller.scrollLeft,d=Fn(e)-(e.options.fixedGutter?n.gutters.offsetWidth:0),f=t.right-t.left>d;return f&&(t.right=t.left+d),t.left<10?l.scrollLeft=0:t.left<h?l.scrollLeft=Math.max(0,t.left-(f?0:10)):t.right>d+h-3&&(l.scrollLeft=t.right+(f?0:10)-d),l}function Pr(e,t){null!=t&&(Rr(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function Er(e){Rr(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function Ir(e,t,n){null==t&&null==n||Rr(e),null!=t&&(e.curOp.scrollLeft=t),null!=n&&(e.curOp.scrollTop=n)}function Rr(e){var t=e.curOp.scrollToPos;t&&(e.curOp.scrollToPos=null,zr(e,tr(e,t.from),tr(e,t.to),t.margin))}function zr(e,t,n,r){var i=Fr(e,{left:Math.min(t.left,n.left),top:Math.min(t.top,n.top)-r,right:Math.max(t.right,n.right),bottom:Math.max(t.bottom,n.bottom)+r});Ir(e,i.scrollLeft,i.scrollTop)}function Br(e,t){Math.abs(e.doc.scrollTop-t)<2||(g||di(e,{top:t}),Gr(e,t,!0),g&&di(e),si(e,100))}function Gr(e,t,n){t=Math.max(0,Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t)),e.display.scroller.scrollTop==t&&!n||(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function Ur(e,t,n,r){t=Math.max(0,Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth)),(n?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!r||(e.doc.scrollLeft=t,gi(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function Vr(e){var t=e.display,n=t.gutters.offsetWidth,r=Math.round(e.doc.height+Dn(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?n:0,docHeight:r,scrollHeight:r+Hn(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:n}}function Kr(e,t,n){this.cm=n;var r=this.vert=O("div",[O("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=O("div",[O("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");r.tabIndex=i.tabIndex=-1,e(r),e(i),we(r,"scroll",function(){r.clientHeight&&t(r.scrollTop,"vertical")}),we(i,"scroll",function(){i.clientWidth&&t(i.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,x&&C<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")}Kr.prototype.update=function(e){var t,n,r=e.scrollWidth>e.clientWidth+1,i=e.scrollHeight>e.clientHeight+1,o=e.nativeBarWidth;return i?(this.vert.style.display="block",this.vert.style.bottom=r?o+"px":"0",t=e.viewHeight-(r?o:0),this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+t)+"px"):(this.vert.style.display="",this.vert.firstChild.style.height="0"),r?(this.horiz.style.display="block",this.horiz.style.right=i?o+"px":"0",this.horiz.style.left=e.barLeft+"px",n=e.viewWidth-e.barLeft-(i?o:0),this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+n)+"px"):(this.horiz.style.display="",this.horiz.firstChild.style.width="0"),!this.checkedZeroWidth&&0<e.clientHeight&&(0==o&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:i?o:0,bottom:r?o:0}},Kr.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},Kr.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},Kr.prototype.zeroWidthHack=function(){var e=w&&!s?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.pointerEvents=this.vert.style.pointerEvents="none",this.disableHoriz=new z,this.disableVert=new z},Kr.prototype.enableZeroWidthBar=function(n,r,i){n.style.pointerEvents="auto",r.set(1e3,function e(){var t=n.getBoundingClientRect();("vert"==i?document.elementFromPoint(t.right-1,(t.top+t.bottom)/2):document.elementFromPoint((t.right+t.left)/2,t.bottom-1))!=n?n.style.pointerEvents="none":r.set(1e3,e)})},Kr.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};function jr(){}function Xr(e,t){t=t||Vr(e);var n=e.display.barWidth,r=e.display.barHeight;Yr(e,t);for(var i=0;i<4&&n!=e.display.barWidth||r!=e.display.barHeight;i++)n!=e.display.barWidth&&e.options.lineWrapping&&Dr(e),Yr(e,Vr(e)),n=e.display.barWidth,r=e.display.barHeight}function Yr(e,t){var n=e.display,r=n.scrollbars.update(t);n.sizer.style.paddingRight=(n.barWidth=r.right)+"px",n.sizer.style.paddingBottom=(n.barHeight=r.bottom)+"px",n.heightForcer.style.borderBottom=r.bottom+"px solid transparent",r.right&&r.bottom?(n.scrollbarFiller.style.display="block",n.scrollbarFiller.style.height=r.bottom+"px",n.scrollbarFiller.style.width=r.right+"px"):n.scrollbarFiller.style.display="",r.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(n.gutterFiller.style.display="block",n.gutterFiller.style.height=r.bottom+"px",n.gutterFiller.style.width=t.gutterWidth+"px"):n.gutterFiller.style.display=""}jr.prototype.update=function(){return{bottom:0,right:0}},jr.prototype.setScrollLeft=function(){},jr.prototype.setScrollTop=function(){},jr.prototype.clear=function(){};var _r={native:Kr,null:jr};function $r(n){n.display.scrollbars&&(n.display.scrollbars.clear(),n.display.scrollbars.addClass&&T(n.display.wrapper,n.display.scrollbars.addClass)),n.display.scrollbars=new _r[n.options.scrollbarStyle](function(e){n.display.wrapper.insertBefore(e,n.display.scrollbarFiller),we(e,"mousedown",function(){n.state.focused&&setTimeout(function(){return n.display.input.focus()},0)}),e.setAttribute("cm-not-content","true")},function(e,t){("horizontal"==t?Ur:Br)(n,e)},n),n.display.scrollbars.addClass&&H(n.display.wrapper,n.display.scrollbars.addClass)}var qr=0;function Zr(e){var t;e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++qr},t=e.curOp,fn?fn.ops.push(t):t.ownsGroup=fn={ops:[t],delayedCallbacks:[]}}function Qr(e){var t=e.curOp;t&&pn(t,function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;!function(e){for(var t=e.ops,n=0;n<t.length;n++)Jr(t[n]);for(var r=0;r<t.length;r++)(i=t[r]).updatedDisplay=i.mustUpdate&&ci(i.cm,i.update);var i;for(var o=0;o<t.length;o++)ei(t[o]);for(var l=0;l<t.length;l++)ti(t[l]);for(var s=0;s<t.length;s++)ni(t[s])}(e)})}function Jr(e){var t,n,r=e.cm,i=r.display;!(n=(t=r).display).scrollbarsClipped&&n.scroller.offsetWidth&&(n.nativeBarWidth=n.scroller.offsetWidth-n.scroller.clientWidth,n.heightForcer.style.height=Hn(t)+"px",n.sizer.style.marginBottom=-n.nativeBarWidth+"px",n.sizer.style.borderRightWidth=Hn(t)+"px",n.scrollbarsClipped=!0),e.updateMaxLine&&Jt(r),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<i.viewFrom||e.scrollToPos.to.line>=i.viewTo)||i.maxLineChanged&&r.options.lineWrapping,e.update=e.mustUpdate&&new ui(r,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function ei(e){var t=e.cm,n=t.display;e.updatedDisplay&&Dr(t),e.barMeasure=Vr(t),n.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=In(t,n.maxLine,n.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(n.scroller.clientWidth,n.sizer.offsetLeft+e.adjustWidthTo+Hn(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,n.sizer.offsetLeft+e.adjustWidthTo-Fn(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=n.input.prepareSelection())}function ti(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&Ur(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var n=e.focus&&e.focus==W();e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,n),!e.updatedDisplay&&e.startHeight==t.doc.height||Xr(t,e.barMeasure),e.updatedDisplay&&pi(t,e.barMeasure),e.selectionChanged&&Tr(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),n&&Mr(e.cm)}function ni(e){var t,n,r,i,o,l,s,a=e.cm,u=a.display,c=a.doc;e.updatedDisplay&&hi(a,e.update),null==u.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(u.wheelStartX=u.wheelStartY=null),null!=e.scrollTop&&Gr(a,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&Ur(a,e.scrollLeft,!0,!0),e.scrollToPos&&(t=function(e,t,n,r){null==r&&(r=0),e.options.lineWrapping||t!=n||(n="before"==(t=t.ch?at(t.line,"before"==t.sticky?t.ch-1:t.ch,"after"):t).sticky?at(t.line,t.ch+1,"before"):t);for(var i=0;i<5;i++){var o,l=!1,s=er(e,t),a=n&&n!=t?er(e,n):s,u=Fr(e,o={left:Math.min(s.left,a.left),top:Math.min(s.top,a.top)-r,right:Math.max(s.left,a.left),bottom:Math.max(s.bottom,a.bottom)+r}),c=e.doc.scrollTop,h=e.doc.scrollLeft;if(null!=u.scrollTop&&(Br(e,u.scrollTop),1<Math.abs(e.doc.scrollTop-c)&&(l=!0)),null!=u.scrollLeft&&(Ur(e,u.scrollLeft),1<Math.abs(e.doc.scrollLeft-h)&&(l=!0)),!l)break}return o}(a,gt(c,e.scrollToPos.from),gt(c,e.scrollToPos.to),e.scrollToPos.margin),r=t,Le(n=a,"scrollCursorIntoView")||(o=(i=n.display).sizer.getBoundingClientRect(),l=null,r.top+o.top<0?l=!0:r.bottom+o.top>(window.innerHeight||document.documentElement.clientHeight)&&(l=!1),null==l||v||(s=O("div","​",null,"position: absolute;\n                         top: "+(r.top-i.viewOffset-On(n.display))+"px;\n                         height: "+(r.bottom-r.top+Hn(n)+i.barHeight)+"px;\n                         left: "+r.left+"px; width: "+Math.max(2,r.right-r.left)+"px;"),n.display.lineSpace.appendChild(s),s.scrollIntoView(l),n.display.lineSpace.removeChild(s))));var h=e.maybeHiddenMarkers,d=e.maybeUnhiddenMarkers;if(h)for(var f=0;f<h.length;++f)h[f].lines.length||Se(h[f],"hide");if(d)for(var p=0;p<d.length;++p)d[p].lines.length&&Se(d[p],"unhide");u.wrapper.offsetHeight&&(c.scrollTop=a.display.scroller.scrollTop),e.changeObjs&&Se(a,"changes",a,e.changeObjs),e.update&&e.update.finish()}function ri(e,t){if(e.curOp)return t();Zr(e);try{return t()}finally{Qr(e)}}function ii(e,t){return function(){if(e.curOp)return t.apply(e,arguments);Zr(e);try{return t.apply(e,arguments)}finally{Qr(e)}}}function oi(e){return function(){if(this.curOp)return e.apply(this,arguments);Zr(this);try{return e.apply(this,arguments)}finally{Qr(this)}}}function li(t){return function(){var e=this.cm;if(!e||e.curOp)return t.apply(this,arguments);Zr(e);try{return t.apply(this,arguments)}finally{Qr(e)}}}function si(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,E(ai,e))}function ai(a){var u,c,h,d=a.doc;d.highlightFrontier>=a.display.viewTo||(u=+new Date+a.options.workTime,c=xt(a,d.highlightFrontier),h=[],d.iter(c.line,Math.min(d.first+d.size,a.display.viewTo+500),function(e){if(c.line>=a.display.viewFrom){var t=e.styles,n=e.text.length>a.options.maxHighlightLength?qe(d.mode,c.state):null,r=bt(a,e,c,!0);n&&(c.state=n),e.styles=r.styles;var i=e.styleClasses,o=r.classes;o?e.styleClasses=o:i&&(e.styleClasses=null);for(var l=!t||t.length!=e.styles.length||i!=o&&(!i||!o||i.bgClass!=o.bgClass||i.textClass!=o.textClass),s=0;!l&&s<t.length;++s)l=t[s]!=e.styles[s];l&&h.push(c.line),e.stateAfter=c.save(),c.nextLine()}else e.text.length<=a.options.maxHighlightLength&&Ct(a,e.text,c),e.stateAfter=c.line%5==0?c.save():null,c.nextLine();if(+new Date>u)return si(a,a.options.workDelay),!0}),d.highlightFrontier=c.line,d.modeFrontier=Math.max(d.modeFrontier,c.line),h.length&&ri(a,function(){for(var e=0;e<h.length;e++)vr(a,h[e],"text")}))}var ui=function(e,t,n){var r=e.display;this.viewport=t,this.visible=Hr(r,e.doc,t),this.editorIsHidden=!r.wrapper.offsetWidth,this.wrapperHeight=r.wrapper.clientHeight,this.wrapperWidth=r.wrapper.clientWidth,this.oldDisplayWidth=Fn(e),this.force=n,this.dims=cr(e),this.events=[]};function ci(e,t){var n=e.display,r=e.doc;if(t.editorIsHidden)return yr(e),!1;if(!t.force&&t.visible.from>=n.viewFrom&&t.visible.to<=n.viewTo&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo)&&n.renderedView==n.view&&0==wr(e))return!1;mi(e)&&(yr(e),t.dims=cr(e));var i=r.first+r.size,o=Math.max(t.visible.from-e.options.viewportMargin,r.first),l=Math.min(i,t.visible.to+e.options.viewportMargin);n.viewFrom<o&&o-n.viewFrom<20&&(o=Math.max(r.first,n.viewFrom)),n.viewTo>l&&n.viewTo-l<20&&(l=Math.min(i,n.viewTo)),Ot&&(o=Yt(e.doc,o),l=_t(e.doc,l));var s,a,u,c,h=o!=n.viewFrom||l!=n.viewTo||n.lastWrapHeight!=t.wrapperHeight||n.lastWrapWidth!=t.wrapperWidth;a=o,u=l,0==(c=(s=e).display).view.length||a>=c.viewTo||u<=c.viewFrom?(c.view=dn(s,a,u),c.viewFrom=a):(c.viewFrom>a?c.view=dn(s,a,c.viewFrom).concat(c.view):c.viewFrom<a&&(c.view=c.view.slice(gr(s,a))),c.viewFrom=a,c.viewTo<u?c.view=c.view.concat(dn(s,c.viewTo,u)):c.viewTo>u&&(c.view=c.view.slice(0,gr(s,u)))),c.viewTo=u,n.viewOffset=Zt(et(e.doc,n.viewFrom)),e.display.mover.style.top=n.viewOffset+"px";var d=wr(e);if(!h&&0==d&&!t.force&&n.renderedView==n.view&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo))return!1;var f,p,g,m=function(e){if(e.hasFocus())return null;var t=W();if(!t||!D(e.display.lineDiv,t))return null;var n,r={activeElt:t};return!window.getSelection||(n=window.getSelection()).anchorNode&&n.extend&&D(e.display.lineDiv,n.anchorNode)&&(r.anchorNode=n.anchorNode,r.anchorOffset=n.anchorOffset,r.focusNode=n.focusNode,r.focusOffset=n.focusOffset),r}(e);return 4<d&&(n.lineDiv.style.display="none"),function(n,e,t){var r=n.display,i=n.options.lineNumbers,o=r.lineDiv,l=o.firstChild;function s(e){var t=e.nextSibling;return b&&w&&n.display.currentWheelTarget==e?e.style.display="none":e.parentNode.removeChild(e),t}for(var a=r.view,u=r.viewFrom,c=0;c<a.length;c++){var h=a[c];if(!h.hidden)if(h.node&&h.node.parentNode==o){for(;l!=h.node;)l=s(l);var d=i&&null!=e&&e<=u&&h.lineNumber;h.changes&&(-1<B(h.changes,"gutter")&&(d=!1),yn(n,h,u,t)),d&&(M(h.lineNumber),h.lineNumber.appendChild(document.createTextNode(st(n.options,u)))),l=h.node.nextSibling}else{var f=(m=u,v=t,y=wn(p=n,g=h),g.text=g.node=y.pre,y.bgClass&&(g.bgClass=y.bgClass),y.textClass&&(g.textClass=y.textClass),Cn(p,g),Sn(p,g,m,v),kn(p,g,v),g.node);o.insertBefore(f,l)}u+=h.size}var p,g,m,v,y;for(;l;)l=s(l)}(e,n.updateLineNumbers,t.dims),4<d&&(n.lineDiv.style.display=""),n.renderedView=n.view,(f=m)&&f.activeElt&&f.activeElt!=W()&&(f.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(f.activeElt.nodeName)&&f.anchorNode&&D(document.body,f.anchorNode)&&D(document.body,f.focusNode)&&(p=window.getSelection(),(g=document.createRange()).setEnd(f.anchorNode,f.anchorOffset),g.collapse(!1),p.removeAllRanges(),p.addRange(g),p.extend(f.focusNode,f.focusOffset))),M(n.cursorDiv),M(n.selectionDiv),n.gutters.style.height=n.sizer.style.minHeight=0,h&&(n.lastWrapHeight=t.wrapperHeight,n.lastWrapWidth=t.wrapperWidth,si(e,400)),!(n.updateLineNumbers=null)}function hi(e,t){for(var n=t.viewport,r=!0;;r=!1){if(r&&e.options.lineWrapping&&t.oldDisplayWidth!=Fn(e))r&&(t.visible=Hr(e.display,e.doc,n));else if(n&&null!=n.top&&(n={top:Math.min(e.doc.height+Dn(e.display)-Pn(e),n.top)}),t.visible=Hr(e.display,e.doc,n),t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)break;if(!ci(e,t))break;Dr(e);var i=Vr(e);xr(e),Xr(e,i),pi(e,i),t.force=!1}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function di(e,t){var n,r=new ui(e,t);ci(e,r)&&(Dr(e),hi(e,r),n=Vr(e),xr(e),Xr(e,n),pi(e,n),r.finish())}function fi(e){var t=e.gutters.offsetWidth;e.sizer.style.marginLeft=t+"px"}function pi(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+Hn(e)+"px"}function gi(e){var t=e.display,n=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var r=hr(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=r+"px",l=0;l<n.length;l++)if(!n[l].hidden){e.options.fixedGutter&&(n[l].gutter&&(n[l].gutter.style.left=o),n[l].gutterBackground&&(n[l].gutterBackground.style.left=o));var s=n[l].alignable;if(s)for(var a=0;a<s.length;a++)s[a].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=r+i+"px")}}function mi(e){if(e.options.lineNumbers){var t=e.doc,n=st(e.options,t.first+t.size-1),r=e.display;if(n.length!=r.lineNumChars){var i=r.measure.appendChild(O("div",[O("div",n)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,l=i.offsetWidth-o;return r.lineGutter.style.width="",r.lineNumInnerWidth=Math.max(o,r.lineGutter.offsetWidth-l)+1,r.lineNumWidth=r.lineNumInnerWidth+l,r.lineNumChars=r.lineNumInnerWidth?n.length:-1,r.lineGutter.style.width=r.lineNumWidth+"px",fi(e.display),1}}}function vi(e,t){for(var n=[],r=!1,i=0;i<e.length;i++){var o=e[i],l=null;if("string"!=typeof o&&(l=o.style,o=o.className),"CodeMirror-linenumbers"==o){if(!t)continue;r=!0}n.push({className:o,style:l})}return t&&!r&&n.push({className:"CodeMirror-linenumbers",style:null}),n}function yi(e){var t=e.gutters,n=e.gutterSpecs;M(t),e.lineGutter=null;for(var r=0;r<n.length;++r){var i=n[r],o=i.className,l=i.style,s=t.appendChild(O("div",null,"CodeMirror-gutter "+o));l&&(s.style.cssText=l),"CodeMirror-linenumbers"==o&&((e.lineGutter=s).style.width=(e.lineNumWidth||1)+"px")}t.style.display=n.length?"":"none",fi(e)}function bi(e){yi(e.display),mr(e),gi(e)}function wi(e,t,n,r){var i=this;this.input=n,i.scrollbarFiller=O("div",null,"CodeMirror-scrollbar-filler"),i.scrollbarFiller.setAttribute("cm-not-content","true"),i.gutterFiller=O("div",null,"CodeMirror-gutter-filler"),i.gutterFiller.setAttribute("cm-not-content","true"),i.lineDiv=A("div",null,"CodeMirror-code"),i.selectionDiv=O("div",null,null,"position: relative; z-index: 1"),i.cursorDiv=O("div",null,"CodeMirror-cursors"),i.measure=O("div",null,"CodeMirror-measure"),i.lineMeasure=O("div",null,"CodeMirror-measure"),i.lineSpace=A("div",[i.measure,i.lineMeasure,i.selectionDiv,i.cursorDiv,i.lineDiv],null,"position: relative; outline: none");var o=A("div",[i.lineSpace],"CodeMirror-lines");i.mover=O("div",[o],null,"position: relative"),i.sizer=O("div",[i.mover],"CodeMirror-sizer"),i.sizerWidth=null,i.heightForcer=O("div",null,null,"position: absolute; height: "+G+"px; width: 1px;"),i.gutters=O("div",null,"CodeMirror-gutters"),i.lineGutter=null,i.scroller=O("div",[i.sizer,i.heightForcer,i.gutters],"CodeMirror-scroll"),i.scroller.setAttribute("tabIndex","-1"),i.wrapper=O("div",[i.scrollbarFiller,i.gutterFiller,i.scroller],"CodeMirror"),x&&C<8&&(i.gutters.style.zIndex=-1,i.scroller.style.paddingRight=0),b||g&&h||(i.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(i.wrapper):e(i.wrapper)),i.viewFrom=i.viewTo=t.first,i.reportedViewFrom=i.reportedViewTo=t.first,i.view=[],i.renderedView=null,i.externalMeasured=null,i.viewOffset=0,i.lastWrapHeight=i.lastWrapWidth=0,i.updateLineNumbers=null,i.nativeBarWidth=i.barHeight=i.barWidth=0,i.scrollbarsClipped=!1,i.lineNumWidth=i.lineNumInnerWidth=i.lineNumChars=null,i.alignWidgets=!1,i.cachedCharWidth=i.cachedTextHeight=i.cachedPaddingH=null,i.maxLine=null,i.maxLineLength=0,i.maxLineChanged=!1,i.wheelDX=i.wheelDY=i.wheelStartX=i.wheelStartY=null,i.shift=!1,i.selForContextMenu=null,i.activeTouch=null,i.gutterSpecs=vi(r.gutters,r.lineNumbers),yi(i),n.init(i)}ui.prototype.signal=function(e,t){Te(e,t)&&this.events.push(arguments)},ui.prototype.finish=function(){for(var e=0;e<this.events.length;e++)Se.apply(null,this.events[e])};var xi=0,Ci=null;function Si(e){var t=e.wheelDeltaX,n=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==n&&e.detail&&e.axis==e.VERTICAL_AXIS?n=e.detail:null==n&&(n=e.wheelDelta),{x:t,y:n}}function Li(e){var t=Si(e);return t.x*=Ci,t.y*=Ci,t}function ki(e,t){var n,r,i,o=Si(t),l=o.x,s=o.y,a=e.display,u=a.scroller,c=u.scrollWidth>u.clientWidth,h=u.scrollHeight>u.clientHeight;if(l&&c||s&&h){if(s&&w&&b)e:for(var d=t.target,f=a.view;d!=u;d=d.parentNode)for(var p=0;p<f.length;p++)if(f[p].node==d){e.display.currentWheelTarget=d;break e}if(l&&!g&&!m&&null!=Ci)return s&&h&&Br(e,Math.max(0,u.scrollTop+s*Ci)),Ur(e,Math.max(0,u.scrollLeft+l*Ci)),(!s||s&&h)&&Ne(t),void(a.wheelStartX=null);s&&null!=Ci&&(n=s*Ci,i=(r=e.doc.scrollTop)+a.wrapper.clientHeight,n<0?r=Math.max(0,r+n-50):i=Math.min(e.doc.height,i+n+50),di(e,{top:r,bottom:i})),xi<20&&(null==a.wheelStartX?(a.wheelStartX=u.scrollLeft,a.wheelStartY=u.scrollTop,a.wheelDX=l,a.wheelDY=s,setTimeout(function(){var e,t,n;null!=a.wheelStartX&&(e=u.scrollLeft-a.wheelStartX,n=(t=u.scrollTop-a.wheelStartY)&&a.wheelDY&&t/a.wheelDY||e&&a.wheelDX&&e/a.wheelDX,a.wheelStartX=a.wheelStartY=null,n&&(Ci=(Ci*xi+n)/(xi+1),++xi))},200)):(a.wheelDX+=l,a.wheelDY+=s))}}x?Ci=-.53:g?Ci=15:l?Ci=-.7:c&&(Ci=-1/3);var Ti=function(e,t){this.ranges=e,this.primIndex=t};Ti.prototype.primary=function(){return this.ranges[this.primIndex]},Ti.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var n=this.ranges[t],r=e.ranges[t];if(!ct(n.anchor,r.anchor)||!ct(n.head,r.head))return!1}return!0},Ti.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new Mi(ht(this.ranges[t].anchor),ht(this.ranges[t].head));return new Ti(e,this.primIndex)},Ti.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},Ti.prototype.contains=function(e,t){t=t||e;for(var n=0;n<this.ranges.length;n++){var r=this.ranges[n];if(0<=ut(t,r.from())&&ut(e,r.to())<=0)return n}return-1};var Mi=function(e,t){this.anchor=e,this.head=t};function Ni(e,t,n){var r=e&&e.options.selectionsMayTouch,i=t[n];t.sort(function(e,t){return ut(e.from(),t.from())}),n=B(t,i);for(var o=1;o<t.length;o++){var l,s,a,u=t[o],c=t[o-1],h=ut(c.to(),u.from());(r&&!u.empty()?0<h:0<=h)&&(l=ft(c.from(),u.from()),s=dt(c.to(),u.to()),a=c.empty()?u.from()==u.head:c.from()==c.head,o<=n&&--n,t.splice(--o,2,new Mi(a?s:l,a?l:s)))}return new Ti(t,n)}function Ai(e,t){return new Ti([new Mi(e,t||e)],0)}function Oi(e){return e.text?at(e.from.line+e.text.length-1,$(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function Di(e,t){if(ut(e,t.from)<0)return e;if(ut(e,t.to)<=0)return Oi(t);var n=e.line+t.text.length-(t.to.line-t.from.line)-1,r=e.ch;return e.line==t.to.line&&(r+=Oi(t).ch-t.to.ch),at(n,r)}function Wi(e,t){for(var n=[],r=0;r<e.sel.ranges.length;r++){var i=e.sel.ranges[r];n.push(new Mi(Di(i.anchor,t),Di(i.head,t)))}return Ni(e.cm,n,e.sel.primIndex)}function Hi(e,t,n){return e.line==t.line?at(n.line,e.ch-t.ch+n.ch):at(n.line+(e.line-t.line),e.ch)}function Fi(e){e.doc.mode=Ye(e.options,e.doc.modeOption),Pi(e)}function Pi(e){e.doc.iter(function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)}),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,si(e,100),e.state.modeGen++,e.curOp&&mr(e)}function Ei(e,t){return 0==t.from.ch&&0==t.to.ch&&""==$(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function Ii(e,r,t,i){function o(e){return t?t[e]:null}function n(e,t,n){!function(e,t,n,r){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),Et(e),It(e,n);var i=r?r(e):1;i!=e.height&&rt(e,i)}(e,t,n,i),mn(e,"change",e,r)}function l(e,t){for(var n=[],r=e;r<t;++r)n.push(new en(d[r],o(r),i));return n}var s,a,u,c=r.from,h=r.to,d=r.text,f=et(e,c.line),p=et(e,h.line),g=$(d),m=o(d.length-1),v=h.line-c.line;r.full?(e.insert(0,l(0,d.length)),e.remove(d.length,e.size-d.length)):Ei(e,r)?(s=l(0,d.length-1),n(p,p.text,m),v&&e.remove(c.line,v),s.length&&e.insert(c.line,s)):f==p?1==d.length?n(f,f.text.slice(0,c.ch)+g+f.text.slice(h.ch),m):((a=l(1,d.length-1)).push(new en(g+f.text.slice(h.ch),m,i)),n(f,f.text.slice(0,c.ch)+d[0],o(0)),e.insert(c.line+1,a)):1==d.length?(n(f,f.text.slice(0,c.ch)+d[0]+p.text.slice(h.ch),o(0)),e.remove(c.line+1,v)):(n(f,f.text.slice(0,c.ch)+d[0],o(0)),n(p,g+p.text.slice(h.ch),m),u=l(1,d.length-1),1<v&&e.remove(c.line+1,v-1),e.insert(c.line+1,u)),mn(e,"change",e,r)}function Ri(e,s,a){!function e(t,n,r){if(t.linked)for(var i=0;i<t.linked.length;++i){var o,l=t.linked[i];l.doc!=n&&(o=r&&l.sharedHist,a&&!o||(s(l.doc,o),e(l.doc,t,o)))}}(e,null,!0)}function zi(e,t){if(t.cm)throw new Error("This document is already in use.");fr((e.doc=t).cm=e),Fi(e),Bi(e),e.options.lineWrapping||Jt(e),e.options.mode=t.modeOption,mr(e)}function Bi(e){("rtl"==e.doc.direction?H:T)(e.display.lineDiv,"CodeMirror-rtl")}function Gi(e){this.done=[],this.undone=[],this.undoDepth=1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e||1}function Ui(e,t){var n={from:ht(t.from),to:Oi(t),text:tt(e,t.from,t.to)};return Yi(e,n,t.from.line,t.to.line+1),Ri(e,function(e){return Yi(e,n,t.from.line,t.to.line+1)},!0),n}function Vi(e){for(;e.length;){if(!$(e).ranges)break;e.pop()}}function Ki(e,t,n,r){var i=e.history;i.undone.length=0;var o,l,s,a=+new Date;if((i.lastOp==r||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&i.lastModTime>a-(e.cm?e.cm.options.historyEventDelay:500)||"*"==t.origin.charAt(0)))&&(o=(s=i).lastOp==r?(Vi(s.done),$(s.done)):s.done.length&&!$(s.done).ranges?$(s.done):1<s.done.length&&!s.done[s.done.length-2].ranges?(s.done.pop(),$(s.done)):void 0))l=$(o.changes),0==ut(t.from,t.to)&&0==ut(t.from,l.to)?l.to=Oi(t):o.changes.push(Ui(e,t));else{var u=$(i.done);for(u&&u.ranges||Xi(e.sel,i.done),o={changes:[Ui(e,t)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(n),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=a,i.lastOp=i.lastSelOp=r,i.lastOrigin=i.lastSelOrigin=t.origin,l||Se(e,"historyAdded")}function ji(e,t,n,r){var i,o,l,s,a,u=e.history,c=r&&r.origin;n==u.lastSelOp||c&&u.lastSelOrigin==c&&(u.lastModTime==u.lastSelTime&&u.lastOrigin==c||(i=e,o=c,l=$(u.done),s=t,"*"==(a=o.charAt(0))||"+"==a&&l.ranges.length==s.ranges.length&&l.somethingSelected()==s.somethingSelected()&&new Date-i.history.lastSelTime<=(i.cm?i.cm.options.historyEventDelay:500)))?u.done[u.done.length-1]=t:Xi(t,u.done),u.lastSelTime=+new Date,u.lastSelOrigin=c,u.lastSelOp=n,r&&!1!==r.clearRedo&&Vi(u.undone)}function Xi(e,t){var n=$(t);n&&n.ranges&&n.equals(e)||t.push(e)}function Yi(t,n,e,r){var i=n["spans_"+t.id],o=0;t.iter(Math.max(t.first,e),Math.min(t.first+t.size,r),function(e){e.markedSpans&&((i=i||(n["spans_"+t.id]={}))[o]=e.markedSpans),++o})}function _i(e){if(!e)return null;for(var t,n=0;n<e.length;++n)e[n].marker.explicitlyCleared?t=t||e.slice(0,n):t&&t.push(e[n]);return t?t.length?t:null:e}function $i(e,t){var n=function(e,t){var n=t["spans_"+e.id];if(!n)return null;for(var r=[],i=0;i<t.text.length;++i)r.push(_i(n[i]));return r}(e,t),r=Ft(e,t);if(!n)return r;if(!r)return n;for(var i=0;i<n.length;++i){var o=n[i],l=r[i];if(o&&l)e:for(var s=0;s<l.length;++s){for(var a=l[s],u=0;u<o.length;++u)if(o[u].marker==a.marker)continue e;o.push(a)}else l&&(n[i]=l)}return n}function qi(e,t,n){for(var r=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)r.push(n?Ti.prototype.deepCopy.call(o):o);else{var l=o.changes,s=[];r.push({changes:s});for(var a=0;a<l.length;++a){var u=l[a],c=void 0;if(s.push({from:u.from,to:u.to,text:u.text}),t)for(var h in u)(c=h.match(/^spans_(\d+)$/))&&-1<B(t,Number(c[1]))&&($(s)[h]=u[h],delete u[h])}}}return r}function Zi(e,t,n,r){if(r){var i,o=e.anchor;return n&&((i=ut(t,o)<0)!=ut(n,o)<0?(o=t,t=n):i!=ut(t,n)<0&&(t=n)),new Mi(o,t)}return new Mi(n||t,t)}function Qi(e,t,n,r,i){null==i&&(i=e.cm&&(e.cm.display.shift||e.extend)),ro(e,new Ti([Zi(e.sel.primary(),t,n,i)],0),r)}function Ji(e,t,n){for(var r=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)r[o]=Zi(e.sel.ranges[o],t[o],null,i);ro(e,Ni(e.cm,r,e.sel.primIndex),n)}function eo(e,t,n,r){var i=e.sel.ranges.slice(0);i[t]=n,ro(e,Ni(e.cm,i,e.sel.primIndex),r)}function to(e,t,n,r){ro(e,Ai(t,n),r)}function no(e,t,n){var r=e.history.done,i=$(r);i&&i.ranges?io(e,r[r.length-1]=t,n):ro(e,t,n)}function ro(e,t,n){io(e,t,n),ji(e,e.sel,e.cm?e.cm.curOp.id:NaN,n)}function io(e,t,n){var r,i,o,l;(Te(e,"beforeSelectionChange")||e.cm&&Te(e.cm,"beforeSelectionChange"))&&(r=e,o=n,l={ranges:(i=t).ranges,update:function(e){this.ranges=[];for(var t=0;t<e.length;t++)this.ranges[t]=new Mi(gt(r,e[t].anchor),gt(r,e[t].head))},origin:o&&o.origin},Se(r,"beforeSelectionChange",r,l),r.cm&&Se(r.cm,"beforeSelectionChange",r.cm,l),t=l.ranges!=i.ranges?Ni(r.cm,l.ranges,l.ranges.length-1):i);var s=n&&n.bias||(ut(t.primary().head,e.sel.primary().head)<0?-1:1);oo(e,so(e,t,s,!0)),n&&!1===n.scroll||!e.cm||Er(e.cm)}function oo(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,ke(e.cm)),mn(e,"cursorActivity",e))}function lo(e){oo(e,so(e,e.sel,null,!1))}function so(e,t,n,r){for(var i,o=0;o<t.ranges.length;o++){var l=t.ranges[o],s=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],a=uo(e,l.anchor,s&&s.anchor,n,r),u=uo(e,l.head,s&&s.head,n,r);!i&&a==l.anchor&&u==l.head||((i=i||t.ranges.slice(0,o))[o]=new Mi(a,u))}return i?Ni(e.cm,i,t.primIndex):t}function ao(e,t,n,r,i){var o=et(e,t.line);if(o.markedSpans)for(var l=0;l<o.markedSpans.length;++l){var s=o.markedSpans[l],a=s.marker,u="selectLeft"in a?!a.selectLeft:a.inclusiveLeft,c="selectRight"in a?!a.selectRight:a.inclusiveRight;if((null==s.from||(u?s.from<=t.ch:s.from<t.ch))&&(null==s.to||(c?s.to>=t.ch:s.to>t.ch))){if(i&&(Se(a,"beforeCursorEnter"),a.explicitlyCleared)){if(o.markedSpans){--l;continue}break}if(!a.atomic)continue;if(n){var h=a.find(r<0?1:-1),d=void 0;if((r<0?c:u)&&(h=co(e,h,-r,h&&h.line==t.line?o:null)),h&&h.line==t.line&&(d=ut(h,n))&&(r<0?d<0:0<d))return ao(e,h,t,r,i)}var f=a.find(r<0?-1:1);return(r<0?u:c)&&(f=co(e,f,r,f.line==t.line?o:null)),f?ao(e,f,t,r,i):null}}return t}function uo(e,t,n,r,i){var o=r||1,l=ao(e,t,n,o,i)||!i&&ao(e,t,n,o,!0)||ao(e,t,n,-o,i)||!i&&ao(e,t,n,-o,!0);return l||(e.cantEdit=!0,at(e.first,0))}function co(e,t,n,r){return n<0&&0==t.ch?t.line>e.first?gt(e,at(t.line-1)):null:0<n&&t.ch==(r||et(e,t.line)).text.length?t.line<e.first+e.size-1?at(t.line+1,0):null:new at(t.line,t.ch+n)}function ho(e){e.setSelection(at(e.firstLine(),0),at(e.lastLine()),V)}function fo(i,e,t){var o={canceled:!1,from:e.from,to:e.to,text:e.text,origin:e.origin,cancel:function(){return o.canceled=!0}};return t&&(o.update=function(e,t,n,r){e&&(o.from=gt(i,e)),t&&(o.to=gt(i,t)),n&&(o.text=n),void 0!==r&&(o.origin=r)}),Se(i,"beforeChange",i,o),i.cm&&Se(i.cm,"beforeChange",i.cm,o),o.canceled?(i.cm&&(i.cm.curOp.updateInput=2),null):{from:o.from,to:o.to,text:o.text,origin:o.origin}}function po(e,t,n){if(e.cm){if(!e.cm.curOp)return ii(e.cm,po)(e,t,n);if(e.cm.state.suppressEdits)return}if(!(Te(e,"beforeChange")||e.cm&&Te(e.cm,"beforeChange"))||(t=fo(e,t,!0))){var r=At&&!n&&function(e,t,n){var r=null;if(e.iter(t.line,n.line+1,function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var n=e.markedSpans[t].marker;!n.readOnly||r&&-1!=B(r,n)||(r=r||[]).push(n)}}),!r)return null;for(var i=[{from:t,to:n}],o=0;o<r.length;++o)for(var l=r[o],s=l.find(0),a=0;a<i.length;++a){var u,c,h,d=i[a];ut(d.to,s.from)<0||0<ut(d.from,s.to)||(u=[a,1],c=ut(d.from,s.from),h=ut(d.to,s.to),(c<0||!l.inclusiveLeft&&!c)&&u.push({from:d.from,to:s.from}),(0<h||!l.inclusiveRight&&!h)&&u.push({from:s.to,to:d.to}),i.splice.apply(i,u),a+=u.length-3)}return i}(e,t.from,t.to);if(r)for(var i=r.length-1;0<=i;--i)go(e,{from:r[i].from,to:r[i].to,text:i?[""]:t.text,origin:t.origin});else go(e,t)}}function go(e,n){var t,r;1==n.text.length&&""==n.text[0]&&0==ut(n.from,n.to)||(t=Wi(e,n),Ki(e,n,t,e.cm?e.cm.curOp.id:NaN),yo(e,n,t,Ft(e,n)),r=[],Ri(e,function(e,t){t||-1!=B(r,e.history)||(Co(e.history,n),r.push(e.history)),yo(e,n,null,Ft(e,n))}))}function mo(i,o,e){var t=i.cm&&i.cm.state.suppressEdits;if(!t||e){for(var l,n=i.history,r=i.sel,s="undo"==o?n.done:n.undone,a="undo"==o?n.undone:n.done,u=0;u<s.length&&(l=s[u],e?!l.ranges||l.equals(i.sel):l.ranges);u++);if(u!=s.length){for(n.lastOrigin=n.lastSelOrigin=null;;){if(!(l=s.pop()).ranges){if(t)return void s.push(l);break}if(Xi(l,a),e&&!l.equals(i.sel))return void ro(i,l,{clearRedo:!1});r=l}var c=[];Xi(r,a),a.push({changes:c,generation:n.generation}),n.generation=l.generation||++n.maxGeneration;for(var h=Te(i,"beforeChange")||i.cm&&Te(i.cm,"beforeChange"),d=function(e){var n=l.changes[e];if(n.origin=o,h&&!fo(i,n,!1))return s.length=0,{};c.push(Ui(i,n));var t=e?Wi(i,n):$(s);yo(i,n,t,$i(i,n)),!e&&i.cm&&i.cm.scrollIntoView({from:n.from,to:Oi(n)});var r=[];Ri(i,function(e,t){t||-1!=B(r,e.history)||(Co(e.history,n),r.push(e.history)),yo(e,n,null,$i(e,n))})},f=l.changes.length-1;0<=f;--f){var p=d(f);if(p)return p.v}}}}function vo(e,t){if(0!=t&&(e.first+=t,e.sel=new Ti(q(e.sel.ranges,function(e){return new Mi(at(e.anchor.line+t,e.anchor.ch),at(e.head.line+t,e.head.ch))}),e.sel.primIndex),e.cm)){mr(e.cm,e.first,e.first-t,t);for(var n=e.cm.display,r=n.viewFrom;r<n.viewTo;r++)vr(e.cm,r,"gutter")}}function yo(e,t,n,r){if(e.cm&&!e.cm.curOp)return ii(e.cm,yo)(e,t,n,r);var i,o;t.to.line<e.first?vo(e,t.text.length-1-(t.to.line-t.from.line)):t.from.line>e.lastLine()||(t.from.line<e.first&&(vo(e,i=t.text.length-1-(e.first-t.from.line)),t={from:at(e.first,0),to:at(t.to.line+i,t.to.ch),text:[$(t.text)],origin:t.origin}),o=e.lastLine(),t.to.line>o&&(t={from:t.from,to:at(o,et(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=tt(e,t.from,t.to),n=n||Wi(e,t),e.cm?function(e,t,n){var r=e.doc,i=e.display,o=t.from,l=t.to,s=!1,a=o.line;e.options.lineWrapping||(a=it(Xt(et(r,o.line))),r.iter(a,l.line+1,function(e){if(e==i.maxLine)return s=!0}));-1<r.sel.contains(t.from,t.to)&&ke(e);Ii(r,t,n,dr(e)),e.options.lineWrapping||(r.iter(a,o.line+t.text.length,function(e){var t=Qt(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,s=!1)}),s&&(e.curOp.updateMaxLine=!0));(function(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var n=e.first,r=t-1;n<r;r--){var i=et(e,r).stateAfter;if(i&&(!(i instanceof vt)||r+i.lookAhead<t)){n=r+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,n)}})(r,o.line),si(e,400);var u=t.text.length-(l.line-o.line)-1;t.full?mr(e):o.line!=l.line||1!=t.text.length||Ei(e.doc,t)?mr(e,o.line,l.line+1,u):vr(e,o.line,"text");var c=Te(e,"changes"),h=Te(e,"change");{var d;(h||c)&&(d={from:o,to:l,text:t.text,removed:t.removed,origin:t.origin},h&&mn(e,"change",e,d),c&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(d))}e.display.selForContextMenu=null}(e.cm,t,r):Ii(e,t,r),io(e,n,V),e.cantEdit&&uo(e,at(e.firstLine(),0))&&(e.cantEdit=!1))}function bo(e,t,n,r,i){var o;ut(r=r||n,n)<0&&(n=(o=[r,n])[0],r=o[1]),"string"==typeof t&&(t=e.splitLines(t)),po(e,{from:n,to:r,text:t,origin:i})}function wo(e,t,n,r){n<e.line?e.line+=r:t<e.line&&(e.line=t,e.ch=0)}function xo(e,t,n,r){for(var i=0;i<e.length;++i){var o=e[i],l=!0;if(o.ranges){o.copied||((o=e[i]=o.deepCopy()).copied=!0);for(var s=0;s<o.ranges.length;s++)wo(o.ranges[s].anchor,t,n,r),wo(o.ranges[s].head,t,n,r)}else{for(var a=0;a<o.changes.length;++a){var u=o.changes[a];if(n<u.from.line)u.from=at(u.from.line+r,u.from.ch),u.to=at(u.to.line+r,u.to.ch);else if(t<=u.to.line){l=!1;break}}l||(e.splice(0,i+1),i=0)}}}function Co(e,t){var n=t.from.line,r=t.to.line,i=t.text.length-(r-n)-1;xo(e.done,n,r,i),xo(e.undone,n,r,i)}function So(e,t,n,r){var i=t,o=t;return"number"==typeof t?o=et(e,pt(e,t)):i=it(t),null==i?null:(r(o,i)&&e.cm&&vr(e.cm,i,n),o)}function Lo(e){this.lines=e,this.parent=null;for(var t=0,n=0;n<e.length;++n)e[n].parent=this,t+=e[n].height;this.height=t}function ko(e){this.children=e;for(var t=0,n=0,r=0;r<e.length;++r){var i=e[r];t+=i.chunkSize(),n+=i.height,i.parent=this}this.size=t,this.height=n,this.parent=null}Mi.prototype.from=function(){return ft(this.anchor,this.head)},Mi.prototype.to=function(){return dt(this.anchor,this.head)},Mi.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},Lo.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var n,r=e,i=e+t;r<i;++r){var o=this.lines[r];this.height-=o.height,(n=o).parent=null,Et(n),mn(o,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,n){this.height+=n,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var r=0;r<t.length;++r)t[r].parent=this},iterN:function(e,t,n){for(var r=e+t;e<r;++e)if(n(this.lines[e]))return!0}},ko.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var n,r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<o){var l=Math.min(t,o-e),s=i.height;if(i.removeInner(e,l),this.height-=s-i.height,o==l&&(this.children.splice(r--,1),i.parent=null),0==(t-=l))break;e=0}else e-=o}this.size-t<25&&(1<this.children.length||!(this.children[0]instanceof Lo))&&(n=[],this.collapse(n),this.children=[new Lo(n)],this.children[0].parent=this)},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,n){this.size+=t.length,this.height+=n;for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<=o){if(i.insertInner(e,t,n),i.lines&&50<i.lines.length){for(var l=i.lines.length%25+25,s=l;s<i.lines.length;){var a=new Lo(i.lines.slice(s,s+=25));i.height-=a.height,this.children.splice(++r,0,a),a.parent=this}i.lines=i.lines.slice(0,l),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t,n,r=new ko(e.children.splice(e.children.length-5,5));e.parent?(e.size-=r.size,e.height-=r.height,t=B(e.parent.children,e),e.parent.children.splice(t+1,0,r)):(((n=new ko(e.children)).parent=e).children=[n,r],e=n),r.parent=e.parent}while(10<e.children.length);e.parent.maybeSpill()}},iterN:function(e,t,n){for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<o){var l=Math.min(t,o-e);if(i.iterN(e,l,n))return!0;if(0==(t-=l))break;e=0}else e-=o}}};function To(e,t,n){if(n)for(var r in n)n.hasOwnProperty(r)&&(this[r]=n[r]);this.doc=e,this.node=t}function Mo(e,t,n){Zt(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&Pr(e,n)}To.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,n=this.line,r=it(n);if(null!=r&&t){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(n.widgets=null);var o=Nn(this);rt(n,Math.max(0,n.height-o)),e&&(ri(e,function(){Mo(e,n,-o),vr(e,r,"widget")}),mn(e,"lineWidgetCleared",e,this,r))}},To.prototype.changed=function(){var e=this,t=this.height,n=this.doc.cm,r=this.line;this.height=null;var i=Nn(this)-t;i&&($t(this.doc,r)||rt(r,r.height+i),n&&ri(n,function(){n.curOp.forceUpdate=!0,Mo(n,r,i),mn(n,"lineWidgetChanged",n,e,it(r))}))},Me(To);var No=0,Ao=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++No};function Oo(t,r,i,e,n){if(e&&e.shared)return function(e,n,r,i,o){(i=I(i)).shared=!1;var l=[Oo(e,n,r,i,o)],s=l[0],a=i.widgetNode;return Ri(e,function(e){a&&(i.widgetNode=a.cloneNode(!0)),l.push(Oo(e,gt(e,n),gt(e,r),i,o));for(var t=0;t<e.linked.length;++t)if(e.linked[t].isParent)return;s=$(l)}),new Do(l,s)}(t,r,i,e,n);if(t.cm&&!t.cm.curOp)return ii(t.cm,Oo)(t,r,i,e,n);var o=new Ao(t,n),l=ut(r,i);if(e&&I(e,o,!1),0<l||0==l&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=A("span",[o.replacedWith],"CodeMirror-widget"),e.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),e.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(jt(t,r.line,r,i,o)||r.line!=i.line&&jt(t,i.line,r,i,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");Ot=!0}o.addToHistory&&Ki(t,{from:r,to:i,origin:"markText"},t.sel,NaN);var s,a=r.line,u=t.cm;if(t.iter(a,i.line+1,function(e){var t,n;u&&o.collapsed&&!u.options.lineWrapping&&Xt(e)==u.display.maxLine&&(s=!0),o.collapsed&&a!=r.line&&rt(e,0),t=e,n=new Dt(o,a==r.line?r.ch:null,a==i.line?i.ch:null),t.markedSpans=t.markedSpans?t.markedSpans.concat([n]):[n],n.marker.attachLine(t),++a}),o.collapsed&&t.iter(r.line,i.line+1,function(e){$t(t,e)&&rt(e,0)}),o.clearOnEnter&&we(o,"beforeCursorEnter",function(){return o.clear()}),o.readOnly&&(At=!0,(t.history.done.length||t.history.undone.length)&&t.clearHistory()),o.collapsed&&(o.id=++No,o.atomic=!0),u){if(s&&(u.curOp.updateMaxLine=!0),o.collapsed)mr(u,r.line,i.line+1);else if(o.className||o.startStyle||o.endStyle||o.css||o.attributes||o.title)for(var c=r.line;c<=i.line;c++)vr(u,c,"text");o.atomic&&lo(u.doc),mn(u,"markerAdded",u,o)}return o}Ao.prototype.clear=function(){if(!this.explicitlyCleared){var e,t=this.doc.cm,n=t&&!t.curOp;n&&Zr(t),!Te(this,"clear")||(e=this.find())&&mn(this,"clear",e.from,e.to);for(var r=null,i=null,o=0;o<this.lines.length;++o){var l=this.lines[o],s=Wt(l.markedSpans,this);t&&!this.collapsed?vr(t,it(l),"text"):t&&(null!=s.to&&(i=it(l)),null!=s.from&&(r=it(l))),l.markedSpans=Ht(l.markedSpans,s),null==s.from&&this.collapsed&&!$t(this.doc,l)&&t&&rt(l,ar(t.display))}if(t&&this.collapsed&&!t.options.lineWrapping)for(var a=0;a<this.lines.length;++a){var u=Xt(this.lines[a]),c=Qt(u);c>t.display.maxLineLength&&(t.display.maxLine=u,t.display.maxLineLength=c,t.display.maxLineChanged=!0)}null!=r&&t&&this.collapsed&&mr(t,r,i+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,t&&lo(t.doc)),t&&mn(t,"markerCleared",t,this,r,i),n&&Qr(t),this.parent&&this.parent.clear()}},Ao.prototype.find=function(e,t){var n,r;null==e&&"bookmark"==this.type&&(e=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],l=Wt(o.markedSpans,this);if(null!=l.from&&(n=at(t?o:it(o),l.from),-1==e))return n;if(null!=l.to&&(r=at(t?o:it(o),l.to),1==e))return r}return n&&{from:n,to:r}},Ao.prototype.changed=function(){var o=this,l=this.find(-1,!0),s=this,a=this.doc.cm;l&&a&&ri(a,function(){var e,t,n=l.line,r=it(l.line),i=Rn(a,r);i&&(jn(i),a.curOp.selectionChanged=a.curOp.forceUpdate=!0),a.curOp.updateMaxLine=!0,$t(s.doc,n)||null==s.height||(e=s.height,s.height=null,(t=Nn(s)-e)&&rt(n,n.height+t)),mn(a,"markerChanged",a,o)})},Ao.prototype.attachLine=function(e){var t;!this.lines.length&&this.doc.cm&&((t=this.doc.cm.curOp).maybeHiddenMarkers&&-1!=B(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)),this.lines.push(e)},Ao.prototype.detachLine=function(e){var t;this.lines.splice(B(this.lines,e),1),!this.lines.length&&this.doc.cm&&((t=this.doc.cm.curOp).maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)},Me(Ao);var Do=function(e,t){this.markers=e,this.primary=t;for(var n=0;n<e.length;++n)e[n].parent=this};function Wo(e){return e.findMarks(at(e.first,0),e.clipPos(at(e.lastLine())),function(e){return e.parent})}function Ho(o){for(var e=function(e){var t=o[e],n=[t.primary.doc];Ri(t.primary.doc,function(e){return n.push(e)});for(var r=0;r<t.markers.length;r++){var i=t.markers[r];-1==B(n,i.doc)&&(i.parent=null,t.markers.splice(r--,1))}},t=0;t<o.length;t++)e(t)}Do.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();mn(this,"clear")}},Do.prototype.find=function(e,t){return this.primary.find(e,t)},Me(Do);var Fo=0,Po=function(e,t,n,r,i){if(!(this instanceof Po))return new Po(e,t,n,r,i);null==n&&(n=0),ko.call(this,[new Lo([new en("",null)])]),this.first=n,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=n;var o=at(n,0);this.sel=Ai(o),this.history=new Gi(null),this.id=++Fo,this.modeOption=t,this.lineSep=r,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),Ii(this,{from:o,to:o,text:e}),ro(this,Ai(o),V)};Po.prototype=Q(ko.prototype,{constructor:Po,iter:function(e,t,n){n?this.iterN(e-this.first,t-e,n):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var n=0,r=0;r<t.length;++r)n+=t[r].height;this.insertInner(e-this.first,t,n)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=nt(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:li(function(e){var t=at(this.first,0),n=this.first+this.size-1;po(this,{from:t,to:at(n,et(this,n).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&Ir(this.cm,0,0),ro(this,Ai(t),V)}),replaceRange:function(e,t,n,r){bo(this,e,t=gt(this,t),n=n?gt(this,n):t,r)},getRange:function(e,t,n){var r=tt(this,gt(this,e),gt(this,t));return!1===n?r:r.join(n||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(lt(this,e))return et(this,e)},getLineNumber:it,getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=et(this,e)),Xt(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return gt(this,e)},getCursor:function(e){var t=this.sel.primary(),n=null==e||"head"==e?t.head:"anchor"==e?t.anchor:"end"==e||"to"==e||!1===e?t.to():t.from();return n},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:li(function(e,t,n){to(this,gt(this,"number"==typeof e?at(e,t||0):e),null,n)}),setSelection:li(function(e,t,n){to(this,gt(this,e),gt(this,t||e),n)}),extendSelection:li(function(e,t,n){Qi(this,gt(this,e),t&&gt(this,t),n)}),extendSelections:li(function(e,t){Ji(this,mt(this,e),t)}),extendSelectionsBy:li(function(e,t){Ji(this,mt(this,q(this.sel.ranges,e)),t)}),setSelections:li(function(e,t,n){if(e.length){for(var r=[],i=0;i<e.length;i++)r[i]=new Mi(gt(this,e[i].anchor),gt(this,e[i].head));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),ro(this,Ni(this.cm,r,t),n)}}),addSelection:li(function(e,t,n){var r=this.sel.ranges.slice(0);r.push(new Mi(gt(this,e),gt(this,t||e))),ro(this,Ni(this.cm,r,r.length-1),n)}),getSelection:function(e){for(var t=this.sel.ranges,n=0;n<t.length;n++)var r=tt(this,t[n].from(),t[n].to()),i=i?i.concat(r):r;return!1===e?i:i.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],n=this.sel.ranges,r=0;r<n.length;r++){var i=tt(this,n[r].from(),n[r].to());!1!==e&&(i=i.join(e||this.lineSeparator())),t[r]=i}return t},replaceSelection:function(e,t,n){for(var r=[],i=0;i<this.sel.ranges.length;i++)r[i]=e;this.replaceSelections(r,t,n||"+input")},replaceSelections:li(function(e,t,n){for(var r=[],i=this.sel,o=0;o<i.ranges.length;o++){var l=i.ranges[o];r[o]={from:l.from(),to:l.to(),text:this.splitLines(e[o]),origin:n}}for(var s=t&&"end"!=t&&function(e,t,n){for(var r=[],i=h=at(e.first,0),o=0;o<t.length;o++){var l,s,a=t[o],u=Hi(a.from,h,i),c=Hi(Oi(a),h,i),h=a.to,i=c;"around"==n?(s=ut((l=e.sel.ranges[o]).head,l.anchor)<0,r[o]=new Mi(s?c:u,s?u:c)):r[o]=new Mi(u,u)}return new Ti(r,e.sel.primIndex)}(this,r,t),a=r.length-1;0<=a;a--)po(this,r[a]);s?no(this,s):this.cm&&Er(this.cm)}),undo:li(function(){mo(this,"undo")}),redo:li(function(){mo(this,"redo")}),undoSelection:li(function(){mo(this,"undo",!0)}),redoSelection:li(function(){mo(this,"redo",!0)}),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,n=0,r=0;r<e.done.length;r++)e.done[r].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++n;return{undo:t,redo:n}},clearHistory:function(){var t=this;this.history=new Gi(this.history.maxGeneration),Ri(this,function(e){return e.history=t.history},!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:qi(this.history.done),undone:qi(this.history.undone)}},setHistory:function(e){var t=this.history=new Gi(this.history.maxGeneration);t.done=qi(e.done.slice(0),null,!0),t.undone=qi(e.undone.slice(0),null,!0)},setGutterMarker:li(function(e,n,r){return So(this,e,"gutter",function(e){var t=e.gutterMarkers||(e.gutterMarkers={});return!(t[n]=r)&&ne(t)&&(e.gutterMarkers=null),1})}),clearGutter:li(function(t){var n=this;this.iter(function(e){e.gutterMarkers&&e.gutterMarkers[t]&&So(n,e,"gutter",function(){return e.gutterMarkers[t]=null,ne(e.gutterMarkers)&&(e.gutterMarkers=null),1})})}),lineInfo:function(e){var t;if("number"==typeof e){if(!lt(this,e))return null;if(!(e=et(this,t=e)))return null}else if(null==(t=it(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:li(function(e,n,r){return So(this,e,"gutter"==n?"gutter":"class",function(e){var t="text"==n?"textClass":"background"==n?"bgClass":"gutter"==n?"gutterClass":"wrapClass";if(e[t]){if(L(r).test(e[t]))return;e[t]+=" "+r}else e[t]=r;return 1})}),removeLineClass:li(function(e,o,l){return So(this,e,"gutter"==o?"gutter":"class",function(e){var t="text"==o?"textClass":"background"==o?"bgClass":"gutter"==o?"gutterClass":"wrapClass",n=e[t];if(n){if(null==l)e[t]=null;else{var r=n.match(L(l));if(!r)return;var i=r.index+r[0].length;e[t]=n.slice(0,r.index)+(r.index&&i!=n.length?" ":"")+n.slice(i)||null}return 1}})}),addLineWidget:li(function(e,t,n){return i=e,o=new To(r=this,t,n),(l=r.cm)&&o.noHScroll&&(l.display.alignWidgets=!0),So(r,i,"widget",function(e){var t,n=e.widgets||(e.widgets=[]);return null==o.insertAt?n.push(o):n.splice(Math.min(n.length-1,Math.max(0,o.insertAt)),0,o),o.line=e,l&&!$t(r,e)&&(t=Zt(e)<r.scrollTop,rt(e,e.height+Nn(o)),t&&Pr(l,o.height),l.curOp.forceUpdate=!0),1}),l&&mn(l,"lineWidgetAdded",l,o,"number"==typeof i?i:it(i)),o;var r,i,o,l}),removeLineWidget:function(e){e.clear()},markText:function(e,t,n){return Oo(this,gt(this,e),gt(this,t),n,n&&n.type||"range")},setBookmark:function(e,t){var n={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return Oo(this,e=gt(this,e),e,n,"bookmark")},findMarksAt:function(e){var t=[],n=et(this,(e=gt(this,e)).line).markedSpans;if(n)for(var r=0;r<n.length;++r){var i=n[r];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(i,o,l){i=gt(this,i),o=gt(this,o);var s=[],a=i.line;return this.iter(i.line,o.line+1,function(e){var t=e.markedSpans;if(t)for(var n=0;n<t.length;n++){var r=t[n];null!=r.to&&a==i.line&&i.ch>=r.to||null==r.from&&a!=i.line||null!=r.from&&a==o.line&&r.from>=o.ch||l&&!l(r.marker)||s.push(r.marker.parent||r.marker)}++a}),s},getAllMarks:function(){var r=[];return this.iter(function(e){var t=e.markedSpans;if(t)for(var n=0;n<t.length;++n)null!=t[n].from&&r.push(t[n].marker)}),r},posFromIndex:function(n){var r,i=this.first,o=this.lineSeparator().length;return this.iter(function(e){var t=e.text.length+o;if(n<t)return r=n,!0;n-=t,++i}),gt(this,at(i,r))},indexFromPos:function(e){var t=(e=gt(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var n=this.lineSeparator().length;return this.iter(this.first,e.line,function(e){t+=e.text.length+n}),t},copy:function(e){var t=new Po(nt(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e=e||{};var t=this.first,n=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<n&&(n=e.to);var r=new Po(nt(this,t,n),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(r.history=this.history),(this.linked||(this.linked=[])).push({doc:r,sharedHist:e.sharedHist}),r.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],function(e,t){for(var n=0;n<t.length;n++){var r,i=t[n],o=i.find(),l=e.clipPos(o.from),s=e.clipPos(o.to);ut(l,s)&&(r=Oo(e,l,s,i.primary,i.primary.type),i.markers.push(r),r.parent=i)}}(r,Wo(this)),r},unlinkDoc:function(e){if(e instanceof Wl&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t){if(this.linked[t].doc==e){this.linked.splice(t,1),e.unlinkDoc(this),Ho(Wo(this));break}}var n;e.history==this.history&&(n=[e.id],Ri(e,function(e){return n.push(e.id)},!0),e.history=new Gi(null),e.history.done=qi(this.history.done,n),e.history.undone=qi(this.history.undone,n))},iterLinkedDocs:function(e){Ri(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):Be(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:li(function(e){var t;"rtl"!=e&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter(function(e){return e.order=null}),this.cm&&ri(t=this.cm,function(){Bi(t),mr(t)}))})}),Po.prototype.eachLine=Po.prototype.iter;var Eo=0;function Io(e){var r=this;if(Ro(r),!Le(r,e)&&!An(r.display,e)){Ne(e),x&&(Eo=+new Date);var t=pr(r,e,!0),n=e.dataTransfer.files;if(t&&!r.isReadOnly())if(n&&n.length&&window.FileReader&&window.File)for(var i=n.length,o=Array(i),l=0,s=function(){++l==i&&ii(r,function(){var e={from:t=gt(r.doc,t),to:t,text:r.doc.splitLines(o.filter(function(e){return null!=e}).join(r.doc.lineSeparator())),origin:"paste"};po(r.doc,e),no(r.doc,Ai(gt(r.doc,t),gt(r.doc,Oi(e))))})()},a=function(e,t){var n;r.options.allowDropFileTypes&&-1==B(r.options.allowDropFileTypes,e.type)?s():((n=new FileReader).onerror=s,n.onload=function(){var e=n.result;/[\x00-\x08\x0e-\x1f]{2}/.test(e)||(o[t]=e),s()},n.readAsText(e))},u=0;u<n.length;u++)a(n[u],u);else{if(r.state.draggingText&&-1<r.doc.sel.contains(t))return r.state.draggingText(e),void setTimeout(function(){return r.display.input.focus()},20);try{var c,h=e.dataTransfer.getData("Text");if(h){if(r.state.draggingText&&!r.state.draggingText.copy&&(c=r.listSelections()),io(r.doc,Ai(t,t)),c)for(var d=0;d<c.length;++d)bo(r.doc,"",c[d].anchor,c[d].head,"drag");r.replaceSelection(h,"around","paste"),r.display.input.focus()}}catch(e){}}}}function Ro(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function zo(t){if(document.getElementsByClassName){for(var e=document.getElementsByClassName("CodeMirror"),n=[],r=0;r<e.length;r++){var i=e[r].CodeMirror;i&&n.push(i)}n.length&&n[0].operation(function(){for(var e=0;e<n.length;e++)t(n[e])})}}var Bo=!1;function Go(){var e;Bo||(we(window,"resize",function(){null==e&&(e=setTimeout(function(){e=null,zo(Uo)},100))}),we(window,"blur",function(){return zo(Or)}),Bo=!0)}function Uo(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var Vo={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Ko=0;Ko<10;Ko++)Vo[Ko+48]=Vo[Ko+96]=String(Ko);for(var jo=65;jo<=90;jo++)Vo[jo]=String.fromCharCode(jo);for(var Xo=1;Xo<=12;Xo++)Vo[Xo+111]=Vo[Xo+63235]="F"+Xo;var Yo={};function _o(e){var t,n,r,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var l=0;l<o.length-1;l++){var s=o[l];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else{if(!/^s(hift)?$/i.test(s))throw new Error("Unrecognized modifier name: "+s);r=!0}}return t&&(e="Alt-"+e),n&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),r&&(e="Shift-"+e),e}function $o(e){var t={};for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];if(/^(name|fallthrough|(de|at)tach)$/.test(n))continue;if("..."==r){delete e[n];continue}for(var i=q(n.split(" "),_o),o=0;o<i.length;o++){var l=void 0,s=void 0,l=o==i.length-1?(s=i.join(" "),r):(s=i.slice(0,o+1).join(" "),"..."),a=t[s];if(a){if(a!=l)throw new Error("Inconsistent bindings for "+s)}else t[s]=l}delete e[n]}for(var u in t)e[u]=t[u];return e}function qo(e,t,n,r){var i=(t=el(t)).call?t.call(e,r):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&n(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return qo(e,t.fallthrough,n,r);for(var o=0;o<t.fallthrough.length;o++){var l=qo(e,t.fallthrough[o],n,r);if(l)return l}}}function Zo(e){var t="string"==typeof e?e:Vo[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function Qo(e,t,n){var r=e;return t.altKey&&"Alt"!=r&&(e="Alt-"+e),(y?t.metaKey:t.ctrlKey)&&"Ctrl"!=r&&(e="Ctrl-"+e),(y?t.ctrlKey:t.metaKey)&&"Cmd"!=r&&(e="Cmd-"+e),!n&&t.shiftKey&&"Shift"!=r&&(e="Shift-"+e),e}function Jo(e,t){if(m&&34==e.keyCode&&e.char)return!1;var n=Vo[e.keyCode];return null!=n&&!e.altGraphKey&&(3==e.keyCode&&e.code&&(n=e.code),Qo(n,e,t))}function el(e){return"string"==typeof e?Yo[e]:e}function tl(t,e){for(var n=t.doc.sel.ranges,r=[],i=0;i<n.length;i++){for(var o=e(n[i]);r.length&&ut(o.from,$(r).to)<=0;){var l=r.pop();if(ut(l.from,o.from)<0){o.from=l.from;break}}r.push(o)}ri(t,function(){for(var e=r.length-1;0<=e;e--)bo(t.doc,"",r[e].from,r[e].to,"+delete");Er(t)})}function nl(e,t,n){var r=oe(e.text,t+n,n);return r<0||r>e.text.length?null:r}function rl(e,t,n){var r=nl(e,t.ch,n);return null==r?null:new at(t.line,r,n<0?"after":"before")}function il(e,t,n,r,i){if(e){"rtl"==t.doc.direction&&(i=-i);var o=ye(n,t.doc.direction);if(o){var l,s,a,u=i<0?$(o):o[0],c=i<0==(1==u.level)?"after":"before";return 0<u.level||"rtl"==t.doc.direction?(l=zn(t,n),s=i<0?n.text.length-1:0,a=Bn(t,l,s).top,s=le(function(e){return Bn(t,l,e).top==a},i<0==(1==u.level)?u.from:u.to-1,s),"before"==c&&(s=nl(n,s,1))):s=i<0?u.to:u.from,new at(r,s,c)}}return new at(r,i<0?n.text.length:0,i<0?"before":"after")}function ol(t,n,s,e){var a=ye(n,t.doc.direction);if(!a)return rl(n,s,e);s.ch>=n.text.length?(s.ch=n.text.length,s.sticky="before"):s.ch<=0&&(s.ch=0,s.sticky="after");var r=ae(a,s.ch,s.sticky),i=a[r];if("ltr"==t.doc.direction&&i.level%2==0&&(0<e?i.to>s.ch:i.from<s.ch))return rl(n,s,e);function u(e,t){return nl(n,e instanceof at?e.ch:e,t)}function o(e){return t.options.lineWrapping?(l=l||zn(t,n),or(t,n,l,e)):{begin:0,end:n.text.length}}var l,c=o("before"==s.sticky?u(s,-1):s.ch);if("rtl"==t.doc.direction||1==i.level){var h=1==i.level==e<0,d=u(s,h?1:-1);if(null!=d&&(h?d<=i.to&&d<=c.end:d>=i.from&&d>=c.begin)){var f=h?"before":"after";return new at(s.line,d,f)}}function p(e,t,n){for(var r=function(e,t){return t?new at(s.line,u(e,1),"before"):new at(s.line,e,"after")};0<=e&&e<a.length;e+=t){var i=a[e],o=0<t==(1!=i.level),l=o?n.begin:u(n.end,-1);if(i.from<=l&&l<i.to)return r(l,o);if(l=o?i.from:u(i.to,-1),n.begin<=l&&l<n.end)return r(l,o)}}var g=p(r+e,e,c);if(g)return g;var m=0<e?c.end:u(c.begin,-1);return null==m||0<e&&m==n.text.length||!(g=p(0<e?0:a.length-1,e,o(m)))?null:g}Yo.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Yo.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Yo.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Alt-F":"goWordRight","Alt-B":"goWordLeft","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-D":"delWordAfter","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Yo.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Yo.default=w?Yo.macDefault:Yo.pcDefault;var ll={selectAll:ho,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),V)},killLine:function(n){return tl(n,function(e){if(e.empty()){var t=et(n.doc,e.head.line).text.length;return e.head.ch==t&&e.head.line<n.lastLine()?{from:e.head,to:at(e.head.line+1,0)}:{from:e.head,to:at(e.head.line,t)}}return{from:e.from(),to:e.to()}})},deleteLine:function(t){return tl(t,function(e){return{from:at(e.from().line,0),to:gt(t.doc,at(e.to().line+1,0))}})},delLineLeft:function(e){return tl(e,function(e){return{from:at(e.from().line,0),to:e.from()}})},delWrappedLineLeft:function(n){return tl(n,function(e){var t=n.charCoords(e.head,"div").top+5;return{from:n.coordsChar({left:0,top:t},"div"),to:e.from()}})},delWrappedLineRight:function(r){return tl(r,function(e){var t=r.charCoords(e.head,"div").top+5,n=r.coordsChar({left:r.display.lineDiv.offsetWidth+100,top:t},"div");return{from:e.from(),to:n}})},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(at(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(at(e.lastLine()))},goLineStart:function(t){return t.extendSelectionsBy(function(e){return sl(t,e.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(t){return t.extendSelectionsBy(function(e){return al(t,e.head)},{origin:"+move",bias:1})},goLineEnd:function(t){return t.extendSelectionsBy(function(e){return function(e,t){var n=et(e.doc,t),r=function(e){for(var t;t=Vt(e);)e=t.find(1,!0).line;return e}(n);r!=n&&(t=it(r));return il(!0,e,n,t,-1)}(t,e.head.line)},{origin:"+move",bias:-1})},goLineRight:function(n){return n.extendSelectionsBy(function(e){var t=n.cursorCoords(e.head,"div").top+5;return n.coordsChar({left:n.display.lineDiv.offsetWidth+100,top:t},"div")},j)},goLineLeft:function(n){return n.extendSelectionsBy(function(e){var t=n.cursorCoords(e.head,"div").top+5;return n.coordsChar({left:0,top:t},"div")},j)},goLineLeftSmart:function(r){return r.extendSelectionsBy(function(e){var t=r.cursorCoords(e.head,"div").top+5,n=r.coordsChar({left:0,top:t},"div");return n.ch<r.getLine(n.line).search(/\S/)?al(r,e.head):n},j)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"char")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],n=e.listSelections(),r=e.options.tabSize,i=0;i<n.length;i++){var o=n[i].from(),l=R(e.getLine(o.line),o.ch,r);t.push(_(r-l%r))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(l){return ri(l,function(){for(var e,t,n,r=l.listSelections(),i=[],o=0;o<r.length;o++){r[o].empty()&&(e=r[o].head,(t=et(l.doc,e.line).text)&&(e.ch==t.length&&(e=new at(e.line,e.ch-1)),0<e.ch?(e=new at(e.line,e.ch+1),l.replaceRange(t.charAt(e.ch-1)+t.charAt(e.ch-2),at(e.line,e.ch-2),e,"+transpose")):e.line>l.doc.first&&((n=et(l.doc,e.line-1).text)&&(e=new at(e.line,1),l.replaceRange(t.charAt(0)+l.doc.lineSeparator()+n.charAt(n.length-1),at(e.line-1,n.length-1),e,"+transpose")))),i.push(new Mi(e,e)))}l.setSelections(i)})},newlineAndIndent:function(r){return ri(r,function(){for(var e=r.listSelections(),t=e.length-1;0<=t;t--)r.replaceRange(r.doc.lineSeparator(),e[t].anchor,e[t].head,"+input");e=r.listSelections();for(var n=0;n<e.length;n++)r.indentLine(e[n].from().line,null,!0);Er(r)})},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function sl(e,t){var n=et(e.doc,t),r=Xt(n);return r!=n&&(t=it(r)),il(!0,e,r,t,1)}function al(e,t){var n=sl(e,t.line),r=et(e.doc,n.line),i=ye(r,e.doc.direction);if(i&&0!=i[0].level)return n;var o=Math.max(n.ch,r.text.search(/\S/)),l=t.line==n.line&&t.ch<=o&&t.ch;return at(n.line,l?0:o,n.sticky)}function ul(e,t,n){if("string"==typeof t&&!(t=ll[t]))return!1;e.display.input.ensurePolled();var r=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),n&&(e.display.shift=!1),i=t(e)!=U}finally{e.display.shift=r,e.state.suppressEdits=!1}return i}var cl=new z;function hl(e,t,n,r){var i=e.state.keySeq;if(i){if(Zo(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:cl.set(50,function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())}),dl(e,i+" "+t,n,r))return!0}return dl(e,t,n,r)}function dl(e,t,n,r){var i=function(e,t,n){for(var r=0;r<e.state.keyMaps.length;r++){var i=qo(t,e.state.keyMaps[r],n,e);if(i)return i}return e.options.extraKeys&&qo(t,e.options.extraKeys,n,e)||qo(t,e.options.keyMap,n,e)}(e,t,r);return"multi"==i&&(e.state.keySeq=t),"handled"==i&&mn(e,"keyHandled",e,t,n),"handled"!=i&&"multi"!=i||(Ne(n),Tr(e)),!!i}function fl(t,e){var n=Jo(e,!0);return!!n&&(e.shiftKey&&!t.state.keySeq?hl(t,"Shift-"+n,e,function(e){return ul(t,e,!0)})||hl(t,n,e,function(e){if("string"==typeof e?/^go[A-Z]/.test(e):e.motion)return ul(t,e)}):hl(t,n,e,function(e){return ul(t,e)}))}var pl=null;function gl(e){var t,n,r,i=this;function o(e){18!=e.keyCode&&e.altKey||(T(r,"CodeMirror-crosshair"),Ce(document,"keyup",o),Ce(document,"mouseover",o))}e.target&&e.target!=i.display.input.getField()||(i.curOp.focus=W(),Le(i,e)||(x&&C<11&&27==e.keyCode&&(e.returnValue=!1),t=e.keyCode,i.display.shift=16==t||e.shiftKey,n=fl(i,e),m&&(pl=n?t:null,!n&&88==t&&!Ue&&(w?e.metaKey:e.ctrlKey)&&i.replaceSelection("",null,"cut")),g&&!w&&!n&&46==t&&e.shiftKey&&!e.ctrlKey&&document.execCommand&&document.execCommand("cut"),18!=t||/\bCodeMirror-crosshair\b/.test(i.display.lineDiv.className)||(H(r=i.display.lineDiv,"CodeMirror-crosshair"),we(document,"keyup",o),we(document,"mouseover",o))))}function ml(e){16==e.keyCode&&(this.doc.sel.shift=!1),Le(this,e)}function vl(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField()||An(t.display,e)||Le(t,e)||e.ctrlKey&&!e.altKey||w&&e.metaKey)){var n,r,i=e.keyCode,o=e.charCode;if(m&&i==pl)return pl=null,void Ne(e);m&&(!e.which||e.which<10)&&fl(t,e)||"\b"!=(n=String.fromCharCode(null==o?i:o))&&(hl(r=t,"'"+n+"'",e,function(e){return ul(r,e,!0)})||t.display.input.onKeyPress(e))}}var yl,bl,wl=function(e,t,n){this.time=e,this.pos=t,this.button=n};function xl(e){var t,n,r,i,o,l,s=this,a=s.display;Le(s,e)||a.activeTouch&&a.input.supportsTouch()||(a.input.ensurePolled(),a.shift=e.shiftKey,An(a,e)?b||(a.scroller.draggable=!1,setTimeout(function(){return a.scroller.draggable=!0},100)):Ll(s,e)||(t=pr(s,e),n=He(e),r=t?(i=t,o=n,l=+new Date,bl&&bl.compare(l,i,o)?(yl=bl=null,"triple"):yl&&yl.compare(l,i,o)?(bl=new wl(l,i,o),yl=null,"double"):(yl=new wl(l,i,o),bl=null,"single")):"single",window.focus(),1==n&&s.state.selectingText&&s.state.selectingText(e),t&&function(n,e,r,t,i){var o="Click";"double"==t?o="Double"+o:"triple"==t&&(o="Triple"+o);return hl(n,Qo(o=(1==e?"Left":2==e?"Middle":"Right")+o,i),i,function(e){if("string"==typeof e&&(e=ll[e]),!e)return!1;var t=!1;try{n.isReadOnly()&&(n.state.suppressEdits=!0),t=e(n,r)!=U}finally{n.state.suppressEdits=!1}return t})}(s,n,t,r,e)||(1==n?t?function(e,t,n,r){x?setTimeout(E(Mr,e),0):e.curOp.focus=W();var i,o=function(e,t,n){var r=e.getOption("configureMouse"),i=r?r(e,t,n):{};{var o;null==i.unit&&(o=d?n.shiftKey&&n.metaKey:n.altKey,i.unit=o?"rectangle":"single"==t?"char":"double"==t?"word":"line")}null!=i.extend&&!e.doc.extend||(i.extend=e.doc.extend||n.shiftKey);null==i.addNew&&(i.addNew=w?n.metaKey:n.ctrlKey);null==i.moveOnDrag&&(i.moveOnDrag=!(w?n.altKey:n.ctrlKey));return i}(e,n,r),l=e.doc.sel;(e.options.dragDrop&&Ee&&!e.isReadOnly()&&"single"==n&&-1<(i=l.contains(t))&&(ut((i=l.ranges[i]).from(),t)<0||0<t.xRel)&&(0<ut(i.to(),t)||t.xRel<0)?function(t,n,r,i){var o=t.display,l=!1,s=ii(t,function(e){b&&(o.scroller.draggable=!1),t.state.draggingText=!1,Ce(o.wrapper.ownerDocument,"mouseup",s),Ce(o.wrapper.ownerDocument,"mousemove",a),Ce(o.scroller,"dragstart",u),Ce(o.scroller,"drop",s),l||(Ne(e),i.addNew||Qi(t.doc,r,null,null,i.extend),b&&!c||x&&9==C?setTimeout(function(){o.wrapper.ownerDocument.body.focus({preventScroll:!0}),o.input.focus()},20):o.input.focus())}),a=function(e){l=l||10<=Math.abs(n.clientX-e.clientX)+Math.abs(n.clientY-e.clientY)},u=function(){return l=!0};b&&(o.scroller.draggable=!0);(t.state.draggingText=s).copy=!i.moveOnDrag,o.scroller.dragDrop&&o.scroller.dragDrop();we(o.wrapper.ownerDocument,"mouseup",s),we(o.wrapper.ownerDocument,"mousemove",a),we(o.scroller,"dragstart",u),we(o.scroller,"drop",s),Nr(t),setTimeout(function(){return o.input.focus()},20)}:function(m,e,v,y){var l=m.display,b=m.doc;Ne(e);var w,x,C=b.sel,t=C.ranges;y.addNew&&!y.extend?(x=b.sel.contains(v),w=-1<x?t[x]:new Mi(v,v)):(w=b.sel.primary(),x=b.sel.primIndex);{var n;"rectangle"==y.unit?(y.addNew||(w=new Mi(v,v)),v=pr(m,e,!0,!0),x=-1):(n=Cl(m,v,y.unit),w=y.extend?Zi(w,n.anchor,n.head,y.extend):n)}y.addNew?-1==x?(x=t.length,ro(b,Ni(m,t.concat([w]),x),{scroll:!1,origin:"*mouse"})):1<t.length&&t[x].empty()&&"char"==y.unit&&!y.extend?(ro(b,Ni(m,t.slice(0,x).concat(t.slice(x+1)),0),{scroll:!1,origin:"*mouse"}),C=b.sel):eo(b,x,w,K):(ro(b,new Ti([w],x=0),K),C=b.sel);var S=v;function s(e){if(0!=ut(S,e))if(S=e,"rectangle"==y.unit){for(var t=[],n=m.options.tabSize,r=R(et(b,v.line).text,v.ch,n),i=R(et(b,e.line).text,e.ch,n),o=Math.min(r,i),l=Math.max(r,i),s=Math.min(v.line,e.line),a=Math.min(m.lastLine(),Math.max(v.line,e.line));s<=a;s++){var u=et(b,s).text,c=X(u,o,n);o==l?t.push(new Mi(at(s,c),at(s,c))):u.length>c&&t.push(new Mi(at(s,c),at(s,X(u,l,n))))}t.length||t.push(new Mi(v,v)),ro(b,Ni(m,C.ranges.slice(0,x).concat(t),x),{origin:"*mouse",scroll:!1}),m.scrollIntoView(e)}else{var h,d=w,f=Cl(m,e,y.unit),p=d.anchor,p=0<ut(f.anchor,p)?(h=f.head,ft(d.from(),f.anchor)):(h=f.anchor,dt(d.to(),f.head)),g=C.ranges.slice(0);g[x]=function(e,t){var n=t.anchor,r=t.head,i=et(e.doc,n.line);if(0==ut(n,r)&&n.sticky==r.sticky)return t;var o=ye(i);if(!o)return t;var l=ae(o,n.ch,n.sticky),s=o[l];if(s.from!=n.ch&&s.to!=n.ch)return t;var a,u=l+(s.from==n.ch==(1!=s.level)?0:1);if(0==u||u==o.length)return t;{var c,h;a=r.line!=n.line?0<(r.line-n.line)*("ltr"==e.doc.direction?1:-1):(c=ae(o,r.ch,r.sticky),h=c-l||(r.ch-n.ch)*(1==s.level?-1:1),c==u-1||c==u?h<0:0<h)}var d=o[u+(a?-1:0)],f=a==(1==d.level),p=f?d.from:d.to,g=f?"after":"before";return n.ch==p&&n.sticky==g?t:new Mi(new at(n.line,p,g),r)}(m,new Mi(gt(b,p),h)),ro(b,Ni(m,g,x),K)}}var a=l.wrapper.getBoundingClientRect(),u=0;function r(e){m.state.selectingText=!1,u=1/0,e&&(Ne(e),l.input.focus()),Ce(l.wrapper.ownerDocument,"mousemove",i),Ce(l.wrapper.ownerDocument,"mouseup",o),b.history.lastSelOrigin=null}var i=ii(m,function(e){(0!==e.buttons&&He(e)?function e(t){var n,r,i=++u,o=pr(m,t,!0,"rectangle"==y.unit);o&&(0!=ut(o,S)?(m.curOp.focus=W(),s(o),n=Hr(l,b),(o.line>=n.to||o.line<n.from)&&setTimeout(ii(m,function(){u==i&&e(t)}),150)):(r=t.clientY<a.top?-20:t.clientY>a.bottom?20:0)&&setTimeout(ii(m,function(){u==i&&(l.scroller.scrollTop+=r,e(t))}),50))}:r)(e)}),o=ii(m,r);m.state.selectingText=o,we(l.wrapper.ownerDocument,"mousemove",i),we(l.wrapper.ownerDocument,"mouseup",o)})(e,r,t,o)}(s,t,r,e):We(e)==a.scroller&&Ne(e):2==n?(t&&Qi(s.doc,t),setTimeout(function(){return a.input.focus()},20)):3==n&&(S?s.display.input.onContextMenu(e):Nr(s)))))}function Cl(e,t,n){if("char"==n)return new Mi(t,t);if("word"==n)return e.findWordAt(t);if("line"==n)return new Mi(at(t.line,0),gt(e.doc,at(t.line+1,0)));var r=n(e,t);return new Mi(r.from,r.to)}function Sl(e,t,n,r){var i,o;if(t.touches)i=t.touches[0].clientX,o=t.touches[0].clientY;else try{i=t.clientX,o=t.clientY}catch(e){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;r&&Ne(t);var l=e.display,s=l.lineDiv.getBoundingClientRect();if(o>s.bottom||!Te(e,n))return Oe(t);o-=s.top-l.viewOffset;for(var a=0;a<e.display.gutterSpecs.length;++a){var u=l.gutters.childNodes[a];if(u&&u.getBoundingClientRect().right>=i)return Se(e,n,e,ot(e.doc,o),e.display.gutterSpecs[a].className,t),Oe(t)}}function Ll(e,t){return Sl(e,t,"gutterClick",!0)}function kl(e,t){var n,r;An(e.display,t)||(r=t,Te(n=e,"gutterContextMenu")&&Sl(n,r,"gutterContextMenu",!1))||Le(e,t,"contextmenu")||S||e.display.input.onContextMenu(t)}function Tl(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),Yn(e)}wl.prototype.compare=function(e,t,n){return this.time+400>e&&0==ut(t,this.pos)&&n==this.button};var Ml={toString:function(){return"CodeMirror.Init"}},Nl={},Al={};function Ol(e,t,n){var r,i;!t!=!(n&&n!=Ml)&&(r=e.display.dragFunctions,(i=t?we:Ce)(e.display.scroller,"dragstart",r.start),i(e.display.scroller,"dragenter",r.enter),i(e.display.scroller,"dragover",r.over),i(e.display.scroller,"dragleave",r.leave),i(e.display.scroller,"drop",r.drop))}function Dl(e){e.options.lineWrapping?(H(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(T(e.display.wrapper,"CodeMirror-wrap"),Jt(e)),fr(e),mr(e),Yn(e),setTimeout(function(){return Xr(e)},100)}function Wl(e,t){var n=this;if(!(this instanceof Wl))return new Wl(e,t);this.options=t=t?I(t):{},I(Nl,t,!1);var r=t.value;"string"==typeof r?r=new Po(r,t.mode,null,t.lineSeparator,t.direction):t.mode&&(r.modeOption=t.mode),this.doc=r;var i=new Wl.inputStyles[t.inputStyle](this),o=this.display=new wi(e,r,i,t);for(var l in Tl(o.wrapper.CodeMirror=this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),$r(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new z,keySeq:null,specialChars:null},t.autofocus&&!h&&o.input.focus(),x&&C<11&&setTimeout(function(){return n.display.input.reset(!0)},20),function(i){var o=i.display;we(o.scroller,"mousedown",ii(i,xl)),we(o.scroller,"dblclick",x&&C<11?ii(i,function(e){var t,n;Le(i,e)||(!(t=pr(i,e))||Ll(i,e)||An(i.display,e)||(Ne(e),n=i.findWordAt(t),Qi(i.doc,n.anchor,n.head)))}):function(e){return Le(i,e)||Ne(e)});we(o.scroller,"contextmenu",function(e){return kl(i,e)}),we(o.input.getField(),"contextmenu",function(e){o.scroller.contains(e.target)||kl(i,e)});var n,r={end:0};function l(){o.activeTouch&&(n=setTimeout(function(){return o.activeTouch=null},1e3),(r=o.activeTouch).end=+new Date)}function s(e,t){if(null==t.left)return 1;var n=t.left-e.left,r=t.top-e.top;return 400<n*n+r*r}we(o.scroller,"touchstart",function(e){var t;Le(i,e)||function(e){if(1==e.touches.length){var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}}(e)||Ll(i,e)||(o.input.ensurePolled(),clearTimeout(n),t=+new Date,o.activeTouch={start:t,moved:!1,prev:t-r.end<=300?r:null},1==e.touches.length&&(o.activeTouch.left=e.touches[0].pageX,o.activeTouch.top=e.touches[0].pageY))}),we(o.scroller,"touchmove",function(){o.activeTouch&&(o.activeTouch.moved=!0)}),we(o.scroller,"touchend",function(e){var t,n,r=o.activeTouch;r&&!An(o,e)&&null!=r.left&&!r.moved&&new Date-r.start<300&&(t=i.coordsChar(o.activeTouch,"page"),n=!r.prev||s(r,r.prev)?new Mi(t,t):!r.prev.prev||s(r,r.prev.prev)?i.findWordAt(t):new Mi(at(t.line,0),gt(i.doc,at(t.line+1,0))),i.setSelection(n.anchor,n.head),i.focus(),Ne(e)),l()}),we(o.scroller,"touchcancel",l),we(o.scroller,"scroll",function(){o.scroller.clientHeight&&(Br(i,o.scroller.scrollTop),Ur(i,o.scroller.scrollLeft,!0),Se(i,"scroll",i))}),we(o.scroller,"mousewheel",function(e){return ki(i,e)}),we(o.scroller,"DOMMouseScroll",function(e){return ki(i,e)}),we(o.wrapper,"scroll",function(){return o.wrapper.scrollTop=o.wrapper.scrollLeft=0}),o.dragFunctions={enter:function(e){Le(i,e)||De(e)},over:function(e){var t,n,r;Le(i,e)||((r=pr(t=i,e))&&(Sr(t,r,n=document.createDocumentFragment()),t.display.dragCursor||(t.display.dragCursor=O("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),t.display.lineSpace.insertBefore(t.display.dragCursor,t.display.cursorDiv)),N(t.display.dragCursor,n)),De(e))},start:function(e){return t=i,n=e,void(x&&(!t.state.draggingText||new Date-Eo<100)?De(n):Le(t,n)||An(t.display,n)||(n.dataTransfer.setData("Text",t.getSelection()),n.dataTransfer.effectAllowed="copyMove",n.dataTransfer.setDragImage&&!c&&((r=O("img",null,null,"position: fixed; left: 0; top: 0;")).src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",m&&(r.width=r.height=1,t.display.wrapper.appendChild(r),r._top=r.offsetTop),n.dataTransfer.setDragImage(r,0,0),m&&r.parentNode.removeChild(r))));var t,n,r},drop:ii(i,Io),leave:function(e){Le(i,e)||Ro(i)}};var e=o.input.getField();we(e,"keyup",function(e){return ml.call(i,e)}),we(e,"keydown",ii(i,gl)),we(e,"keypress",ii(i,vl)),we(e,"focus",function(e){return Ar(i,e)}),we(e,"blur",function(e){return Or(i,e)})}(this),Go(),Zr(this),this.curOp.forceUpdate=!0,zi(this,r),t.autofocus&&!h||this.hasFocus()?setTimeout(E(Ar,this),20):Or(this),Al)Al.hasOwnProperty(l)&&Al[l](this,t[l],Ml);mi(this),t.finishInit&&t.finishInit(this);for(var s=0;s<Hl.length;++s)Hl[s](this);Qr(this),b&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}Wl.defaults=Nl,Wl.optionHandlers=Al;var Hl=[];function Fl(e,t,n,r){var i,o=e.doc;null==n&&(n="add"),"smart"==n&&(o.mode.indent?i=xt(e,t).state:n="prev");var l=e.options.tabSize,s=et(o,t),a=R(s.text,null,l);s.stateAfter&&(s.stateAfter=null);var u,c=s.text.match(/^\s*/)[0];if(r||/\S/.test(s.text)){if("smart"==n&&((u=o.mode.indent(i,s.text.slice(c.length),s.text))==U||150<u)){if(!r)return;n="prev"}}else u=0,n="not";"prev"==n?u=t>o.first?R(et(o,t-1).text,null,l):0:"add"==n?u=a+e.options.indentUnit:"subtract"==n?u=a-e.options.indentUnit:"number"==typeof n&&(u=a+n),u=Math.max(0,u);var h="",d=0;if(e.options.indentWithTabs)for(var f=Math.floor(u/l);f;--f)d+=l,h+="\t";if(d<u&&(h+=_(u-d)),h!=c)return bo(o,h,at(t,0),at(t,c.length),"+input"),!(s.stateAfter=null);for(var p=0;p<o.sel.ranges.length;p++){var g=o.sel.ranges[p];if(g.head.line==t&&g.head.ch<c.length){var m=at(t,c.length);eo(o,p,new Mi(m,m));break}}}Wl.defineInitHook=function(e){return Hl.push(e)};var Pl=null;function El(e){Pl=e}function Il(e,t,n,r,i){var o=e.doc;e.display.shift=!1,r=r||o.sel;var l=new Date-200,s="paste"==i||e.state.pasteIncoming>l,a=Be(t),u=null;if(s&&1<r.ranges.length)if(Pl&&Pl.text.join("\n")==t){if(r.ranges.length%Pl.text.length==0){u=[];for(var c=0;c<Pl.text.length;c++)u.push(o.splitLines(Pl.text[c]))}}else a.length==r.ranges.length&&e.options.pasteLinesPerSelection&&(u=q(a,function(e){return[e]}));for(var h=e.curOp.updateInput,d=r.ranges.length-1;0<=d;d--){var f=r.ranges[d],p=f.from(),g=f.to();f.empty()&&(n&&0<n?p=at(p.line,p.ch-n):e.state.overwrite&&!s?g=at(g.line,Math.min(et(o,g.line).text.length,g.ch+$(a).length)):s&&Pl&&Pl.lineWise&&Pl.text.join("\n")==t&&(p=g=at(p.line,0)));var m={from:p,to:g,text:u?u[d%u.length]:a,origin:i||(s?"paste":e.state.cutIncoming>l?"cut":"+input")};po(e.doc,m),mn(e,"inputRead",e,m)}t&&!s&&zl(e,t),Er(e),e.curOp.updateInput<2&&(e.curOp.updateInput=h),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function Rl(e,t){var n=e.clipboardData&&e.clipboardData.getData("Text");return n&&(e.preventDefault(),t.isReadOnly()||t.options.disableInput||ri(t,function(){return Il(t,n,0,null,"paste")}),1)}function zl(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var n=e.doc.sel,r=n.ranges.length-1;0<=r;r--){var i=n.ranges[r];if(!(100<i.head.ch||r&&n.ranges[r-1].head.line==i.head.line)){var o=e.getModeAt(i.head),l=!1;if(o.electricChars){for(var s=0;s<o.electricChars.length;s++)if(-1<t.indexOf(o.electricChars.charAt(s))){l=Fl(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(et(e.doc,i.head.line).text.slice(0,i.head.ch))&&(l=Fl(e,i.head.line,"smart"));l&&mn(e,"electricInput",e,i.head.line)}}}function Bl(e){for(var t=[],n=[],r=0;r<e.doc.sel.ranges.length;r++){var i=e.doc.sel.ranges[r].head.line,o={anchor:at(i,0),head:at(i+1,0)};n.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:n}}function Gl(e,t,n,r){e.setAttribute("autocorrect",n?"":"off"),e.setAttribute("autocapitalize",r?"":"off"),e.setAttribute("spellcheck",!!t)}function Ul(){var e=O("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; outline: none"),t=O("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return b?e.style.width="1000px":e.setAttribute("wrap","off"),a&&(e.style.border="1px solid black"),Gl(e),t}function Vl(r,i,o,e,l){var t=i,n=o,s=et(r,i.line),a=l&&"rtl"==r.direction?-o:o;function u(e){var t,n=l?ol(r.cm,s,i,o):rl(s,i,o);if(null==n){if(e||(t=i.line+a)<r.first||t>=r.first+r.size||(i=new at(t,i.ch,i.sticky),!(s=et(r,t))))return;i=il(l,r.cm,s,i.line,a)}else i=n;return 1}if("char"==e)u();else if("column"==e)u(!0);else if("word"==e||"group"==e)for(var c=null,h="group"==e,d=r.cm&&r.cm.getHelper(i,"wordChars"),f=!0;!(o<0)||u(!f);f=!1){var p=s.text.charAt(i.ch)||"\n",g=te(p,d)?"w":h&&"\n"==p?"n":!h||/\s/.test(p)?null:"p";if(!h||f||g||(g="s"),c&&c!=g){o<0&&(o=1,u(),i.sticky="after");break}if(g&&(c=g),0<o&&!u(!f))break}var m=uo(r,i,t,n,!0);return ct(t,m)&&(m.hitSide=!0),m}function Kl(e,t,n,r){var i,o,l,s,a=e.doc,u=t.left;for("page"==r?(i=Math.min(e.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight),o=Math.max(i-.5*ar(e.display),3),l=(0<n?t.bottom:t.top)+n*o):"line"==r&&(l=0<n?t.bottom+3:t.top-3);(s=rr(e,u,l)).outside;){if(n<0?l<=0:l>=a.height){s.hitSide=!0;break}l+=5*n}return s}function jl(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new z,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null}function Xl(e,t){var n=Rn(e,t.line);if(!n||n.hidden)return null;var r=et(e.doc,t.line),i=En(n,r,t.line),o=ye(r,e.doc.direction),l="left";o&&(l=ae(o,t.ch)%2?"right":"left");var s=Vn(i.map,t.ch,l);return s.offset="right"==s.collapse?s.end:s.start,s}function Yl(e,t){return t&&(e.bad=!0),e}function _l(e,t,n){var r;if(t==e.display.lineDiv){if(!(r=e.display.lineDiv.childNodes[n]))return Yl(e.clipPos(at(e.display.viewTo-1)),!0);t=null,n=0}else for(r=t;;r=r.parentNode){if(!r||r==e.display.lineDiv)return null;if(r.parentNode&&r.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==r)return $l(o,t,n)}}function $l(u,e,t){var n=u.text.firstChild,r=!1;if(!e||!D(n,e))return Yl(at(it(u.line),0),!0);if(e==n&&(r=!0,e=n.childNodes[t],t=0,!e)){var i=u.rest?$(u.rest):u.line;return Yl(at(it(i),i.text.length),r)}var o=3==e.nodeType?e:null,l=e;for(o||1!=e.childNodes.length||3!=e.firstChild.nodeType||(o=e.firstChild,t=t&&o.nodeValue.length);l.parentNode!=n;)l=l.parentNode;var c=u.measure,h=c.maps;function s(e,t,n){for(var r=-1;r<(h?h.length:0);r++)for(var i=r<0?c.map:h[r],o=0;o<i.length;o+=3){var l=i[o+2];if(l==e||l==t){var s=it(r<0?u.line:u.rest[r]),a=i[o]+n;return(n<0||l!=e)&&(a=i[o+(n?1:0)]),at(s,a)}}}var a=s(o,l,t);if(a)return Yl(a,r);for(var d=l.nextSibling,f=o?o.nodeValue.length-t:0;d;d=d.nextSibling){if(a=s(d,d.firstChild,0))return Yl(at(a.line,a.ch-f),r);f+=d.textContent.length}for(var p=l.previousSibling,g=t;p;p=p.previousSibling){if(a=s(p,p.firstChild,-1))return Yl(at(a.line,a.ch+g),r);g+=p.textContent.length}}jl.prototype.init=function(e){var t=this,l=this,s=l.cm,a=l.div=e.lineDiv;function u(e){for(var t=e.target;t;t=t.parentNode){if(t==a)return 1;if(/\bCodeMirror-(?:line)?widget\b/.test(t.className))break}}function n(e){if(u(e)&&!Le(s,e)){if(s.somethingSelected())El({lineWise:!1,text:s.getSelections()}),"cut"==e.type&&s.replaceSelection("",null,"cut");else{if(!s.options.lineWiseCopyCut)return;var t=Bl(s);El({lineWise:!0,text:t.text}),"cut"==e.type&&s.operation(function(){s.setSelections(t.ranges,0,V),s.replaceSelection("",null,"cut")})}if(e.clipboardData){e.clipboardData.clearData();var n=Pl.text.join("\n");if(e.clipboardData.setData("Text",n),e.clipboardData.getData("Text")==n)return void e.preventDefault()}var r=Ul(),i=r.firstChild;s.display.lineSpace.insertBefore(r,s.display.lineSpace.firstChild),i.value=Pl.text.join("\n");var o=document.activeElement;P(i),setTimeout(function(){s.display.lineSpace.removeChild(r),o.focus(),o==a&&l.showPrimarySelection()},50)}}Gl(a,s.options.spellcheck,s.options.autocorrect,s.options.autocapitalize),we(a,"paste",function(e){!u(e)||Le(s,e)||Rl(e,s)||C<=11&&setTimeout(ii(s,function(){return t.updateFromDOM()}),20)}),we(a,"compositionstart",function(e){t.composing={data:e.data,done:!1}}),we(a,"compositionupdate",function(e){t.composing||(t.composing={data:e.data,done:!1})}),we(a,"compositionend",function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)}),we(a,"touchstart",function(){return l.forceCompositionEnd()}),we(a,"input",function(){t.composing||t.readFromDOMSoon()}),we(a,"copy",n),we(a,"cut",n)},jl.prototype.screenReaderLabelChanged=function(e){e?this.div.setAttribute("aria-label",e):this.div.removeAttribute("aria-label")},jl.prototype.prepareSelection=function(){var e=Cr(this.cm,!1);return e.focus=document.activeElement==this.div,e},jl.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},jl.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},jl.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,n=t.doc.sel.primary(),r=n.from(),i=n.to();if(t.display.viewTo==t.display.viewFrom||r.line>=t.display.viewTo||i.line<t.display.viewFrom)e.removeAllRanges();else{var o=_l(t,e.anchorNode,e.anchorOffset),l=_l(t,e.focusNode,e.focusOffset);if(!o||o.bad||!l||l.bad||0!=ut(ft(o,l),r)||0!=ut(dt(o,l),i)){var s,a,u=t.display.view,c=r.line>=t.display.viewFrom&&Xl(t,r)||{node:u[0].measure.map[2],offset:0},h=i.line<t.display.viewTo&&Xl(t,i);if(h||(h={node:(a=(s=u[u.length-1].measure).maps?s.maps[s.maps.length-1]:s.map)[a.length-1],offset:a[a.length-2]-a[a.length-3]}),c&&h){var d,f=e.rangeCount&&e.getRangeAt(0);try{d=k(c.node,c.offset,h.offset,h.node)}catch(e){}d&&(!g&&t.state.focused?(e.collapse(c.node,c.offset),d.collapsed||(e.removeAllRanges(),e.addRange(d))):(e.removeAllRanges(),e.addRange(d)),f&&null==e.anchorNode?e.addRange(f):g&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},jl.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation(function(){return e.cm.curOp.selectionChanged=!0})},20)},jl.prototype.showMultipleSelections=function(e){N(this.cm.display.cursorDiv,e.cursors),N(this.cm.display.selectionDiv,e.selection)},jl.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},jl.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return D(this.div,t)},jl.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()&&document.activeElement==this.div||this.showSelection(this.prepareSelection(),!0),this.div.focus())},jl.prototype.blur=function(){this.div.blur()},jl.prototype.getField=function(){return this.div},jl.prototype.supportsTouch=function(){return!0},jl.prototype.receivedFocus=function(){var t=this;this.selectionInEditor()?this.pollSelection():ri(this.cm,function(){return t.cm.curOp.selectionChanged=!0}),this.polling.set(this.cm.options.pollInterval,function e(){t.cm.state.focused&&(t.pollSelection(),t.polling.set(t.cm.options.pollInterval,e))})},jl.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},jl.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e,t,n=this.getSelection(),r=this.cm;if(u&&l&&this.cm.display.gutterSpecs.length&&function(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return 1;return}(n.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();this.composing||(this.rememberSelection(),e=_l(r,n.anchorNode,n.anchorOffset),t=_l(r,n.focusNode,n.focusOffset),e&&t&&ri(r,function(){ro(r.doc,Ai(e,t),V),(e.bad||t.bad)&&(r.curOp.selectionChanged=!0)}))}},jl.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,n,r=this.cm,i=r.display,o=r.doc.sel.primary(),l=o.from(),s=o.to();if(0==l.ch&&l.line>r.firstLine()&&(l=at(l.line-1,et(r.doc,l.line-1).length)),s.ch==et(r.doc,s.line).text.length&&s.line<r.lastLine()&&(s=at(s.line+1,0)),l.line<i.viewFrom||s.line>i.viewTo-1)return!1;n=l.line==i.viewFrom||0==(e=gr(r,l.line))?(t=it(i.view[0].line),i.view[0].node):(t=it(i.view[e].line),i.view[e-1].node.nextSibling);var a,u=gr(r,s.line),c=u==i.view.length-1?(a=i.viewTo-1,i.lineDiv.lastChild):(a=it(i.view[u+1].line)-1,i.view[u+1].node.previousSibling);if(!n)return!1;for(var h=r.doc.splitLines(function(a,e,t,u,c){var n="",h=!1,d=a.doc.lineSeparator(),f=!1;function p(){h&&(n+=d,f&&(n+=d),h=f=!1)}function g(e){e&&(p(),n+=e)}function m(e){if(1==e.nodeType){var t=e.getAttribute("cm-text");if(t)return void g(t);var n,r=e.getAttribute("cm-marker");if(r){var i=a.findMarks(at(u,0),at(c+1,0),(s=+r,function(e){return e.id==s}));return void(i.length&&(n=i[0].find(0))&&g(tt(a.doc,n.from,n.to).join(d)))}if("false"==e.getAttribute("contenteditable"))return;var o=/^(pre|div|p|li|table|br)$/i.test(e.nodeName);if(!/^br$/i.test(e.nodeName)&&0==e.textContent.length)return;o&&p();for(var l=0;l<e.childNodes.length;l++)m(e.childNodes[l]);/^(pre|p)$/i.test(e.nodeName)&&(f=!0),o&&(h=!0)}else 3==e.nodeType&&g(e.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "));var s}for(;m(e),e!=t;)e=e.nextSibling,f=!1;return n}(r,n,c,t,a)),d=tt(r.doc,at(t,0),at(a,et(r.doc,a).text.length));1<h.length&&1<d.length;)if($(h)==$(d))h.pop(),d.pop(),a--;else{if(h[0]!=d[0])break;h.shift(),d.shift(),t++}for(var f=0,p=0,g=h[0],m=d[0],v=Math.min(g.length,m.length);f<v&&g.charCodeAt(f)==m.charCodeAt(f);)++f;for(var y=$(h),b=$(d),w=Math.min(y.length-(1==h.length?f:0),b.length-(1==d.length?f:0));p<w&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)++p;if(1==h.length&&1==d.length&&t==l.line)for(;f&&f>l.ch&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)f--,p++;h[h.length-1]=y.slice(0,y.length-p).replace(/^\u200b+/,""),h[0]=h[0].slice(f).replace(/\u200b+$/,"");var x=at(t,f),C=at(a,d.length?$(d).length-p:0);return 1<h.length||h[0]||ut(x,C)?(bo(r.doc,h,x,C,"+input"),!0):void 0},jl.prototype.ensurePolled=function(){this.forceCompositionEnd()},jl.prototype.reset=function(){this.forceCompositionEnd()},jl.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},jl.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout(function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()},80))},jl.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||ri(this.cm,function(){return mr(e.cm)})},jl.prototype.setUneditable=function(e){e.contentEditable="false"},jl.prototype.onKeyPress=function(e){0==e.charCode||this.composing||(e.preventDefault(),this.cm.isReadOnly()||ii(this.cm,Il)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},jl.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},jl.prototype.onContextMenu=function(){},jl.prototype.resetPosition=function(){},jl.prototype.needsContentAttribute=!0;function ql(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new z,this.hasSelection=!1,this.composing=null}var Zl,Ql,Jl,es,ts;function ns(e,t,r,n){Zl.defaults[e]=t,r&&(Ql[e]=n?function(e,t,n){n!=Ml&&r(e,t,n)}:r)}ql.prototype.init=function(n){var e=this,r=this,i=this.cm;this.createField(n);var o=this.textarea;function t(e){if(!Le(i,e)){if(i.somethingSelected())El({lineWise:!1,text:i.getSelections()});else{if(!i.options.lineWiseCopyCut)return;var t=Bl(i);El({lineWise:!0,text:t.text}),"cut"==e.type?i.setSelections(t.ranges,null,V):(r.prevInput="",o.value=t.text.join("\n"),P(o))}"cut"==e.type&&(i.state.cutIncoming=+new Date)}}n.wrapper.insertBefore(this.wrapper,n.wrapper.firstChild),a&&(o.style.width="0px"),we(o,"input",function(){x&&9<=C&&e.hasSelection&&(e.hasSelection=null),r.poll()}),we(o,"paste",function(e){Le(i,e)||Rl(e,i)||(i.state.pasteIncoming=+new Date,r.fastPoll())}),we(o,"cut",t),we(o,"copy",t),we(n.scroller,"paste",function(e){if(!An(n,e)&&!Le(i,e)){if(!o.dispatchEvent)return i.state.pasteIncoming=+new Date,void r.focus();var t=new Event("paste");t.clipboardData=e.clipboardData,o.dispatchEvent(t)}}),we(n.lineSpace,"selectstart",function(e){An(n,e)||Ne(e)}),we(o,"compositionstart",function(){var e=i.getCursor("from");r.composing&&r.composing.range.clear(),r.composing={start:e,range:i.markText(e,i.getCursor("to"),{className:"CodeMirror-composing"})}}),we(o,"compositionend",function(){r.composing&&(r.poll(),r.composing.range.clear(),r.composing=null)})},ql.prototype.createField=function(e){this.wrapper=Ul(),this.textarea=this.wrapper.firstChild},ql.prototype.screenReaderLabelChanged=function(e){e?this.textarea.setAttribute("aria-label",e):this.textarea.removeAttribute("aria-label")},ql.prototype.prepareSelection=function(){var e,t,n,r=this.cm,i=r.display,o=r.doc,l=Cr(r);return r.options.moveInputWithCursor&&(e=er(r,o.sel.primary().head,"div"),t=i.wrapper.getBoundingClientRect(),n=i.lineDiv.getBoundingClientRect(),l.teTop=Math.max(0,Math.min(i.wrapper.clientHeight-10,e.top+n.top-t.top)),l.teLeft=Math.max(0,Math.min(i.wrapper.clientWidth-10,e.left+n.left-t.left))),l},ql.prototype.showSelection=function(e){var t=this.cm.display;N(t.cursorDiv,e.cursors),N(t.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},ql.prototype.reset=function(e){var t,n;this.contextMenuPending||this.composing||((t=this.cm).somethingSelected()?(this.prevInput="",n=t.getSelection(),this.textarea.value=n,t.state.focused&&P(this.textarea),x&&9<=C&&(this.hasSelection=n)):e||(this.prevInput=this.textarea.value="",x&&9<=C&&(this.hasSelection=null)))},ql.prototype.getField=function(){return this.textarea},ql.prototype.supportsTouch=function(){return!1},ql.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!h||W()!=this.textarea))try{this.textarea.focus()}catch(e){}},ql.prototype.blur=function(){this.textarea.blur()},ql.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},ql.prototype.receivedFocus=function(){this.slowPoll()},ql.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,function(){e.poll(),e.cm.state.focused&&e.slowPoll()})},ql.prototype.fastPoll=function(){var t=!1,n=this;n.pollingFast=!0,n.polling.set(20,function e(){n.poll()||t?(n.pollingFast=!1,n.slowPoll()):(t=!0,n.polling.set(60,e))})},ql.prototype.poll=function(){var e=this,t=this.cm,n=this.textarea,r=this.prevInput;if(this.contextMenuPending||!t.state.focused||Ge(n)&&!r&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=n.value;if(i==r&&!t.somethingSelected())return!1;if(x&&9<=C&&this.hasSelection===i||w&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||r||(r="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var l=0,s=Math.min(r.length,i.length);l<s&&r.charCodeAt(l)==i.charCodeAt(l);)++l;return ri(t,function(){Il(t,i.slice(l),r.length-l,null,e.composing?"*compose":null),1e3<i.length||-1<i.indexOf("\n")?n.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},ql.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},ql.prototype.onKeyPress=function(){x&&9<=C&&(this.hasSelection=null),this.fastPoll()},ql.prototype.onContextMenu=function(e){var n=this,r=n.cm,i=r.display,o=n.textarea;n.contextMenuPending&&n.contextMenuPending();var l,s,t,a,u,c=pr(r,e),h=i.scroller.scrollTop;function d(){var e,t;null!=o.selectionStart&&(t="​"+((e=r.somethingSelected())?o.value:""),o.value="⇚",o.value=t,n.prevInput=e?"":"​",o.selectionStart=1,o.selectionEnd=t.length,i.selForContextMenu=r.doc.sel)}function f(){var e,t;n.contextMenuPending==f&&(n.contextMenuPending=!1,n.wrapper.style.cssText=s,o.style.cssText=l,x&&C<9&&i.scrollbars.setScrollTop(i.scroller.scrollTop=h),null!=o.selectionStart&&((!x||x&&C<9)&&d(),e=0,t=function(){i.selForContextMenu==r.doc.sel&&0==o.selectionStart&&0<o.selectionEnd&&"​"==n.prevInput?ii(r,ho)(r):e++<10?i.detectingSelectAll=setTimeout(t,500):(i.selForContextMenu=null,i.input.reset())},i.detectingSelectAll=setTimeout(t,200)))}c&&!m&&(r.options.resetSelectionOnContextMenu&&-1==r.doc.sel.contains(c)&&ii(r,ro)(r.doc,Ai(c),V),l=o.style.cssText,s=n.wrapper.style.cssText,t=n.wrapper.offsetParent.getBoundingClientRect(),n.wrapper.style.cssText="position: static",o.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-t.top-5)+"px; left: "+(e.clientX-t.left-5)+"px;\n      z-index: 1000; background: "+(x?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",b&&(a=window.scrollY),i.input.focus(),b&&window.scrollTo(null,a),i.input.reset(),r.somethingSelected()||(o.value=n.prevInput=" "),n.contextMenuPending=f,i.selForContextMenu=r.doc.sel,clearTimeout(i.detectingSelectAll),x&&9<=C&&d(),S?(De(e),u=function(){Ce(window,"mouseup",u),setTimeout(f,20)},we(window,"mouseup",u)):setTimeout(f,50))},ql.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e},ql.prototype.setUneditable=function(){},ql.prototype.needsContentAttribute=!1,Ql=(Zl=Wl).optionHandlers,Zl.defineOption=ns,Zl.Init=Ml,ns("value","",function(e,t){return e.setValue(t)},!0),ns("mode",null,function(e,t){e.doc.modeOption=t,Fi(e)},!0),ns("indentUnit",2,Fi,!0),ns("indentWithTabs",!1),ns("smartIndent",!0),ns("tabSize",4,function(e){Pi(e),Yn(e),mr(e)},!0),ns("lineSeparator",null,function(e,r){if(e.doc.lineSep=r){var i=[],o=e.doc.first;e.doc.iter(function(e){for(var t=0;;){var n=e.text.indexOf(r,t);if(-1==n)break;t=n+r.length,i.push(at(o,n))}o++});for(var t=i.length-1;0<=t;t--)bo(e.doc,r,i[t],at(i[t].line,i[t].ch+r.length))}}),ns("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b-\u200c\u200e\u200f\u2028\u2029\ufeff\ufff9-\ufffc]/g,function(e,t,n){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),n!=Ml&&e.refresh()}),ns("specialCharPlaceholder",ln,function(e){return e.refresh()},!0),ns("electricChars",!0),ns("inputStyle",h?"contenteditable":"textarea",function(){throw new Error("inputStyle can not (yet) be changed in a running editor")},!0),ns("spellcheck",!1,function(e,t){return e.getInputField().spellcheck=t},!0),ns("autocorrect",!1,function(e,t){return e.getInputField().autocorrect=t},!0),ns("autocapitalize",!1,function(e,t){return e.getInputField().autocapitalize=t},!0),ns("rtlMoveVisually",!f),ns("wholeLineUpdateBefore",!0),ns("theme","default",function(e){Tl(e),bi(e)},!0),ns("keyMap","default",function(e,t,n){var r=el(t),i=n!=Ml&&el(n);i&&i.detach&&i.detach(e,r),r.attach&&r.attach(e,i||null)}),ns("extraKeys",null),ns("configureMouse",null),ns("lineWrapping",!1,Dl,!0),ns("gutters",[],function(e,t){e.display.gutterSpecs=vi(t,e.options.lineNumbers),bi(e)},!0),ns("fixedGutter",!0,function(e,t){e.display.gutters.style.left=t?hr(e.display)+"px":"0",e.refresh()},!0),ns("coverGutterNextToScrollbar",!1,function(e){return Xr(e)},!0),ns("scrollbarStyle","native",function(e){$r(e),Xr(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)},!0),ns("lineNumbers",!1,function(e,t){e.display.gutterSpecs=vi(e.options.gutters,t),bi(e)},!0),ns("firstLineNumber",1,bi,!0),ns("lineNumberFormatter",function(e){return e},bi,!0),ns("showCursorWhenSelecting",!1,xr,!0),ns("resetSelectionOnContextMenu",!0),ns("lineWiseCopyCut",!0),ns("pasteLinesPerSelection",!0),ns("selectionsMayTouch",!1),ns("readOnly",!1,function(e,t){"nocursor"==t&&(Or(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)}),ns("screenReaderLabel",null,function(e,t){t=""===t?null:t,e.display.input.screenReaderLabelChanged(t)}),ns("disableInput",!1,function(e,t){t||e.display.input.reset()},!0),ns("dragDrop",!0,Ol),ns("allowDropFileTypes",null),ns("cursorBlinkRate",530),ns("cursorScrollMargin",0),ns("cursorHeight",1,xr,!0),ns("singleCursorHeightPerLine",!0,xr,!0),ns("workTime",100),ns("workDelay",100),ns("flattenSpans",!0,Pi,!0),ns("addModeClass",!1,Pi,!0),ns("pollInterval",100),ns("undoDepth",200,function(e,t){return e.doc.history.undoDepth=t}),ns("historyEventDelay",1250),ns("viewportMargin",10,function(e){return e.refresh()},!0),ns("maxHighlightLength",1e4,Pi,!0),ns("moveInputWithCursor",!0,function(e,t){t||e.display.input.resetPosition()}),ns("tabindex",null,function(e,t){return e.display.input.getField().tabIndex=t||""}),ns("autofocus",null),ns("direction","ltr",function(e,t){return e.doc.setDirection(t)},!0),ns("phrases",null),es=(Jl=Wl).optionHandlers,ts=Jl.helpers={},Jl.prototype={constructor:Jl,focus:function(){window.focus(),this.display.input.focus()},setOption:function(e,t){var n=this.options,r=n[e];n[e]==t&&"mode"!=e||(n[e]=t,es.hasOwnProperty(e)&&ii(this,es[e])(this,t,r),Se(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](el(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,n=0;n<t.length;++n)if(t[n]==e||t[n].name==e)return t.splice(n,1),!0},addOverlay:oi(function(e,t){var n=e.token?e:Jl.getMode(this.options,e);if(n.startState)throw new Error("Overlays may not be stateful.");!function(e,t,n){for(var r=0,i=n(t);r<e.length&&n(e[r])<=i;)r++;e.splice(r,0,t)}(this.state.overlays,{mode:n,modeSpec:e,opaque:t&&t.opaque,priority:t&&t.priority||0},function(e){return e.priority}),this.state.modeGen++,mr(this)}),removeOverlay:oi(function(e){for(var t=this.state.overlays,n=0;n<t.length;++n){var r=t[n].modeSpec;if(r==e||"string"==typeof e&&r.name==e)return t.splice(n,1),this.state.modeGen++,void mr(this)}}),indentLine:oi(function(e,t,n){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),lt(this.doc,e)&&Fl(this,e,t,n)}),indentSelection:oi(function(e){for(var t=this.doc.sel.ranges,n=-1,r=0;r<t.length;r++){var i=t[r];if(i.empty())i.head.line>n&&(Fl(this,i.head.line,e,!0),n=i.head.line,r==this.doc.sel.primIndex&&Er(this));else{for(var o=i.from(),l=i.to(),s=Math.max(n,o.line),n=Math.min(this.lastLine(),l.line-(l.ch?0:1))+1,a=s;a<n;++a)Fl(this,a,e);var u=this.doc.sel.ranges;0==o.ch&&t.length==u.length&&0<u[r].from().ch&&eo(this.doc,r,new Mi(o,u[r].to()),V)}}}),getTokenAt:function(e,t){return Tt(this,e,t)},getLineTokens:function(e,t){return Tt(this,at(e),t,!0)},getTokenTypeAt:function(e){e=gt(this.doc,e);var t,n=wt(this,et(this.doc,e.line)),r=0,i=(n.length-1)/2,o=e.ch;if(0==o)t=n[2];else for(;;){var l=r+i>>1;if((l?n[2*l-1]:0)>=o)i=l;else{if(!(n[2*l+1]<o)){t=n[2*l+2];break}r=1+l}}var s=t?t.indexOf("overlay "):-1;return s<0?t:0==s?null:t.slice(0,s-1)},getModeAt:function(e){var t=this.doc.mode;return t.innerMode?Jl.innerMode(t,this.getTokenAt(e).state).mode:t},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var n=[];if(!ts.hasOwnProperty(t))return n;var r=ts[t],i=this.getModeAt(e);if("string"==typeof i[t])r[i[t]]&&n.push(r[i[t]]);else if(i[t])for(var o=0;o<i[t].length;o++){var l=r[i[t][o]];l&&n.push(l)}else i.helperType&&r[i.helperType]?n.push(r[i.helperType]):r[i.name]&&n.push(r[i.name]);for(var s=0;s<r._global.length;s++){var a=r._global[s];a.pred(i,this)&&-1==B(n,a.val)&&n.push(a.val)}return n},getStateAfter:function(e,t){var n=this.doc;return xt(this,(e=pt(n,null==e?n.first+n.size-1:e))+1,t).state},cursorCoords:function(e,t){var n=this.doc.sel.primary(),r=null==e?n.head:"object"==typeof e?gt(this.doc,e):e?n.from():n.to();return er(this,r,t||"page")},charCoords:function(e,t){return Jn(this,gt(this.doc,e),t||"page")},coordsChar:function(e,t){return rr(this,(e=Qn(this,e,t||"page")).left,e.top)},lineAtHeight:function(e,t){return e=Qn(this,{top:e,left:0},t||"page").top,ot(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,n){var r,i=!1,o="number"==typeof e?(r=this.doc.first+this.doc.size-1,e<this.doc.first?e=this.doc.first:r<e&&(e=r,i=!0),et(this.doc,e)):e;return Zn(this,o,{top:0,left:0},t||"page",n||i).top+(i?this.doc.height-Zt(o):0)},defaultTextHeight:function(){return ar(this.display)},defaultCharWidth:function(){return ur(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,n,r,i){var o,l,s,a,u,c=this.display,h=(e=er(this,gt(this.doc,e))).bottom,d=e.left;t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),c.sizer.appendChild(t),"over"==r?h=e.top:"above"!=r&&"near"!=r||(o=Math.max(c.wrapper.clientHeight,this.doc.height),l=Math.max(c.sizer.clientWidth,c.lineSpace.clientWidth),("above"==r||e.bottom+t.offsetHeight>o)&&e.top>t.offsetHeight?h=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=o&&(h=e.bottom),d+t.offsetWidth>l&&(d=l-t.offsetWidth)),t.style.top=h+"px",t.style.left=t.style.right="","right"==i?(d=c.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?d=0:"middle"==i&&(d=(c.sizer.clientWidth-t.offsetWidth)/2),t.style.left=d+"px"),n&&(s=this,a={left:d,top:h,right:d+t.offsetWidth,bottom:h+t.offsetHeight},null!=(u=Fr(s,a)).scrollTop&&Br(s,u.scrollTop),null!=u.scrollLeft&&Ur(s,u.scrollLeft))},triggerOnKeyDown:oi(gl),triggerOnKeyPress:oi(vl),triggerOnKeyUp:ml,triggerOnMouseDown:oi(xl),execCommand:function(e){if(ll.hasOwnProperty(e))return ll[e].call(null,this)},triggerElectric:oi(function(e){zl(this,e)}),findPosH:function(e,t,n,r){var i=1;t<0&&(i=-1,t=-t);for(var o=gt(this.doc,e),l=0;l<t&&!(o=Vl(this.doc,o,i,n,r)).hitSide;++l);return o},moveH:oi(function(t,n){var r=this;this.extendSelectionsBy(function(e){return r.display.shift||r.doc.extend||e.empty()?Vl(r.doc,e.head,t,n,r.options.rtlMoveVisually):t<0?e.from():e.to()},j)}),deleteH:oi(function(n,r){var e=this.doc.sel,i=this.doc;e.somethingSelected()?i.replaceSelection("",null,"+delete"):tl(this,function(e){var t=Vl(i,e.head,n,r,!1);return n<0?{from:t,to:e.head}:{from:e.head,to:t}})}),findPosV:function(e,t,n,r){var i=1,o=r;t<0&&(i=-1,t=-t);for(var l=gt(this.doc,e),s=0;s<t;++s){var a=er(this,l,"div");if(null==o?o=a.left:a.left=o,(l=Kl(this,a,i,n)).hitSide)break}return l},moveV:oi(function(r,i){var o=this,l=this.doc,s=[],a=!this.display.shift&&!l.extend&&l.sel.somethingSelected();if(l.extendSelectionsBy(function(e){if(a)return r<0?e.from():e.to();var t=er(o,e.head,"div");null!=e.goalColumn&&(t.left=e.goalColumn),s.push(t.left);var n=Kl(o,t,r,i);return"page"==i&&e==l.sel.primary()&&Pr(o,Jn(o,n,"div").top-t.top),n},j),s.length)for(var e=0;e<l.sel.ranges.length;e++)l.sel.ranges[e].goalColumn=s[e]}),findWordAt:function(e){var t=et(this.doc,e.line).text,n=e.ch,r=e.ch;if(t){var i=this.getHelper(e,"wordChars");"before"!=e.sticky&&r!=t.length||!n?++r:--n;for(var o=t.charAt(n),l=te(o,i)?function(e){return te(e,i)}:/\s/.test(o)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!te(e)};0<n&&l(t.charAt(n-1));)--n;for(;r<t.length&&l(t.charAt(r));)++r}return new Mi(at(e.line,n),at(e.line,r))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||(((this.state.overwrite=!this.state.overwrite)?H:T)(this.display.cursorDiv,"CodeMirror-overwrite"),Se(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==W()},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:oi(function(e,t){Ir(this,e,t)}),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-Hn(this)-this.display.barHeight,width:e.scrollWidth-Hn(this)-this.display.barWidth,clientHeight:Pn(this),clientWidth:Fn(this)}},scrollIntoView:oi(function(e,t){var n,r;null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:at(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?(r=e,Rr(n=this),n.curOp.scrollToPos=r):zr(this,e.from,e.to,e.margin)}),setSize:oi(function(e,t){function n(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e}var r=this;null!=e&&(this.display.wrapper.style.width=n(e)),null!=t&&(this.display.wrapper.style.height=n(t)),this.options.lineWrapping&&Xn(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){vr(r,i,"widget");break}++i}),this.curOp.forceUpdate=!0,Se(this,"refresh",this)}),operation:function(e){return ri(this,e)},startOperation:function(){return Zr(this)},endOperation:function(){return Qr(this)},refresh:oi(function(){var e=this.display.cachedTextHeight;mr(this),this.curOp.forceUpdate=!0,Yn(this),Ir(this,this.doc.scrollLeft,this.doc.scrollTop),fi(this.display),(null==e||.5<Math.abs(e-ar(this.display))||this.options.lineWrapping)&&fr(this),Se(this,"refresh",this)}),swapDoc:oi(function(e){var t=this.doc;return t.cm=null,this.state.selectingText&&this.state.selectingText(),zi(this,e),Yn(this),this.display.input.reset(),Ir(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,mn(this,"swapDoc",this,t),t}),phrase:function(e){var t=this.options.phrases;return t&&Object.prototype.hasOwnProperty.call(t,e)?t[e]:e},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},Me(Jl),Jl.registerHelper=function(e,t,n){ts.hasOwnProperty(e)||(ts[e]=Jl[e]={_global:[]}),ts[e][t]=n},Jl.registerGlobalHelper=function(e,t,n,r){Jl.registerHelper(e,t,r),ts[e]._global.push({pred:n,val:r})};var rs,is="iter insert remove copy getEditor constructor".split(" ");for(var os in Po.prototype)Po.prototype.hasOwnProperty(os)&&B(is,os)<0&&(Wl.prototype[os]=function(e){return function(){return e.apply(this.doc,arguments)}}(Po.prototype[os]));return Me(Po),Wl.inputStyles={textarea:ql,contenteditable:jl},Wl.defineMode=function(e){Wl.defaults.mode||"null"==e||(Wl.defaults.mode=e),function(e,t){2<arguments.length&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Ke[e]=t}.apply(this,arguments)},Wl.defineMIME=function(e,t){je[e]=t},Wl.defineMode("null",function(){return{token:function(e){return e.skipToEnd()}}}),Wl.defineMIME("text/plain","null"),Wl.defineExtension=function(e,t){Wl.prototype[e]=t},Wl.defineDocExtension=function(e,t){Po.prototype[e]=t},Wl.fromTextArea=function(t,n){var e;function r(){t.value=s.getValue()}if((n=n?I(n):{}).value=t.value,!n.tabindex&&t.tabIndex&&(n.tabindex=t.tabIndex),!n.placeholder&&t.placeholder&&(n.placeholder=t.placeholder),null==n.autofocus&&(e=W(),n.autofocus=e==t||null!=t.getAttribute("autofocus")&&e==document.body),t.form&&(we(t.form,"submit",r),!n.leaveSubmitMethodAlone)){var i=t.form,o=i.submit;try{var l=i.submit=function(){r(),i.submit=o,i.submit(),i.submit=l}}catch(e){}}n.finishInit=function(e){e.save=r,e.getTextArea=function(){return t},e.toTextArea=function(){e.toTextArea=isNaN,r(),t.parentNode.removeChild(e.getWrapperElement()),t.style.display="",t.form&&(Ce(t.form,"submit",r),n.leaveSubmitMethodAlone||"function"!=typeof t.form.submit||(t.form.submit=o))}},t.style.display="none";var s=Wl(function(e){return t.parentNode.insertBefore(e,t.nextSibling)},n);return s},(rs=Wl).off=Ce,rs.on=we,rs.wheelEventPixels=Li,rs.Doc=Po,rs.splitLines=Be,rs.countColumn=R,rs.findColumn=X,rs.isWordChar=ee,rs.Pass=U,rs.signal=Se,rs.Line=en,rs.changeEnd=Oi,rs.scrollbarModel=_r,rs.Pos=at,rs.cmpPos=ut,rs.modes=Ke,rs.mimeModes=je,rs.resolveMode=Xe,rs.getMode=Ye,rs.modeExtensions=_e,rs.extendMode=$e,rs.copyState=qe,rs.startState=Qe,rs.innerMode=Ze,rs.commands=ll,rs.keyMap=Yo,rs.keyName=Jo,rs.isModifierKey=Zo,rs.lookupKey=qo,rs.normalizeKeyMap=$o,rs.StringStream=Je,rs.SharedTextMarker=Do,rs.TextMarker=Ao,rs.LineWidget=To,rs.e_preventDefault=Ne,rs.e_stopPropagation=Ae,rs.e_stop=De,rs.addClass=H,rs.contains=D,rs.rmClass=T,rs.keyNames=Vo,Wl.version="5.55.0",Wl});
