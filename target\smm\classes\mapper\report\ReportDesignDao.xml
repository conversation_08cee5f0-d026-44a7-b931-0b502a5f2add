<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.report.dao.ReportDesignDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sloth.modules.report.entity.ReportDesign" id="reportDesignMap">
        <result property="id" column="ID"/>
        <result property="name" column="NAME"/>
        <result property="params" column="PARAMS"/>
        <result property="content" column="CONTENT"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
    </resultMap>
    <select id="selectDesignPage" resultType="com.sloth.modules.report.entity.ReportDesign">
        select srd.*,su.show_name createUserName
        from sys_report_design srd
        left join sys_user su on srd.CREATE_USER_ID=su.user_id
    </select>


</mapper>