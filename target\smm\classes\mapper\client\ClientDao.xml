<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.client.dao.ClientDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.client.entity.Client" id="clientMap">
        <result property="id" column="ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="projectId" column="PROJECT_ID"/>
        <result property="stationId" column="STATION_ID"/>
        <result property="sn" column="SN"/>
        <result property="pwd" column="PWD"/>
        <result property="mac" column="MAC"/>
        <result property="ip" column="IP"/>
        <result property="com" column="COM"/>
        <result property="baudRate" column="BAUD_RATE"/>
        <result property="dataBit" column="DATA_BIT"/>
        <result property="verifyBit" column="VERIFY_BIT"/>
        <result property="stopBit" column="STOP_BIT"/>
        <result property="flowControl" column="FLOW_CONTROL"/>
        <result property="type" column="TYPE"/>
        <result property="startTime" column="START_TIME"/>
        <result property="endTime" column="END_TIME"/>
        <result property="status" column="STATUS"/>
    </resultMap>

    <select id="selectClientPage" resultType="com.ybkj.smm.modules.client.entity.Client">
        select
        client.id,
        client.CREATE_USER_ID,
        client.UPDATE_USER_ID,
        client.CREATE_TIME,
        client.UPDATE_TIME,
        client.PROJECT_ID,
        client.STATION_ID,
        client.SN,
        client.PWD,
        client.MAC,
        client.IP,
        client.COM,
        client.BAUD_RATE,
        client.DATA_BIT,
        client.VERIFY_BIT,
        client.STOP_BIT,
        client.UNIT,
        client.FLOW_CONTROL,
        client.TYPE,
        client.START_TIME,
        client.END_TIME,
        client.STATUS,
        client.STATION_NAME AS stationName,
        client.PROJECT_NAME AS projectName,
        client.HEARTBEAT_TIME,
        client.SECTION_NAME,
        client.PROGRAM_VERSION,
        client.APPLY_BEFORE_SUBMIT
        from
        smm_client_client client
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <select id="selectClientList" resultType="com.ybkj.smm.modules.client.entity.Client">
        select
        client.ID,
        client.PROJECT_ID AS projectId,
        client.PROJECT_NAME AS projectName,
        client.STATION_NAME AS stationName,
        client.SECTION_NAME AS sectionName
        from
        smm_client_client client
        <where>
            ${ew.sqlSegment}
        </where>
        ORDER BY client.CREATE_TIME DESC
    </select>
    <select id="selectClientPageByWater" resultType="com.ybkj.smm.modules.client.entity.Client">
        SELECT
        client.id,
        client.CREATE_USER_ID,
        client.UPDATE_USER_ID,
        client.CREATE_TIME,
        client.UPDATE_TIME,
        client.PROJECT_ID,
        client.STATION_ID,
        client.SN,
        client.PWD,
        client.MAC,
        client.IP,
        client.COM,
        client.BAUD_RATE,
        client.DATA_BIT,
        client.VERIFY_BIT,
        client.STOP_BIT,
        client.UNIT,
        client.FLOW_CONTROL,
        client.TYPE,
        client.START_TIME,
        client.END_TIME,
        client.STATUS,
        client.STATION_NAME AS stationName,
        client.PROJECT_NAME AS projectName,
        client.HEARTBEAT_TIME,
        client.SECTION_NAME,
        client.PROGRAM_VERSION,
        client.APPLY_BEFORE_SUBMIT
        FROM
        smm_client_client client
        LEFT JOIN
        sys_dept sd
        ON client.PROJECT_ID = sd.DEPT_ID AND sd.TYPE = 'sand' AND sd.DEL_FLAG = 0
        <where>
            <if test="projectName!=null and projectName!=''">
                client.PROJECT_NAME like concat('%',#{projectName},'%')
            </if>
            <if test="sectionName!=null and sectionName!=''">
                AND client.SECTION_NAME like concat('%',#{sectionName},'%')
            </if>
            <if test="stationName!=null and stationName!=''">
                AND client.STATION_NAME like concat('%',#{stationName},'%')
            </if>
            <if test="heartbeatTime != null and heartbeatTime == 'online'">
                AND HEARTBEAT_TIME &gt;= #{onlineTime}
            </if>
            <if test="heartbeatTime != null and heartbeatTime == 'offline'">
                AND HEARTBEAT_TIME != '' AND HEARTBEAT_TIME &lt; #{onlineTime}
            </if>
            <if test="heartbeatTime != null and heartbeatTime == 'unknown'">
                AND HEARTBEAT_TIME = ''
            </if>
            AND ((sd.DEPT_ID = #{deptId} OR sd.DEPT_ID IN (SELECT DEPT_ID FROM sys_dept WHERE DEL_FLAG = 0 AND DEPT_CODE LIKE CONCAT(#{deptCode},'%'))))
        </where>
        ORDER BY client.CREATE_TIME DESC,client.ID DESC
    </select>

</mapper>
