<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.project.dao.ProjectDeclareDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.project.entity.ProjectDeclare" id="projectDeclareMap">
        <result property="deptId" column="DEPT_ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="deptCode" column="DEPT_CODE"/>
        <result property="name" column="NAME"/>
        <result property="sectionName" column="SECTION_NAME"/>
        <result property="areaName" column="AREA_NAME"/>
        <result property="areaCode" column="AREA_CODE"/>
        <result property="type" column="TYPE"/>
        <result property="parentId" column="PARENT_ID"/>
        <result property="totalYield" column="TOTAL_YIELD"/>
        <result property="licenseNo" column="LICENSE_NO"/>
        <result property="leaderName" column="LEADER_NAME"/>
        <result property="contact" column="CONTACT"/>
        <result property="deleted" column="DELETED"/>
        <result property="investment" column="INVESTMENT"/>
        <result property="reverLength" column="REVER_LENGTH"/>
        <result property="complaintMobile" column="COMPLAINT_MOBILE"/>
        <result property="queryMobile" column="QUERY_MOBILE"/>
        <result property="auditStatus" column="AUDIT_STATUS"/>
        <result property="version" column="VERSION"/>
    </resultMap>
    <select id="page" resultType="com.ybkj.smm.modules.project.entity.ProjectDeclare">
        select
        project.DEPT_ID,
        project.CREATE_TIME,
        project.DEPT_CODE,
        project.NAME,
        project.SECTION_NAME,
        project.AREA_NAME,
        project.AREA_CODE,
        project.TYPE,
        project.PARENT_ID,
        project.TOTAL_YIELD,
        project.LICENSE_NO,
        project.LEADER_NAME,
        project.CONTACT,
        project.INVESTMENT,
        project.REVER_LENGTH,
        project.COMPLAINT_MOBILE,
        project.QUERY_MOBILE,
        project.AUDIT_STATUS,
        project.VERSION,
        project.VIDEO,
        project.SPRAY,
        project.BILLBOARD,
        project.SUPERVISOR,
        project.VIDEO_CAUSE,
        project.SPRAY_CAUSE,
        project.BILLBOARD_CAUSE,
        project.SUPERVISOR_CAUSE,
        project.DEPARTMENT_AUDIT_TIME,
        dept.NAME AS deptName,
        dept.DEPT_CODE AS deptCode
        from
        smm_project_project_declare project
        left join sys_dept dept on project.PARENT_ID = dept.DEPT_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="getDivisionCodeByAreaCode" resultType="com.ybkj.smm.modules.project.dto.DeptDivisionCodeDto">
        SELECT division_code,department_code FROM sys_dept WHERE AREA_CODE = #{areaCode} AND TYPE = 'dept' AND DEL_FLAG = 0
    </select>


</mapper>
