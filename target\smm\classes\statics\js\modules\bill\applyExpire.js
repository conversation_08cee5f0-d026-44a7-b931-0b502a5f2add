																																													
var vm = new Vue({
	el:'#app',
	mixins: [basicTableMixin],
	data:{
		showList: true,
		title: null,
		drawer:false,
		q:{},
		applyExpire: {}
	},
	methods: {
		add: function(){
			vm.title = "新增";
			vm.applyExpire = {};
			vm.$nextTick(function () {
				vm.showList = false;
			})
		},
		update: function (id) {
			//var id = getSelectedRow();
			if(id == null){
				return ;
			}
			vm.showList = false;
            vm.title = "修改";
            vm.getInfo(id)
		},
		saveOrUpdate: function (event) {
		    if(!validateForm("frm")){
		        return;
			}
			var url = vm.applyExpire.id == null ? "bill/applyExpire/save" : "bill/applyExpire/update";
			var that=this;
			this.$http.postWithShade(baseURL + url, this.applyExpire)
					.then(function (r) {
						alert("操作成功", function () {
							that.reload();
						});
					});
		},
		del: function (event) {
			var ids = this.$refs.applyExpireTable.getSelectRowKeys();
			if(ids == null){
				alert('请选择一条记录');
				return ;
			}
			var that=this;
			confirm('确定要删除选中的 ' + ids.length + ' 条记录？', function () {
				that.$http.post(baseURL + "bill/applyExpire/delete", ids)
						.then(function (r) {
							alert("操作成功", function () {
								that.reload();
							});
						});
			});
		},
		delOne: function (id) {
			var that=this;
			confirm('确定要删除选中的记录？', function(){
				that.$http.post(baseURL + "bill/applyExpire/delete/" +id)
						.then(function (r) {
							alert("操作成功", function () {
								that.reload();
							});
						});
			});
        },
		getInfo: function(id){
			var that=this
			this.$http.post(baseURL + "bill/applyExpire/info/" +id)
					.then(function (r) {
						that.applyExpire = r.applyExpire;
					});
		},
		reload: function (event) {
			vm.showList = true;
			this.$refs.applyExpireTable.search(this.q, event === 'query');
            $(".error-label").remove();
		}
	},
});
