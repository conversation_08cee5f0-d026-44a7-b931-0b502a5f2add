<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.bill.dao.ApplyDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.bill.entity.Apply" id="applyMap">
        <result property="id" column="ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="projectId" column="PROJECT_ID"/>
        <result property="carNumber" column="CAR_NUMBER"/>
        <result property="carCompany" column="CAR_COMPANY"/>
        <result property="carBrand" column="CAR_BRAND"/>
        <result property="carOwnerName" column="CAR_OWNER_NAME"/>
        <result property="carKerbWeight" column="CAR_KERB_WEIGHT"/>
        <result property="carMaxPayload" column="CAR_MAX_PAYLOAD"/>
        <result property="destination" column="DESTINATION"/>
        <result property="destLongitude" column="DEST_LONGITUDE"/>
        <result property="destLatitude" column="DEST_LATITUDE"/>
        <result property="estimatedArrivalTime" column="ESTIMATED_ARRIVAL_TIME"/>
        <result property="destLeader" column="DEST_LEADER"/>
        <result property="destLeaderMobile" column="DEST_LEADER_MOBILE"/>
        <result property="status" column="STATUS"/>
    </resultMap>

    <select id="selectApplyPage" resultType="com.ybkj.smm.modules.bill.entity.Apply">
        select
        appl.ID,
        appl.CREATE_USER_ID,
        appl.UPDATE_USER_ID,
        appl.CREATE_TIME,
        appl.UPDATE_TIME,
        appl.PROJECT_ID,
        appl.CAR_NUMBER,
        appl.CAR_COMPANY,
        appl.CAR_BRAND,
        appl.CAR_OWNER_NAME,
        appl.CAR_KERB_WEIGHT,
        appl.CAR_MAX_PAYLOAD,
        appl.DESTINATION,
        appl.DEST_LONGITUDE,
        appl.DEST_LATITUDE,
        appl.ESTIMATED_ARRIVAL_TIME,
        appl.DEST_LEADER,
        appl.DEST_LEADER_MOBILE,
        appl.STATUS,
        project.NAME projectName,
        project.SECTION_NAME sectionName,
        user.SHOW_NAME driverName,
        user.mobile driverMobile
        from
        smm_bill_apply appl
        left join sys_user user on appl.CREATE_USER_ID = user.USER_ID
        left join smm_project_project project on appl.PROJECT_ID = project.DEPT_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="hasBillAuth" resultType="java.lang.Boolean">
        select exists(select apply.ID
                      from smm_bill_apply apply
                               join smm_project_project project
                                    on apply.PROJECT_ID = project.DEPT_ID and apply.ID = #{applyId}
                               join sys_user user
                                    on user.USER_ID = #{userId} and project.DEPT_CODE like CONCAT(user.DEPT_CODE, "%"))
    </select>
    <select id="selectMaApplyPage" resultType="com.ybkj.smm.modules.bill.dto.ApplyResultDto">
        select
        appl.ID,
        appl.CREATE_TIME,
        appl.CAR_OWNER_NAME,
        appl.CAR_NUMBER,
        appl.DESTINATION,
        appl.STATUS,
        appl.PROJECT_ID,
        user.SHOW_NAME driverName,
        user.mobile driverMobile,
        project.STATUS projectStatus
        from
        smm_bill_apply appl
        left join smm_project_project project on appl.PROJECT_ID=project.DEPT_ID
        left join sys_user user on appl.CREATE_USER_ID = user.USER_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <select id="selectAnnouncement" resultType="com.ybkj.smm.modules.bill.entity.AnNounCement">
        SELECT a.ID AS id,a.VALUE AS announcement,a.UPDATE_TIME AS updateTime,b.ANNOUNCEMENT_STATUS AS status FROM sys_dict a LEFT JOIN sys_user b ON b.USER_ID = #{userId} WHERE a.NAME = '系统公告'
    </select>

    <update id="updateAnnouncementStatus">
        UPDATE sys_user SET ANNOUNCEMENT_STATUS = #{status} WHERE USER_ID = #{userId};
    </update>

    <update id="updateAnnouncementStatus2">
        UPDATE sys_user SET ANNOUNCEMENT_STATUS = 'false'
    </update>
</mapper>
