<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.project.dao.BillAdditionRecordDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.project.entity.BillAdditionRecord" id="billAdditionRecordMap">
        <result property="id" column="ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="projectId" column="PROJECT_ID"/>
        <result property="addCount" column="ADD_COUNT"/>
        <result property="deleteUserId" column="DELETE_USER_ID"/>
        <result property="deleteTime" column="DELETE_TIME"/>
        <result property="deleted" column="DELETED"/>
    </resultMap>
    <select id="selectProjectPage" resultType="com.ybkj.smm.modules.project.entity.Project">
        SELECT
        project.DEPT_ID,
        project.CREATE_USER_ID,
        project.CREATE_TIME,
        project.UPDATE_USER_ID,
        project.NAME,
        project.SECTION_NAME,
        project.TYPE,
        project.AREA_NAME,
        project.LEADER_NAME,
        IFNULL(project.DAILY_BILL_COUNT,0) dailyBillCount,
        IFNULL(billAdditionRecord.ADD_COUNT,0) AS addCount,
        dept.NAME AS deptName
        FROM
        smm_project_project project
        LEFT JOIN sys_dept dept ON project.PARENT_ID = dept.DEPT_ID
        LEFT JOIN (
            select SUM(ADD_COUNT) ADD_COUNT ,PROJECT_ID
            from smm_project_bill_addition_record
            where
                CREATE_TIME like '${currentDate}%' and DELETED=false
                group by PROJECT_ID
        ) billAdditionRecord ON project.DEPT_ID = billAdditionRecord.PROJECT_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="selectBillAdditionRecordPage"
            resultType="com.ybkj.smm.modules.project.entity.BillAdditionRecord">
        select
        billAdditionRecord.ID,
        billAdditionRecord.CREATE_TIME,
        billAdditionRecord.CREATE_USER_ID,
        billAdditionRecord.PROJECT_ID,
        billAdditionRecord.ADD_COUNT,
        user.SHOW_NAME as showName
        from
        smm_project_bill_addition_record billAdditionRecord
        LEFT JOIN sys_user user ON billAdditionRecord.CREATE_USER_ID = user.USER_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>


</mapper>
