<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.statement.dao.MasterStatementDao">
    <update id="deleteByReportId">
        UPDATE smm_report_person
        SET DELETED = 1
        WHERE REPORT_ID = #{id}
    </update>
    <select id="queryAllByReportId" resultType="com.ybkj.smm.modules.statement.entity.ReportPersonVo">
        SELECT * FROM smm_report_person WHERE REPORT_ID = #{id} AND DELETED = 0
    </select>
</mapper>
