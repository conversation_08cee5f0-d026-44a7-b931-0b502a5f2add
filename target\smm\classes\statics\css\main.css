html { overflow-x:hidden; }
/*body{
    padding: 10px
}*/
.content-header {
	position: relative;
	padding: 0 0 3px 8px
}

.content-header>.breadcrumb {
    position: relative;
    top: 0;
    right: 0;
    float: none;
    margin-top: 0px;
    padding-left: 10px;
    background: #ecf0f5;
}

.main-footer {
    padding: 7px;
    color: #444;
    border-top: 1px solid #eee;
}

[v-cloak] {
  display: none;
}

.grid-btn .form-group{
	margin-bottom:12px;
}
.grid-btn .btn{
	margin-right:12px;
}
.pointer{cursor: pointer;}

.ml-10 { margin-left:0 !important; }
@media (min-width: 768px) {
	.ml-10 { margin-left:10px !important; }
}
tbody > tr > th {font-weight: normal; }
.panel .table { margin:0 0; }
.panel .pagination { margin:0; }
.panel-default>.panel-heading {background-color: #f5f5f5;}
.row{
	border-top: 1px solid #ddd;
	margin:0;
	padding:20px 2px 0px 2px;
}
.col-xs-6{padding-left: 0px;padding-right: 0px;}
.form-horizontal .form-group {margin-left:0px;margin-right:0px;}
.form-horizontal{
	width:550px;padding-top:20px;
}
.form-medium-horizontal .form-medium-group {margin-left:0px;margin-right:0px;}
.form-medium-horizontal{
	width:768px;padding-top:20px;
}
th{
    text-align: center;
}
td{
    text-align: center;
}
.checkbox{
    display: inline;
}

.panel-left{
    min-height: 300px;
    height: 500px
}
.panel-right{
    min-height: 300px;
    height: 500px
}

.ztree-container{
    display: none;padding:10px;position: absolute; z-index: 99999;background: #f0f6e4;
}
/**
*页面校验样式
**/
.normal-label{
	display:none;
}

.app-container {
    width: calc(100% - 20px);
}
/**
 * 加载样式
 */

#loadingDiv .el-loading-mask {
    display: none;
    position: absolute;
    z-index: 2000;
    background-color: rgba(255, 255, 255, .9);
    margin: 0;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    -webkit-transition: opacity .3s;
    transition: opacity .3s
}

#loadingDiv .el-loading-mask.is-fullscreen {
    position: fixed
}

#loadingDiv .el-loading-mask.is-fullscreen .el-loading-spinner {
    margin-top: -25px
}

#loadingDiv .el-loading-mask.is-fullscreen .el-loading-spinner .circular {
    height: 50px;
    width: 50px
}

#loadingDiv .el-loading-spinner {
    color: #f3f0ed;
    top: 50%;
    margin-top: -21px;
    width: 100%;
    text-align: center;
    position: absolute
}

#loadingDiv .el-loading-spinner .el-loading-text {
    color: #f3f0ed;
    margin: 3px 0;
    font-size: 20px
}
@font-face {
    font-family: element-icons;
    src: url(../fonts/element-icons.woff) format("woff"), url(../fonts/element-icons.ttf) format("truetype");
    font-weight: 400;
    font-style: normal
}
#loadingDiv [class*=" el-icon-"], [class^=el-icon-] {
    font-family: element-icons !important;
    speak: none;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    vertical-align: baseline;
    display: inline-block;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

#loadingDiv .el-icon-loading:before {
    content: "\e61e"
}

#loadingDiv .el-icon-loading {
    font-size: 80px;
    -webkit-animation: rotating 2s linear infinite;
    animation: rotating 2s linear infinite
}

@-webkit-keyframes rotating {
    0% {
        -webkit-transform: rotateZ(0);
        transform: rotateZ(0)
    }
    100% {
        -webkit-transform: rotateZ(360deg);
        transform: rotateZ(360deg)
    }
}
@keyframes rotating {
    0% {
        -webkit-transform: rotateZ(0);
        transform: rotateZ(0)
    }
    100% {
        -webkit-transform: rotateZ(360deg);
        transform: rotateZ(360deg)
    }
}
.login-page{
    /*background: url("../images/changecolor.png")  0px 0px;*/
    background: linear-gradient(top, #3C8DBC, white);
    background: -ms-linear-gradient(top, #3C8DBC, white);
    background: -webkit-linear-gradient(top, #3C8DBC, white);
    background: -moz-linear-gradient(top, #3C8DBC, white);
    padding: 7% 0px 15% 0px;
}
.login-logo b{
    color:#fff
}
.app-warning {
    text-align: center;
    line-height: 30px;
    width: 100%;
    height: 30px;
    position: fixed;
    top: 0;
    opacity: 0.75;
    color: red;
    background-color: #f7ecb5;
    z-index: 99999;
    display: none;
}
.center{text-align: center;}

/*重写elementUi data-time-picker样式*/
.el-date-editor.el-input {
    width: 100%;
}
.el-input__inner{
    border-color: rgb(204,204,204);
    height: 34px;
}
.el-input__inner:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
}
.el-input__inner:read-only{
    color:#555;
    background-color: #eee;
}
.el-date-editor.el-input.is-disabled .el-input__inner{
    color:#555;
    background-color: #eee;
    border: 1px solid #ccc;
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
}
.el-input__inner::-webkit-input-placeholder{
    color:#999;
}
input[type='file'].el-upload__input{
    display: none;
}
.el-drawer__body{
    overflow: auto;
}
/*重写el-datetimepicker相关样式*/
.el-scrollbar__wrap{
    margin-right: -17px;
}
.my-date-time-picker .el-time-panel__content::before,.my-date-time-picker .el-time-panel__content::after{
    top:55%;
}
.my-date-time-picker{
    width: 232px;
    line-height: 24px;
}
.my-date-time-picker .el-picker-panel__content{
    width: 200px;
    margin-top: 5px;
    margin-bottom: 10px;
}
.my-date-time-picker td, .my-date-time-picker td div{
    height: 20px;
}
.my-date-time-picker .el-date-picker__header{
    margin-bottom: 5px;
    margin-top: 5px;
}
.my-date-time-picker .el-date-picker__header-label{
    font-size: 14px;
}
.my-date-time-picker .el-picker-panel__icon-btn{
    margin-top: 5px;
}
.my-date-time-picker .el-time-panel{
    width: 140px;
}
.el-date-editor--daterange.el-input__inner{
    width: 100%;
}
.form-inline .el-date-editor--daterange.el-input__inner{
    width: auto;
    max-width: 250px
}
.form-edit{
    padding: 10px;
}
.table-edit>tbody>tr>th{
    background-color: #f5f5f5;
    text-align:right;
    vertical-align: middle;
    width: 15%;
}
.table-edit>tbody>tr>td{
    text-align:left;
    width: 35%;
}
.font-size-18{
   font-size: 18px;
}
.z-margin-t15{
    margin-top: 15px;
}

.z-padding-l0{
    padding-left: 0;
}

.main-sidebar .sidebar .sidebar-menu>li>a {
    font-size: 15px;
}

.main-sidebar .sidebar .sidebar-menu>li>ul>li>a{
    font-size: 14px;
    padding-left: 25px;
}

/*重写el-select样式*/
.el-select{
    width: 100%;
}
.el-select .el-input__inner:read-only{
    color:#555;
    background-color: #fff;
}

.el-select .el-input__inner:disabled{
    color:#555;
    background-color: #eee;
}
@media(min-width: 768px){
    .form-inline .el-select{
        width: 196px;
    }
}

/*隐藏el-upload 上传按钮的辅助类*/
.hidden-upload .el-upload{
    display:none;
}

/*添加一组.5样式*/

.col-sm-0_5,.col-sm-1_5,.col-sm-2_5,.col-sm-3_5,.col-sm-4_5,.col-sm-5_5,.col-sm-6_5,.col-sm-7_5,.col-sm-8_5,.col-sm-9_5,.col-sm-10_5,.col-sm-11_5 {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
}

@media (min-width:768px) {
    .col-sm-0_5,.col-sm-1_5,.col-sm-2_5,.col-sm-3_5,.col-sm-4_5,.col-sm-5_5,.col-sm-6_5,.col-sm-7_5,.col-sm-8_5,.col-sm-9_5,.col-sm-10_5,.col-sm-11_5 {
        float: left;
    }
    .col-sm-11_5 {
        width: 95.833333%;
    }
    .col-sm-10_5 {
        width: 87.5%;
    }
    .col-sm-9_5 {
        width: 79.166667%;
    }
    .col-sm-8_5 {
        width: 70.833333%;
    }
    .col-sm-7_5 {
        width: 62.5%;
    }
    .col-sm-6_5 {
        width: 54.166667%;
    }
    .col-sm-5_5 {
        width: 45.833333%;
    }
    .col-sm-4_5 {
        width: 37.5%;
    }
    .col-sm-3_5 {
        width: 29.166667%;
    }
    .col-sm-2_5 {
        width: 20.833333%;
    }
    .col-sm-1_5 {
        width: 12.5%;
    }
    .col-sm-0_5 {
        width: 4.166667%;
    }
}

/*搜索部分控件宽度一致*/
@media (min-width: 768px) {
    .grid-btn select.form-control, .grid-btn .el-date-editor, .grid-btn input.form-control, .grid-btn .el-select {
        width: 196px;
    }
}

@media (min-width: 768px) {
    .grid-btn .input-group {
        width: 196px;
    }
}

/*登录页底部样式*/
.login-footer {
    z-index: 100;
    position: fixed;
    height: 40px;
    width: 100%;
    bottom: 0;
    text-align: center;
}

.login-footer span {
    color: #bbb;
}

.login-footer a span:hover {
    color: #000;
}

/* el-table 样式*/
.el-table, .el-table thead {
    color: #333;
}
/*左侧固定列*/
.el-table .el-table__fixed{
    height: 100% !important;
    bottom: 18px;
}
/*右侧固定列*/
.el-table .el-table__fixed-right{
    height: 100% !important;
    bottom: 18px;
}
