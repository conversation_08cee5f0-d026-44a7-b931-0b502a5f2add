<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.sys.dao.SysDictDao">
    <select id="queryParentDdList" resultType="com.sloth.modules.sys.entity.SysDictEntity">
        select * from sys_dict
                  where id in (select distinct parent_id from sys_dict) order by order_num,create_time
    </select>
    <select id="listWithDeleted" resultType="com.sloth.modules.sys.entity.SysDictEntity">
        select * from sys_dict
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

</mapper>