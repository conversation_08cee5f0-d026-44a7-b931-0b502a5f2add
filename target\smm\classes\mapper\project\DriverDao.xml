<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.project.dao.DriverDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.project.entity.Driver" id="driverMap">
        <result property="id" column="ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="projectId" column="PROJECT_ID"/>
        <result property="name" column="NAME"/>
        <result property="mobile" column="MOBILE"/>
        <result property="registered" column="REGISTERED"/>
    </resultMap>
    <select id="selectDriverPage" resultType="com.ybkj.smm.modules.project.entity.Driver">
        SELECT
        driver.ID,
        driver.CREATE_TIME,
        driver.PROJECT_ID,
        driver.NAME,
        driver.MOBILE,
        driver.REGISTERED,
        user.SHOW_NAME AS createUserName,
        project.NAME AS projectName,
        project.SECTION_NAME AS sectionName
        FROM smm_project_driver driver
        LEFT JOIN smm_project_project project ON driver.PROJECT_ID = project.DEPT_ID
        LEFT JOIN sys_user user ON driver.CREATE_USER_ID = user.USER_ID
        <where>
            <if test="(name != null and name != '')">
                (
                driver.NAME LIKE CONCAT('%', #{name}, '%')
                OR driver.MOBILE LIKE CONCAT('%', #{name}, '%')
                )
            </if>
            <if test="ew1 != null and ew1.sqlSegment != null and ew1.sqlSegment != ''">
                <choose>
                    <when test="name != null and name != ''">
                        AND ${ew1.sqlSegment}
                    </when>
                </choose>
            </if>

            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                OR ${ew.sqlSegment}
            </if>
        </where>
    </select>
    <select id="selectDriverCount" resultType="com.ybkj.smm.modules.sys.query.DriverDataQuery">
        select
            project.AREA_NAME AS areaName,
            count(driver.ID) AS driverCount
        from smm_project_driver driver
        left join smm_project_project project on driver.PROJECT_ID = project.DEPT_ID
        LEFT JOIN sys_user user on driver.CREATE_USER_ID = user.USER_ID
        <where>
            ${ew.sqlSegment}
        </where>
        AND project.DELETED = '0'
        GROUP BY project.AREA_NAME

    </select>

    <select id="listProjectByMobile" resultType="com.ybkj.smm.modules.project.entity.Project">
        SELECT
        project.NAME,
        project.DEPT_ID,
        project.SECTION_NAME,
        project.STATUS
        FROM
        smm_project_driver driver
        LEFT JOIN smm_project_project project ON project.DEPT_ID = driver.PROJECT_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <select id="selectWebDriverPage" resultType="com.ybkj.smm.modules.project.entity.Driver">
        select
        driver.ID,
        driver.CREATE_TIME,
        driver.PROJECT_ID,
        driver.NAME,
        driver.MOBILE,
        driver.REGISTERED,
        user.SHOW_NAME createUserName,
        project.NAME projectName,
        project.SECTION_NAME sectionName
        from smm_project_driver driver
        left join smm_project_project project on driver.PROJECT_ID = project.DEPT_ID
        LEFT JOIN sys_user user on driver.CREATE_USER_ID = user.USER_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>


</mapper>
