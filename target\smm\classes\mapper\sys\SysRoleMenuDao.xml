<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.sys.dao.SysRoleMenuDao">
    <insert id="insertBatch">
		insert into sys_role_menu(id, role_id, menu_id) values
		<foreach collection="entityList" item="item" separator=",">
			(#{item.id},#{item.roleId},#{item.menuId})
		</foreach>
	</insert>
    <select id="queryMenuIdList" resultType="String">
		select menu_id from sys_role_menu where role_id = #{value}
	</select>

	<delete id="deleteBatch">
		delete from sys_role_menu where role_id in
		<foreach item="roleId" collection="array" open="(" separator="," close=")">
			#{roleId}
		</foreach>
	</delete>

</mapper>