<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ybkj.smm.modules.project.dao.CarDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ybkj.smm.modules.project.entity.Car" id="carMap">
        <result property="id" column="ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="updateUserId" column="UPDATE_USER_ID"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="company" column="COMPANY"/>
        <result property="number" column="NUMBER"/>
        <result property="brand" column="BRAND"/>
        <result property="ownerName" column="OWNER_NAME"/>
        <result property="kerbWeight" column="KERB_WEIGHT"/>
        <result property="maxPayload" column="MAX_PAYLOAD"/>
        <result property="times" column="TIMES"/>
        <result property="inputType" column="INPUT_TYPE"/>
    </resultMap>

    <select id="selectCarPage" resultType="com.ybkj.smm.modules.project.entity.Car">
        select
        car.id,
        car.CREATE_USER_ID,
        car.UPDATE_USER_ID,
        car.CREATE_TIME,
        car.UPDATE_TIME,
        car.COMPANY,
        car.NUMBER,
        car.BRAND,
        car.OWNER_NAME,
        car.KERB_WEIGHT,
        car.MAX_PAYLOAD,
        car.TIMES,
        car.INPUT_TYPE,
        car.TYPE,
        car.MOBILE,
        user.SHOW_NAME driverName,
        user.MOBILE driverMobile
        from
        smm_project_car car
        left join sys_user user on car.CREATE_USER_ID = user.USER_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="selectCarList" resultType="com.ybkj.smm.modules.project.entity.Car">
        select
        car.id,
        car.CREATE_USER_ID,
        car.UPDATE_USER_ID,
        car.CREATE_TIME,
        car.UPDATE_TIME,
        car.COMPANY,
        car.NUMBER,
        car.BRAND,
        car.OWNER_NAME,
        car.KERB_WEIGHT,
        car.MAX_PAYLOAD,
        car.TIMES,
        car.INPUT_TYPE,
        car.TYPE,
        car.MOBILE,
        user.SHOW_NAME driverName,
        user.MOBILE driverMobile
        from
        smm_project_car car
        left join sys_user user on car.CREATE_USER_ID = user.USER_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="selectCarData" resultType="com.ybkj.smm.modules.project.entity.Car"
            parameterType="java.lang.String">
        SELECT * FROM smm_project_car WHERE NUMBER = #{number} AND STATUS = 0
    </select>
    <select id="selectCarByUserId" resultType="com.ybkj.smm.modules.project.entity.Car"
            parameterType="java.lang.String">
        SELECT * FROM smm_project_car a LEFT JOIN sys_user b ON a.MOBILE = b.MOBILE WHERE b.USER_ID = #{userId}
    </select>
    <select id="getCarDataByUserId" resultType="com.ybkj.smm.modules.project.entity.Car"
            parameterType="java.lang.String">
        SELECT * FROM smm_project_car car
            LEFT JOIN sys_user user
                 ON user.MOBILE = car.MOBILE
                 WHERE user.USER_ID = #{userId}
                     AND DEL_FLAG = 0
                     AND USER_TYPE = 'driver'
    </select>
    <select id="getCarDataByNumber" resultType="com.ybkj.smm.modules.project.entity.Car"
            parameterType="java.lang.String">
        SELECT * FROM smm_project_car WHERE NUMBER = #{number}
    </select>

</mapper>
