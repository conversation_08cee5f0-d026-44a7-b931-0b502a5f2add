/**
 *<li>功能描述：对webuploader插件进行二次封装</li>
 * 主要参数说明：
 *             auto:默认true，选择文件后是否自动开始上传
 *             saveUrl:保存文件地址
 *             deleteUrl:删除文件地址
 *             fileType:默认 img，上传文件类型，图片、视频还是文件，img/video/file，决定view的显示方式
 *             fileNumLimit：队列允许添加文件数量
 *             hiddenInputClass:隐藏input的class属性
 *             currCountInputId:用于计数的隐藏inputId
 * @author: <EMAIL>
 * @date:  2019/2/18 9:44
 */
(function ($, window) {
    function randomNum() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    }

    function initWebUpload(item, options) {

        if (!WebUploader.Uploader.support()) {
            var error = "上传控件不支持您的浏览器！请尝试升级flash版本或者使用Chrome引擎的浏览器。<a target='_blank' href='http://se.360.cn'>下载页面</a>";
            if (window.console) {
                window.console.log(error);
            }
            $(item).text(error);
            return;
        }

        //创建默认参数
        var defaults = {
            auto:true,
            hiddenInputId: "uploadifyHiddenInputId", // input hidden id
            hiddenInputClass:"hiddenInput",
            onAllComplete: function (event) { }, // 当所有file都上传后执行的回调函数
            onComplete: function (event) { },// 每上传一个file的回调函数
            innerOptions: {},
            fileNumLimit: 1,//验证文件总数量, 超出则不允许加入队列
            fileSizeLimit: undefined,//验证文件总大小是否超出限制, 超出则不允许加入队列。
            fileSingleSizeLimit: 100*1024*1024,//验证单个文件大小是否超出限制, 超出则不允许加入队列 100MB
            PostbackHold: false,
            saveUrl:'',//保存文件地址
            deleteUrl:'',//删除文件地址
            fileType:'img',//上传文件类型，图片还是文件，img/file，决定view的显示方式
            thumbnailWidth:100,//缩略图宽
            thumbnailHeight:100,//缩略图高
            currCountInputId:undefined,//存放当前文件数的input id前缀,多文件上传必须设置此项
        };

        var opts = $.extend(defaults, options);
        var target = $(item);//容器
        var pickerid = (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
        var uploaderStrdiv = '<div class="webuploader">';
        var pickerName="";
        var listNum=0;
        if(opts.fileType=="img"){
            pickerName="选择图片";
        }else{
            pickerName="选择文件";
        }
        if (opts.auto) {
            uploaderStrdiv =
                '<div id="Uploadthelist" class="uploader-list"></div>' +
                '<div class="btns">' +
                '<div id="' + pickerid + '" class="picker">'+ pickerName +'</div>' +
                '</div>'
        } else {
            uploaderStrdiv =
                '<div  class="uploader-list"></div>' +
                '<div class="btns">' +
                '<div id="' + pickerid + '" class="picker">'+ pickerName +'</div>' +
                '<button class="btn btn-default webuploadbtn">开始上传</button>' +
                '</div>'
        }
        uploaderStrdiv += '<div style="display:none" class="UploadhiddenInput" >';
        if(opts.currCountInputId!=undefined){
            uploaderStrdiv += '<input type="text" id="'+opts.currCountInputId+'" value="0" />';
        }
        uploaderStrdiv += '</div>';
        uploaderStrdiv+='</div>';
        target.append(uploaderStrdiv);

        var $list = target.find('.uploader-list'),
            $btn = target.find('.webuploadbtn'),//手动上传按钮备用
            state = 'pending',
            $hiddenInput = target.find('.UploadhiddenInput'),
            uploader;

        var webuploaderbaseoptions = {

            // swf文件路径
            swf: baseURL + '/statics/plugins/webuploader/Uploader.swf',
            // 文件接收服务端。
            server:  baseURL + opts.saveUrl,
            deleteServer:baseURL+opts.deleteUrl,
            // 选择文件的按钮。可选。
            // 内部根据当前运行是创建，可能是input元素，也可能是flash.
            pick: '#' + pickerid,
            //不压缩image, 默认如果是jpeg，文件上传前会压缩一把再上传！
            resize: false,
            fileNumLimit: opts.fileNumLimit,
            fileSizeLimit: opts.fileSizeLimit,
            fileSingleSizeLimit: opts.fileSingleSizeLimit,
        };
        var webuploaderextraoptions={};
        if(opts.fileType=='img'){//图片类型，设置图片允许格式
            webuploaderextraoptions={
                accept: {
                    title: 'Images',
                    extensions: 'gif,jpg,jpeg,bmp,png',
                    mimeTypes: 'image/*'
                },
            }
        }else if(opts.fileType=='video'){
            webuploaderextraoptions={
                accept: {
                    title: '',
                    extensions: 'mp4',
                    mimeTypes: 'video/mp4'
                },
            }
        }else if (opts.fileType == 'file') {
            webuploaderextraoptions={
                accept: {
                    title: '',
                    extensions: 'doc,docx,xls,xlsx,rar,zip,ppt,pptx,pdf,txt',
                    mimeTypes: 'application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,' +
                        'application/vnd.ms-excel,application/x-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,' +
                        'application/x-zip-compressed,' +
                        'application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.presentationml.presentation,' +
                        'application/pdf,text/plain'
                },
            }
        }
        var webuploaderoptions=$.extend(webuploaderbaseoptions,webuploaderextraoptions,opts.innerOptions);
        var uploader = WebUploader.create(webuploaderoptions);
        uploader.on('fileQueued', function (file) {//队列时间
            if(opts.fileType=="img"){//图片类型
                var $file=$('<div id="' + file.id + '" class="file-item thumbnail">'+
                    '<img src="/statics/plugins/webuploader/loading.gif"/>'+
                    '</div>');
                var $li = $(
                    '<img>' +
                    '<div class="info">' + file.name + '</div>' +
                    '<div class="del">' +
                    '<a  href="javascript:void(0);" title="删除图片" ><i class="fa fa-trash"></i> 删除</a>' +
                    '</div>'
                );
                $list.append( $file );
                // 创建缩略图
                // 如果为非图片文件，可以不用调用此方法。
                // thumbnailWidth x thumbnailHeight 为 100 x 100
                uploader.makeThumb( file, function( error, src ) {
                    if ( error ) {
                        $img.replaceWith('<span>不能预览</span>');
                        return;
                    }
                    $file.html('');
                    $file.append($li)
                    $img = $file.find('img');
                    $img.attr( 'src', src );
                }, opts.thumbnailWidth,opts.thumbnailHeight );
            }else{
                var labelInfo="";
                if(opts.auto){
                    labelInfo="正在上传.."
                }else{
                    labelInfo="等待上传.."
                }

                $list.append('<div id="' + $(item)[0].id + file.id + '" class="item">' +
                    '<span class="webuploadinfo btn">' + file.name + '</span>'+
                    '<span class="webuploadstate text-info">'+labelInfo+'</span>' +
                    '<div class="webuploadDelbtn btn btn-xs btn-success" style="display: none">删除</div><br />' +
                    '</div>');
            }
            if(opts.auto){
                uploader.upload();
            }
            //选择文件数达到设置值，隐藏选择按钮
            if(opts.currCountInputId!=undefined){
                listNum=$("#"+opts.currCountInputId).val();
                listNum++;
                $("#"+opts.currCountInputId).val(listNum);
            }else {
                listNum++;
            }
            if(listNum>=opts.fileNumLimit){
                $("#"+pickerid).addClass("hide")
            }
        });
        uploader.on('uploadProgress', function (file, percentage) {//进度条事件
            if(opts.fileType=="img"){
                var $li = $( '#'+file.id ),
                    $percent = $li.find('.progress span');
                // 避免重复创建
                if ( !$percent.length ) {
                    $percent = $('<p class="progress"><span></span></p>')
                        .appendTo( $li )
                        .find('span');
                }

                $percent.css( 'width', percentage * 100 + '%' );
            }else{
                var $li = target.find('#' + $(item)[0].id + file.id),
                    $percent = $li.find('.progress .bar');

                // 避免重复创建
                if (!$percent.length) {
                    $percent = $('<span class="progress">' +
                        '<span  class="percentage"><span class="text"></span>' +
                        '<span class="bar" role="progressbar" style="width: 0%">' +
                        '</span></span>' +
                        '</span>').appendTo($li).find('.bar');
                }

                $li.find('span.webuploadstate').html('上传中');
                $li.find(".text").text(Math.round(percentage * 100) + '%');
                $percent.css('width', percentage * 100 + '%');
            }

        });
        uploader.on('uploadSuccess', function (file, response) {//上传成功事件
            if(response.code==0){
                if(opts.fileType==="img"){//图片类型
                    $( '#'+file.id ).addClass('upload-state-done');
                    //添加删除按钮
                    var $li = $( '#'+file.id ),
                        $del=$li.find('div.del');

                    //避免重复创建
                    if(!$del.length){
                        $del=$('<div class="del"></div>').appendTo($li);
                        $del.append('<a  href="javascript:void(0);" title="删除图片" ><i class="fa fa-trash"></i> 删除</a>')
                    }
                    // $del.append('<a  href="javascript:void(0);" title="删除图片" onclick="deleteFile(\''+file+'\',\''+response.path+'\',\''+response.fileId+'\')"><i class="fa fa-trash"></i> 删除</a>')
                    $hiddenInput.append('<input type="text" id="hiddenInput'+$(item)[0].id + file.id + '" class="' + opts.hiddenInputClass + '" value="' + response.file.id + '" />')
                }else{
                    target.find('#' + $(item)[0].id + file.id).find('span.webuploadstate').html('已上传');
                    target.find('#' + $(item)[0].id + file.id).find('.webuploadDelbtn').show();
                    $hiddenInput.append('<input type="text" id="hiddenInput'+$(item)[0].id + file.id + '"  class="'+opts.hiddenInputClass+ '" value="' + response.file.id + '" />')
                }
            }else{
                $( '#'+file.id ).remove();
                uploader.removeFile(file);
                if(response.msg!=undefined){
                    alert(response.msg);
                }else{
                    alert("文件上传错误");
                }
            }
        });

        uploader.on('uploadError', function (file,reason) {
            if (reason == "http") {
                confirm("您的登录信息已超时，请重新登录",function () {
                    top.location.href = "/exam/login.html";
                });
            }
            if(opts.fileType==='img'){
                var $li = $( '#'+file.id ),
                    $error = $li.find('div.error');
                // 避免重复创建
                if ( !$error.length ) {
                    $error = $('<div class="error"></div>').appendTo( $li );
                }
                $error.text('上传失败');
            }else{
                target.find('#' + $(item)[0].id + file.id).find('span.webuploadstate').html('上传出错');
            }
        });

        uploader.on('uploadComplete', function (file) {//全部完成事件
            if(opts.fileType==='img'){
                $( '#'+file.id ).find('.progress').remove();
            }else {
                target.find('#' + $(item)[0].id + file.id).find('.progress').fadeOut();
            }

        });

        uploader.on('all', function (type) {
            if (type === 'startUpload') {
                state = 'uploading';
            } else if (type === 'stopUpload') {
                state = 'paused';
            } else if (type === 'uploadFinished') {
                state = 'done';
            }

            if (state === 'uploading') {
                $btn.text('暂停上传');
            } else {
                $btn.text('开始上传');
            }
        });

        //删除时执行的方法
        uploader.on('fileDequeued', function (file) {
            var id = $("#hiddenInput" + $(item)[0].id + file.id).val();
            if (id!=null) {
                $.post(webuploaderoptions.deleteServer, { fileId: id }, function (r) {
                    if(r.code==0){
                        if(opts.fileType=='img'){
                            var $li = $('#'+file.id);
                            $li.off().find('.file-panel').off().end().remove();
                        }else{
                            $("#"+ $(item)[0].id + file.id).remove();
                        }

                        $("#hiddenInput" + $(item)[0].id + file.id).remove();
                        if(opts.currCountInputId!==undefined){
                            listNum=$("#"+opts.currCountInputId).val();
                            listNum--
                            $("#"+opts.currCountInputId).val(listNum);
                        }else{
                            listNum--;
                        }
                        if(listNum<opts.fileNumLimit){
                            $("#"+pickerid).removeClass("hide")
                        }

                    }else{
                        alert(r.msg);
                    }
                })
            }
        })

        //多文件点击上传的方法
        $btn.on('click', function () {
            if (state === 'uploading') {
                uploader.stop();
            } else {
                uploader.upload();
            }
        });

        //删除
        if(opts.fileType=='img'){
            $list.on("click", ".del a", function () {

                var $ele = $(this);
                var id = $ele.parent().parent().attr("id");
                uploader.removeFile(id);
            });
        }else{
            $list.on("click", ".webuploadDelbtn", function () {

                var $ele = $(this);
                var id = $ele.parent().attr("id");
                var id = id.replace($(item)[0].id, "");
                var file = uploader.getFile(id);
                uploader.removeFile( file);
            });
        }

        //异常处理
        uploader.on("error", function (type) {
            if (type == 'F_EXCEED_SIZE') {
                alert("请选择" + webuploaderoptions.fileSingleSizeLimit / 1024 / 1024 + "MB以内的文件上传！");
            }else if (type == 'Q_TYPE_DENIED') {
                alert("文件类型错误！");
            }else if (type=='Q_EXCEED_NUM_LIMIT'){
                alert("文件个数过多，请不要一次上传超过"+webuploaderoptions.fileNumLimit+"个文件。")
            }else if(type=='F_DUPLICATE'){
                alert("所选文件重复！")
            }else{
                alert("未知错误，错误码：" + type);
            }
        });

        //设置自定义请求头参数
        uploader.on("uploadBeforeSend", function (obj, data, headers) {
            headers.reqType = "uploadFile";

        });

        //重置上传控件到初始状态，用于reload
        uploader.resetDom=function () {
            uploader.reset();
            $(webuploaderoptions.pick).removeClass("hide");
            $("#"+opts.currCountInputId).val(0)
            $(item).find(".item").remove();
            $("."+opts.hiddenInputClass).remove();
        }
        //根据已有的文件列表长度，设置当前还能上传多少文件,用于getinfo成功返回后
        uploader.setCurrentCount=function(fileListLength){
            $("#"+opts.currCountInputId).val(fileListLength);//设置已上传文件数
            if(fileListLength>=opts.fileNumLimit){
                $(webuploaderoptions.pick).addClass("hide");
            }
        }

        return uploader;

    }
    $.fn.GetFilesAddress = function (options) {
        var ele = $(this);
        var filesdata = ele.find(".UploadhiddenInput");
        var filesAddress = [];
        filesdata.find(".hiddenInput").each(function () {
            filesAddress.push($(this).val());
        })
        return filesAddress;

    }

    $.fn.myWebUpload = function (options) {
        var ele = this;
        if (typeof WebUploader == 'undefined') {
            var casspath = "/statics/plugins/webuploader/webuploader.css";
            $("<link>").attr({ rel: "stylesheet", type: "text/css", href: casspath }).appendTo("head");
            var jspath = "/statics/plugins/webuploader/webuploader.min.js";
            $.getScript(jspath) .done(function() {
                return initWebUpload(ele, options);
            })
                .fail(function() {
                    alert("请检查webuploader的路径是否正确!")
                });

        }
        else {
           return initWebUpload(ele, options);
        }
    }
    $.fn.clearHiddenInputClass = function (options) {
        var ele = this;
        var opts = $.extend(defaults, options);
        $hiddenInput = $(ele).find('.UploadhiddenInput').find("."+opts.hiddenInputClass);
        $hiddenInput.remove();
    }
})(jQuery, window);