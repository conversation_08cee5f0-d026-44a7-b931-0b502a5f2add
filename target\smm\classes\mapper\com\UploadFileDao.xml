<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.com.dao.UploadFileDao">

    <select id="logicDeletedList" resultType="com.sloth.modules.com.entity.UploadFile">
        SELECT id,
               UPLOAD_FILE_PATH,
               UPLOAD_FILE_NAME,
               FIELD_NAME
        FROM com_upload_file
        WHERE DEL_FLAG != 0

    </select>

    <delete id="trueDelete">
        delete
        from com_upload_file
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">#{id}</foreach>
    </delete>


</mapper>
