<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sloth.modules.com.dao.AuditDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.sloth.modules.com.entity.Audit" id="auditMap">
        <result property="id" column="ID"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="ownerId" column="OWNER_ID"/>
        <result property="passed" column="PASSED"/>
        <result property="opinion" column="OPINION"/>
    </resultMap>
    <select id="queryPage" resultType="com.sloth.modules.com.entity.Audit">
        SELECT audit.*, user.SHOW_NAME createUser
        FROM COM_AUDIT audit, SYS_USER user
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="queryList" resultType="com.sloth.modules.com.entity.Audit">
        SELECT audit.*, user.SHOW_NAME createUser
        FROM COM_AUDIT audit, SYS_USER user
        <where>
            ${ew.sqlSegment}
        </where>
    </select>


</mapper>
