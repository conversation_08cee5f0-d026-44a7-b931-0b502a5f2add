/*!
 * vue-validator v3.0.0-alpha.1 
 * (c) 2016 ka<PERSON><PERSON>
 * Released under the MIT License.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.VueValidator=e()}(this,function(){"use strict";function t(t,e){window.console&&(console.warn("[vue-validator] "+t),e&&console.warn(e.stack))}function e(t,e){return t===e||!(!u(t)||!u(e))&&JSON.stringify(t)===JSON.stringify(e)}function i(t){var e=t.className;return"object"==typeof e&&(e=e.baseVal||""),e}function n(t,e){D&&!/svg$/.test(t.namespaceURI)?t.className=e:t.setAttribute("class",e)}function s(t,e){if(t.classList)t.classList.add(e);else{var s=" "+i(t)+" ";s.indexOf(" "+e+" ")<0&&n(t,(s+e).trim())}}function r(t,e){if(t.classList)t.classList.remove(e);else{for(var s=" "+i(t)+" ",r=" "+e+" ";s.indexOf(r)>=0;)s=s.replace(r," ");n(t,s.trim())}t.className||t.removeAttribute("class")}function a(t,e,i){if(t){if(e=e.trim(),e.indexOf(" ")===-1)return void i(t,e);for(var n=e.split(/\s+/),s=0,r=n.length;s<r;s++)i(t,n[s])}}function o(t,e,i){var n=document.createEvent("HTMLEvents");n.initEvent(e,!0,!0),i&&i(n),t.dispatchEvent(n)}function l(e){var i={};return d(e).forEach(function(e){var n=e.key,s=e.val;i[n]=function(){var e=this.$validation;if(!this._isMounted)return null;var i=s.split("."),n=i.shift();if("$validation"!==n)return t("unknown validation result path: "+s),null;var r,a=e;do r=i.shift(),a=a[r];while(i.length>0);return a}}),i}function u(t){return null!==t&&"object"==typeof t}function d(t){return Array.isArray(t)?t.map(function(t){return{key:t,val:t}}):Object.keys(t).map(function(e){return{key:e,val:t[e]}})}function h(t){if(Array.isArray(t)){if(0!==t.length){for(var e=!0,i=0,n=t.length;i<n&&(e=h(t[i]),e);i++);return e}return!1}return"number"==typeof t||"function"==typeof t||("boolean"==typeof t?t:"string"==typeof t?t.length>0:null!==t&&"object"==typeof t&&Object.keys(t).length>0)}function c(t,e){if("string"!=typeof e)return!1;var i=e.match(new RegExp("^/(.*?)/([gimy]*)$"));return!!i&&new RegExp(i[1],i[2]).test(t)}function v(t,e){return"string"==typeof t?y(e,10)&&t.length>=parseInt(e,10):!!Array.isArray(t)&&t.length>=parseInt(e,10)}function f(t,e){return"string"==typeof t?y(e,10)&&t.length<=parseInt(e,10):!!Array.isArray(t)&&t.length<=parseInt(e,10)}function p(t,e){return!isNaN(+t)&&!isNaN(+e)&&+t>=+e}function _(t,e){return!isNaN(+t)&&!isNaN(+e)&&+t<=+e}function y(t){return/^(-?[1-9]\d*|0)$/.test(t)}function g(t){var e;return e="string"==typeof t?[t]:Array.isArray(t)?t:Object.keys(t)}function m(t){var e={};return t.forEach(function(t){e[t]=void 0}),e}function b(t){var e={};return t.forEach(function(t){e[t]=""}),e}function E(t,e){return"boolean"==typeof e&&!e||!("string"!=typeof e||!e)&&e}function w(t){t[S]="DOM"}function V(t){var i=t.data&&t.data.directives||[],n=i.find(function(t){return"model"===t.name});return n&&void 0!==n.oldValue?e(n.value,n.oldValue):null}function M(t){for(var e=[],i=0,n=t.options.length;i<n;i++){var s=t.options[i];!s.disabled&&s.selected&&e.push(s.value)}return e}function O(t){var e=Object.create(null);return function(i){for(var n=[],s=arguments.length-1;s-- >0;)n[s]=arguments[s+1];var r=e[i];return r||(e[i]=t.apply(void 0,n))}}function I(t){var e=t._vnode;return t.multiple?new X(t):new Y(t,e)}function k(t){return(t.data&&t.data.directives||[]).find(function(t){return"model"===t.name})}function $(t){var e={},i=e.listeners=t.componentOptions?t.componentOptions.listeners:t.data&&t.data.on;return e.type="input"===t.tag&&"text"===(t.data&&t.data.attrs&&t.data.attrs.type)||t.tag&&t.tag.match(/vue-component/)?"input":"change",i&&(e.orgListeners=i[e.type]),e}function R(t){return t&&"function"==typeof t.then}function x(t,e,i){i.forEach(function(i){i&&i.componentOptions&&i.componentOptions.propsData&&"validity-control"===i.componentOptions.tag&&(i.componentOptions.propsData.validation={instance:t,name:e}),i.children&&x(t,e,i.children)})}function L(e,i){return void 0===i&&(i={}),ut?void t("already installed."):(U(e),K(e),j(e),N(e),void(ut=!0))}function j(t){t.mixin(B(t))}function N(t){var e=lt(t);Object.keys(e).forEach(function(i){t.component(i,e[i])})}var C="undefined"!=typeof window&&"[object Object]"!==Object.prototype.toString.call(window),A=C&&window.navigator.userAgent.toLowerCase(),D=A&&A.indexOf("msie 9.0")>0,S="__VUE_VALIDATOR_MODEL_NOTIFY_EVENT__",T={classes:{}},U=function(t){Object.defineProperty(t.config,"validator",{enumerable:!0,configurable:!0,get:function(){return T},set:function(t){T=t}})},G=Object.freeze({required:h,pattern:c,minlength:v,maxlength:f,min:p,max:_}),K=function(t){function e(e,i){return void 0===i?t.options.validators[e]:(t.options.validators[e]=i,void(null===i&&delete t.options.validators.id))}var i=t.util,n=i.extend,s=Object.create(null);n(s,G),t.options.validators=s;var r=t.config.optionMergeStrategies;r&&(r.validators=function(t,e){if(!e)return t;if(!t)return e;var i=Object.create(null);n(i,t);var s;for(s in e)i[s]=e[s];return i}),t.validator=e},W=function(t){var e=t.util,i=e.extend;return{data:function(){return{valid:!0,dirty:!1,touched:!1,modified:!1,results:{}}},computed:{invalid:function(){return!this.valid},pristine:function(){return!this.dirty},untouched:function(){return!this.touched},result:function(){var t={valid:this.valid,invalid:this.invalid,dirty:this.dirty,pristine:this.pristine,touched:this.touched,untouched:this.untouched,modified:this.modified},e=this.results;return this._validityKeys.forEach(function(i){if(t[i]=e[i],t[i].errors){var n=t.errors||[];t[i].errors.forEach(function(t){n.push(t)}),t.errors=n}}),t}},watch:{results:function t(e,i){var n=this._validityKeys,t=this.results;this.valid=this.checkResults(n,t,"valid",!0),this.dirty=this.checkResults(n,t,"dirty",!1),this.touched=this.checkResults(n,t,"touched",!1),this.modified=this.checkResults(n,t,"modified",!1)}},created:function(){this._validities=Object.create(null),this._validityWatchers=Object.create(null),this._validityKeys=[],this._committing=!1},destroyed:function(){var t=this;this._validityKeys.forEach(function(e){t._validityWatchers[e](),delete t._validityWatchers[e],delete t._validities[e]}),delete this._validityWatchers,delete this._validities,delete this._validityKeys},methods:{register:function(t,e){var i=this;this._validities[t]=e,this._validityKeys=Object.keys(this._validities),this.setResults(t,{}),this.withCommit(function(){i._validityWatchers[t]=e.$watch("result",function(e,n){i.setResults(t,e)},{deep:!0,immediate:!0})})},unregister:function(t){var e=this;this._validityWatchers[t](),delete this._validityWatchers[t],delete this._validities[t],this._validityKeys=Object.keys(this._validities),this.withCommit(function(){e.resetResults(t)})},isRegistered:function(t){return t in this._validities},getValidityKeys:function(){return this._validityKeys},checkResults:function(t,e,i,n){for(var s=n,r=0;r<t.length;r++){var a=e[t[r]];if(a[i]!==n){s=!n;break}}return s},setResults:function(t,e){var n=this,s={};this._validityKeys.forEach(function(t){s[t]=i({},n.results[t])}),s[t]=i({},e),this.results=s},resetResults:function(t){var e=this,n={};this._validityKeys.forEach(function(s){t&&t!==s&&(n[s]=i({},e.results[s]))}),this.results=n},withCommit:function(t){var e=this._committing;this._committing=!0,t(),this._committing=e}}}},q=function(t){var e=W(t),i=function(e){void 0===e&&(e={}),this._result={},this._host=e.host,this._named=Object.create(null),this._group=Object.create(null),this._validities=Object.create(null),this._beginDestroy=!1,t.util.defineReactive(this._host,"$validation",this._result)};return i.prototype.register=function(i,n,s){if(void 0===s&&(s={}),this._validityManager||(this._validityManager=new t(e),this._watchValidityResult()),!this._validities[i]){this._validities[i]=n;var r=s.named,a=s.group,o=a?this._getValidityGroup("group",a)||this._registerValidityGroup("group",a):null,l=r?this._getValidityGroup("named",r)||this._registerValidityGroup("named",r):null;r&&a&&l&&o?(o.register(i,n),!l.isRegistered(a)&&l.register(a,o),!this._validityManager.isRegistered(r)&&this._validityManager.register(r,l)):l?(l.register(i,n),!this._validityManager.isRegistered(r)&&this._validityManager.register(r,l)):o?(o.register(i,n),!this._validityManager.isRegistered(a)&&this._validityManager.register(a,o)):this._validityManager.register(i,n)}},i.prototype.unregister=function(t,e){if(void 0===e&&(e={}),this._validityManager&&this._validities[t]){delete this._validities[t];var i=e.named,n=e.group,s=n?this._getValidityGroup("group",n):null,r=i?this._getValidityGroup("named",i):null;i&&n&&r&&s?(s.unregister(t),r.isRegistered(n)&&r.unregister(n),this._validityManager.isRegistered(i)&&this._validityManager.unregister(i)):r?(r.unregister(t),this._validityManager.isRegistered(i)&&this._validityManager.unregister(i)):s?(s.unregister(t),this._validityManager.isRegistered(n)&&this._validityManager.unregister(n)):this._validityManager.unregister(t),n&&this._unregisterValidityGroup("group",n),i&&this._unregisterValidityGroup("named",i)}},i.prototype.destroy=function(){var t=this,e=Object.keys(this._validities),i=Object.keys(this._named),n=Object.keys(this._group);e.forEach(function(e){n.forEach(function(i){var n=t._getValidityGroup("group",i);n&&n.isRegistered(i)&&n.unregister(e)}),i.forEach(function(i){var n=t._getValidityGroup("named",i);n&&n.isRegistered(e)&&n.unregister(e)}),t._validityManager.isRegistered(e)&&t._validityManager.unregister(e),delete t._validities[e]}),n.forEach(function(e){i.forEach(function(i){var n=t._getValidityGroup("named",i);n&&n.isRegistered(e)&&n.unregister(e)}),t._validityManager.isRegistered(e)&&t._validityManager.unregister(e),t._unregisterValidityGroup("group",e)}),i.forEach(function(e){t._validityManager.isRegistered(e)&&t._validityManager.unregister(e),t._unregisterValidityGroup("named",e)}),this._beginDestroy=!0},i.prototype._getValidityGroup=function(t,e){return"named"===t?this._named[e]:this._group[e]},i.prototype._registerValidityGroup=function(i,n){var s="named"===i?this._named:this._group;return s[n]=new t(e),s[n]},i.prototype._unregisterValidityGroup=function(t,e){var i="named"===t?this._named:this._group;i[e]&&(i[e].$destroy(),delete i[e])},i.prototype._watchValidityResult=function(){var e=this;this._watcher=this._validityManager.$watch("results",function(i,n){t.set(e._host,"$validation",i),e._beginDestroy&&e._destroyValidityMananger()},{deep:!0})},i.prototype._unwatchValidityResult=function(){this._watcher(),delete this._watcher},i.prototype._destroyValidityMananger=function(){this._unwatchValidityResult(),this._validityManager.$destroy(),this._validityManager=null},i},B=function(t){var e=q(t);return{beforeCreate:function(){this._validation=new e({host:this})}}},P={field:{type:String,required:!0},validators:{type:[String,Array,Object],required:!0},group:{type:String},multiple:{type:Boolean},classes:{type:Object,default:function(){return{}}}},H={valid:"valid",invalid:"invalid",touched:"touched",untouched:"untouched",pristine:"pristine",dirty:"dirty",modified:"modified"},J=function(t){function e(){var t=g(this.validators);return{results:m(t),valid:!0,dirty:!1,touched:!1,modified:!1,progresses:b(t)}}var i=t.util,n=i.extend,s=n({child:{type:Object,required:!0}},P);return{props:s,data:e}},z=function(t){function e(){return!this.valid}function i(){return!this.dirty}function n(){return!this.touched}function s(){var t=this,e={valid:this.valid,invalid:this.invalid,dirty:this.dirty,pristine:this.pristine,touched:this.touched,untouched:this.untouched,modified:this.modified},i=this._keysCached(this._uid.toString(),this.results);return i.forEach(function(i){var n=E(i,t.results[i]);if(n===!1)e[i]=!1;else{var s={field:t.field,validator:i};"string"==typeof n&&(s.message=n),e.errors||(e.errors=[]),Array.isArray(e.errors)&&e.errors.push(s),e[i]=n}}),e}return{invalid:e,pristine:i,untouched:n,result:s}},F=function(t){return{render:function(t){return this._interceptEvents(this.child,this.multiple),this.child}}},Y=function(t,e){this._vm=t,this._vnode=e,this.initValue=this.getValue(),this.attachValidity()},Q={_isBuiltIn:{},_isComponent:{}};Q._isBuiltIn.get=function(){var t=this._vnode;return!t.child&&!t.componentOptions&&t.tag},Q._isComponent.get=function(){var t=this._vnode;return t.child&&t.componentOptions&&t.tag.match(/vue-component/)},Y.prototype.attachValidity=function(){this._vm.$el.$validity=this._vm},Y.prototype.getValue=function(){if(this._isBuiltIn){var t=this._vm.$el;return"SELECT"===t.tagName?M(t):"checkbox"===t.type?t.checked:t.value}return this._isComponent?this._vnode.child.value:""},Y.prototype.checkModified=function(){if(this._isBuiltIn){var t=this._vm.$el;return"SELECT"===t.tagName?!e(this.initValue,M(t)):"checkbox"===t.type?!e(this.initValue,t.checked):!e(this.initValue,t.value)}return!!this._isComponent&&!e(this.initValue,this._vnode.child.value)},Y.prototype.listenToucheableEvent=function(){this._vm.$el.addEventListener("focusout",this._vm.willUpdateTouched)},Y.prototype.unlistenToucheableEvent=function(){this._vm.$el.removeEventListener("focusout",this._vm.willUpdateTouched)},Y.prototype.listenInputableEvent=function(){var t=this._vm;if(this._isBuiltIn){var e=t.$el;"SELECT"===e.tagName?e.addEventListener("change",t.handleInputable):"checkbox"===e.type?e.addEventListener("change",t.handleInputable):e.addEventListener("input",t.handleInputable)}else this._isComponent&&(this._unwatchInputable=this._vnode.child.$watch("value",t.watchInputable))},Y.prototype.unlistenInputableEvent=function(){var t=this._vm;if(this._isBuiltIn){var e=t.$el;"SELECT"===e.tagName?e.removeEventListener("change",t.handleInputable):"checkbox"===e.type?e.removeEventListener("change",t.handleInputable):e.removeEventListener("input",t.handleInputable)}else this._isComponent&&this._unwatchInputable&&(this._unwatchInputable(),this._unwatchInputable=void 0,delete this._unwatchInputable)},Y.prototype.fireInputableEvent=function(){if(this._isBuiltIn){var t=this._vm.$el;"SELECT"===t.tagName?o(t,"change",w):"checkbox"===t.type?o(t,"change",w):o(t,"input",w)}else if(this._isComponent){var e={value:this.getValue()};e[S]="COMPONENT",this._vnode.child.$emit("input",e)}},Y.prototype.modelValueEqual=function(t){return V(t)},Object.defineProperties(Y.prototype,Q);var X=function(t){this._vm=t,this.initValue=this.getValue(),this.attachValidity()};X.prototype.attachValidity=function(){var t=this;this._vm.$el.$validity=this._vm,this._eachItems(function(e){e.$validity=t._vm})},X.prototype.getValue=function(){return this._getCheckedValue()},X.prototype.checkModified=function(){return!e(this.initValue,this._getCheckedValue())},X.prototype.listenToucheableEvent=function(){var t=this;this._eachItems(function(e){e.addEventListener("focusout",t._vm.willUpdateTouched)})},X.prototype.unlistenToucheableEvent=function(){var t=this;this._eachItems(function(e){e.removeEventListener("focusout",t._vm.willUpdateTouched)})},X.prototype.listenInputableEvent=function(){var t=this;this._eachItems(function(e){e.addEventListener("change",t._vm.handleInputable)})},X.prototype.unlistenInputableEvent=function(){var t=this;this._eachItems(function(e){e.removeEventListener("change",t._vm.handleInputable)})},X.prototype.fireInputableEvent=function(){this._eachItems(function(t){o(t,"change",w)})},X.prototype.modelValueEqual=function(t){for(var e=null,i=this._vm.child&&this._vm.child.children||[],n=0;n<i.length;n++){var s=V(i[n]);if(!s){e=s;break}}return e},X.prototype._getCheckedValue=function(){var t=[];return this._eachItems(function(e){!e.disabled&&e.checked&&t.push(e.value)}),t},X.prototype._getItems=function(){return this._vm.$el.querySelectorAll('input[type="checkbox"], input[type="radio"]')},X.prototype._eachItems=function(t){for(var e=this._getItems(),i=0;i<e.length;i++)t(e[i])};var Z=function(t){function e(){this._elementable=null,this._keysCached=O(function(t){return Object.keys(t)}),this._modified=!1,this._modelIntegrationMode="NONE",this._watchValidationRawResults();var t=this.$options.propsData?this.$options.propsData.validation:null;if(t){var e=t.instance,i=t.name,n=this.group;e.register(this.field,this,{named:i,group:n})}}function i(){var t=this.$options.propsData?this.$options.propsData.validation:null;if(t){var e=t.instance,i=t.name,n=this.group;e.unregister(this.field,this,{named:i,group:n})}this._unwatchValidationRawResults(),this._elementable.unlistenInputableEvent(),this._elementable.unlistenToucheableEvent(),this._elementable=null}function n(){this._elementable=I(this),this._elementable.listenToucheableEvent(),this._elementable.listenInputableEvent(),a(this.$el,this.classes.untouched,s),a(this.$el,this.classes.pristine,s)}function r(){if("MODEL_AND_USER"===this._modelIntegrationMode){var t=this._elementable.modelValueEqual(this._vnode);this._applyWithUserHandler||null===t||t||this._elementable.fireInputableEvent(),delete this._applyWithUserHandler}else if("MODEL"===this._modelIntegrationMode){var e=this._elementable.modelValueEqual(this._vnode);null===e||e||this._elementable.fireInputableEvent()}}return{created:e,destroyed:i,mounted:n,updated:r}},tt=function(t){function e(t){for(var e=[],i=arguments.length-1;i-- >0;)e[i]=arguments[i+1];(n=this).$emit.apply(n,[t].concat(e));var n}function i(t,e){var i=this;(e?t.children||[]:[t]).forEach(function(t){i._wrapEvent(t)})}function n(t){var e=this,i={};if(!t.tag||!t.data)return i;var n=k(t);if(!n)return i;var s=$(t),r=s.type,o=s.orgListeners,l=s.listeners,u=Array.isArray(o)?o[0]:o,d=Array.isArray(o)?o[1]:null,h=this._modelIntegrationMode;u&&d?h=this._modelIntegrationMode="MODEL_AND_USER":u&&!d&&(h=this._modelIntegrationMode="MODEL");var c=function(i){return function(n){d&&(e._applyWithUserHandler=!0),void 0!==n&&n!==!0||u.apply(t.context,i)}},v=(n.modifiers||{}).validity,f=this;return l[r]=function(){var e=a(arguments,0);if("MODEL_AND_USER"===h){var i=e[0];if("DOM"===i[S])return delete i[S],void(d&&d.apply(t.context,e));if("COMPONENT"===i[S]){var n=i.value;return e[0]=n,void(d&&d.apply(t.context,e))}if(v){var s=f._applyer=c(e);e.push(s),d&&d.apply(t.context,e)}else d&&d.apply(t.context,e),u.apply(t.context,e)}else"MODEL"===h&&(v?f._applyer=c(e):u.apply(t.context,e))},i.dir=n,i}function s(t){"NONE"!==this._modelIntegrationMode&&this._applyer&&this._applyer(t)}var r=t.util,a=r.toArray;return{_fireEvent:e,_interceptEvents:i,_wrapEvent:n,pass:s}},et=function(t){function e(t){return this._elementable.getValue()}function i(){return this._elementable.checkModified()}function n(t){this.touched||(this.touched=!0,a(this.$el,this.classes.touched,s),a(this.$el,this.classes.untouched,r),this._fireEvent("touched"))}function o(){!this.dirty&&this.checkModified()&&(this.dirty=!0,a(this.$el,this.classes.dirty,s),a(this.$el,this.classes.pristine,r),this._fireEvent("dirty"))}function l(){var t=this.modified=this.checkModified();this._modified!==t&&(this._modified=t,a(this.$el,this.classes.modified,t?s:r),this._fireEvent("modified",t))}function u(t){this.willUpdateDirty(),this.willUpdateModified()}function d(t){this.willUpdateDirty(),this.willUpdateModified()}function h(){var t=this;this._unwatchValidationRawResults();for(var e=this._keysCached(this._uid.toString(),this.results),i=0;i<e.length;i++)t.results[e[i]]=void 0,t.progresses[e[i]]="";a(this.$el,this.classes.valid,r),a(this.$el,this.classes.invalid,r),a(this.$el,this.classes.touched,r),a(this.$el,this.classes.untouched,s),a(this.$el,this.classes.dirty,r),a(this.$el,this.classes.pristine,s),a(this.$el,this.classes.modified,r),this.valid=!0,this.dirty=!1,this.touched=!1,this.modified=!1,this._modified=!1,this._watchValidationRawResults()}function c(){var t=this;this._unwatch=this.$watch("results",function(e){for(var i=!0,n=t._keysCached(t._uid.toString(),t.results),o=0;o<n.length;o++){var l=t.results[n[o]];if("boolean"==typeof l&&!l){i=!1;break}if("string"==typeof l&&l){i=!1;break}}t.valid=i,i?(a(t.$el,t.classes.valid,s),a(t.$el,t.classes.invalid,r)):(a(t.$el,t.classes.valid,r),a(t.$el,t.classes.invalid,s)),t._fireEvent(i?"valid":"invalid")},{deep:!0})}function v(){this._unwatch(),this._unwatch=void 0,delete this._unwatch}return{getValue:e,checkModified:i,willUpdateTouched:n,willUpdateDirty:o,willUpdateModified:l,handleInputable:u,watchInputable:d,reset:h,_watchValidationRawResults:c,_unwatchValidationRawResults:v}},it=function(t){function e(t){var e=this.child&&this.child.context?this.child.context.$options:this.$options;return u(e,"validators",t)}function i(t,e,i){var n=this._resolveValidator(t);if(!n)return null;var s=null,r=null,a=null;if(l(n))n.check&&"function"==typeof n.check&&(s=n.check),n.message&&(a=n.message);else{if("function"!=typeof n)return null;s=n}if(!s)return null;l(this.validators)&&(l(this.validators[t])?(this.validators[t].rule&&(r=this.validators[t].rule),this.validators[t].message&&(a=this.validators[t].message)):r=this.validators[t]);var o={fn:s,value:i,field:e};return r&&(o.rule=r),a&&(o.msg=a),o}function n(t,e,i){return i?i:e?"function"==typeof e?e(t):e:void 0}function s(t,e){var i=this,n=t.fn,s=t.value,r=t.field,a=t.rule,o=t.msg,l=n.call(this.child.context,s,a);"function"==typeof l?l(function(){e(!0)},function(t){e(!1,i._resolveMessage(r,o,t))}):R(l)?l.then(function(){e(!0)},function(t){e(!1,i._resolveMessage(r,o,t))}).catch(function(t){e(!1,i._resolveMessage(r,o,t.message))}):e(l,l===!1?this._resolveMessage(r,o):void 0)}function r(t,e,i){var n=this,s=this._getValidateDescriptor(t,this.field,e);if(s){if(this.progresses[t])return!1;this.progresses[t]="running",this.$nextTick(function(){n._invokeValidator(s,function(e,s){if(n.progresses[t]="",n.results[t]=s||e,i)n.$nextTick(function(){i.call(n,null,e,s)});else{var r={result:e};s&&(r.msg=s),n._fireEvent("validate",t,r)}})})}else{var r=new Error;i?i.call(this,r):this._fireEvent("validate",t,r)}return!0}function a(){for(var t=this,e=[],i=arguments.length;i--;)e[i]=arguments[i];var n,s,r,a=!0;return 3===e.length?(n=[e[0]],s=e[1],r=e[2]):2===e.length?l(e[0])?(n=[e[0].validator],s=e[0].value||this.getValue(),r=e[1]):(n=this._keysCached(this._uid.toString(),this.results),s=e[0],r=e[1]):1===e.length?(n=this._keysCached(this._uid.toString(),this.results),s=this.getValue(),r=e[0]):(n=this._keysCached(this._uid.toString(),this.results),s=this.getValue(),r=null),3===e.length||2===e.length&&l(e[0])?a=this._validate(n[0],s,r):n.forEach(function(e){a=t._validate(e,s,r)}),a}var o=t.util,l=o.isPlainObject,u=o.resolveAsset;return{_resolveValidator:e,_getValidateDescriptor:i,_resolveMessage:n,_invokeValidator:s,_validate:r,validate:a}},nt=function(t){var e=t.util,i=e.extend,n={};return i(n,tt(t)),i(n,et(t)),i(n,it(t)),n},st=function(t){var e=t.util,i=e.extend,n=J(t),s=n.props,r=n.data,a=z(t),o=Z(t),l=F(t),u=l.render,d=nt(t),h={props:s,data:r,render:u,computed:a,methods:d};return i(h,o),h},rt=function(t){var e=t.util,i=e.extend;return{functional:!0,props:P,render:function(e,n){var s=n.props,r=n.data,a=n.children;return a.map(function(n){if(!n.tag)return n;var a=i({},r);return a.props=i({},s),a.props.classes=i(i(i({},H),t.config.validator.classes),a.props.classes),a.props.child=n,e("validity-control",a)})}}},at=function(t){var e=t.util,i=e.extend,n=i({tag:{type:String,default:"fieldset"}},P);return{functional:!0,props:n,render:function(e,n){var s=n.props,r=n.data,a=n.children,o=e(s.tag,a),l=i({},r);return l.props=i({},s),l.props.classes=i(i(i({},H),t.config.validator.classes),l.props.classes),l.props.child=o,l.props.multiple=!0,e("validity-control",l)}}},ot=function(t){var e=t.util,i=e.extend;return{functional:!0,props:{name:{type:String},tag:{type:String,default:"form"}},render:function(t,e){var n=e.props,s=e.data,r=e.parent,a=e.children;e.slots;if(!r._validation)return a;var o=n.tag||"form";x(r._validation,n.name,a);var l=i({attrs:{}},s);return"form"===o&&(l.attrs.novalidate=!0),t(o,l,a)}}},lt=function(t){return{"validity-control":st(t),validity:rt(t),"validity-group":at(t),validation:ot(t)}},ut=!1;L.mapValidation=l,L.version="3.0.0-alpha.1","undefined"!=typeof window&&window.Vue&&window.Vue.use(L);var dt={install:L,mapValidation:l};return dt});