											
var vm = new Vue({
	el:'#app',
    data: {
        showList: 'list',
        showAddParam: false,
        title: null,
        q: {},
        reportDesign: {
            params: '',
        },
        paramArr: [],
        param: {
            type:'text',
            custom: '',
            id: '',
            name: '',
            readonly: 'false',
            placeholder: '',
            event: '',
            script: '',
            pvalue: '',
            hiddenHtml: '',
            datePattern:'yyyy-MM-dd HH:mm:ss',
        },
        paramIndex: 0,
        addFlag: false,
    },
	methods: {
		query: function () {
			vm.reload('query');
		},
        queryAll: function () {
		    //清空查询条件
            for(var key in vm.q){
                vm.q[key] = '';
            }
            vm.reload('query');
        },
		update: function (id) {
			//var id = getSelectedRow();
			if(id == null){
				return ;
			}
			vm.showList = 'edit';
            vm.title = "修改";
            vm.getInfo(id)
		},
		saveOrUpdate: function (event) {
		    if(vm.showList=='edit'&&!validateForm("frm2")){
		        return;
			}
			var url = vm.reportDesign.id == null ? "report/reportdesign/save" : "report/reportdesign/update";
			$.ajax({
				type: "POST",
			    url: baseURL + url,
                contentType: "application/json",
			    data: JSON.stringify(vm.reportDesign),
			    success: function(r){
			    	if(r.code === 0){
						alert('操作成功', function(index){
							vm.reload();
						});
					}else{
						alert(r.msg);
					}
				}
			});
		},
		del: function (event) {
			var ids = getSelectedRows();
			if(ids == null){
				return ;
			}
			
			confirm('确定要删除选中的记录？', function(){
				$.ajax({
					type: "POST",
				    url: baseURL + "report/reportdesign/delete",
                    contentType: "application/json",
				    data: JSON.stringify(ids),
				    success: function(r){
						if(r.code == 0){
							alert('操作成功', function(index){
								$("#jqGrid").trigger("reloadGrid");
							});
						}else{
							alert(r.msg);
						}
					}
				});
			});
		},
		delOne: function (id) {
             confirm('确定要删除选中的记录？', function(){
                 $.ajax({
                    type: "POST",
                    url: baseURL + "report/reportdesign/delete/"+id,
                    contentType: "application/json",
                    success: function(r){
                    if(r.code == 0){
                    	alert('操作成功', function(index){
                    	$("#jqGrid").trigger("reloadGrid");
                		});
                	}else{
                    	alert(r.msg);
                	}
                	}
                });
             });
        },
		getInfo: function(id){
			$.get(baseURL + "report/reportdesign/info/"+id, function(r){
                vm.reportDesign = r.reportDesign;
                vm.paramArr = [];
                if (vm.reportDesign.params!=null&&typeof (vm.reportDesign.params)!="undefined"&&vm.reportDesign.params != '') {
                    vm.paramArr = JSON.parse(vm.reportDesign.params);
                }
                loadParamsGrid(vm.paramArr);
            });
		},
		//跳转到设计参数
        designParams: function (id) {
            if(id == null){
                return ;
            }
            vm.showList = 'params';
            vm.title = "设计查询参数";
            vm.paramArr = [];
            vm.getInfo(id)

        },
		//添加一个参数
        addParam: function (id) {
            if ( id == 0) {
                vm.paramIndex = vm.paramArr.length;
            }else{
                vm.paramIndex= $('#' + id)[0].rowIndex-1;
            }
            $(".error-label").remove();
            vm.param = {
                type:'text',
                custom: '',
                id: '',
                name: '',
                readonly: 'false',
                placeholder: '',
                event: '',
                script: '',
                pvalue: '',
                hiddenHtml: '',
                datePattern:'yyyy-MM-dd HH:mm:ss',
            };
            vm.addFlag = true;
            vm.showParamsLayer()
        },
		//编辑一个参数
        updateParam: function (id) {
            vm.paramIndex= $('#' + id)[0].rowIndex-1;
            $(".error-label").remove();
            vm.param = AppUtils.cloneObj(vm.paramArr[vm.paramIndex]);
            vm.addFlag = false;
            vm.showParamsLayer()
        },
		//删除一个参数
        delParam: function (id) {
            vm.paramIndex= $('#' + id)[0].rowIndex-1;
            vm.paramArr.splice(vm.paramIndex, 1)
            $("#paramsGrid").setGridParam({data:vm.paramArr}).trigger('reloadGrid');
        },
        //打开弹窗
        showParamsLayer: function () {
            layer.open({
                type: 1,
                offset: '50px',
                title: "编辑查询参数",
                area: ['600px', '500px'],
                shadeClose: false,
                content: jQuery("#paramsLayer"),
                btn: ['确定', '取消'],
                btn1: function (index) {
                    //保存当前编辑的参数
                    if(!validateForm("frm")){
                        return;
                    }
                    //id和name属性不能重名
                    for(var i=0;i<vm.paramArr.length;i++){
                        if ((vm.param.id == vm.paramArr[i].id || vm.param.name == vm.paramArr[i].name)&&i!=vm.paramIndex) {
                            debugger
                            alert('控件id或name重复');
                            return;
                        }
                    }
                    //保存并更新grid
                    if (vm.addFlag) {
                        vm.paramArr.splice(vm.paramIndex, 0, vm.param);
                    }else{
                        vm.paramArr.splice(vm.paramIndex, 1, vm.param);
                    }
                    $("#paramsGrid").setGridParam({data:vm.paramArr}).trigger('reloadGrid');
                    layer.close(index);
                }
            });

        },
		//保存当前的参数设计到后台
		saveParams:function(){
            if (vm.paramArr.length == 0) {
                vm.reportDesign.params = "";
            } else {
                vm.reportDesign.params = JSON.stringify(vm.paramArr);
            }
            vm.saveOrUpdate();
		},
		reload: function (event) {
			vm.showList = 'list';
            var page = event == 'query' ? '1' : $("#jqGrid").jqGrid('getGridParam','page');
			$("#jqGrid").jqGrid('setGridParam',{ 
                page:page,
                postData:vm.q
            }).trigger("reloadGrid");
            $(".error-label").remove();
		}
	},
    watch:{
	    'param.type':function () {
            $(".error-label").remove();
        }
    }
});