<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybkj.smm.modules.rlc.mapper.RlcLicenceMapper">

    <resultMap type="RlcLicence" id="RlcLicenceResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="licCode"    column="lic_code"    />
        <result property="licDate"    column="lic_date"    />
        <result property="licDept"    column="lic_dept"    />
        <result property="licFilename"    column="lic_filename"    />
        <result property="projName"    column="proj_name"    />
        <result property="projType"    column="proj_type"    />
        <result property="conUnit"    column="con_unit"    />
        <result property="conUnitPerson"    column="con_unit_person"    />
        <result property="conUnitTel"    column="con_unit_tel"    />
        <result property="evalUnit"    column="eval_unit"    />
        <result property="evalUnitPerson"    column="eval_unit_person"    />
        <result property="evalUnitTel"    column="eval_unit_tel"    />
        <result property="urlRecord"    column="url_record"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="regInfo"    column="reg_info"    />
        <result property="regTime"    column="reg_time"    />
        <result property="licDateExpr"    column="lic_date_expr"    />
        <result property="fileName"    column="file_name"    />

    </resultMap>

    <sql id="selectRlcLicenceVo">
        select id, dept_id, lic_code, lic_date, lic_dept, lic_filename, proj_name,
            proj_type, con_unit, con_unit_person, con_unit_tel, eval_unit, eval_unit_person, eval_unit_tel,
            url_record, create_by, create_time, update_by, update_time, remark,
            reg_info, reg_time, lic_date_expr,file_name
        from rlc_licence
    </sql>

    <select id="selectRlcLicenceList" parameterType="RlcLicence" resultMap="RlcLicenceResult">
        <include refid="selectRlcLicenceVo"/>
        <where>
            <if test="deptId != null "> and dept_id LIKE CONCAT( #{deptId}, '%')</if>
            <if test="licCode != null  and licCode != ''"> and lic_code like concat('%', #{licCode}, '%')</if>
            <if test="licDateBegin != null "> and lic_date &gt;= #{licDateBegin} and lic_date &lt;= #{licDateEnd}</if>
            <if test="licDept != null  and licDept != ''"> and lic_dept like concat('%', #{licDept}, '%')</if>
            <if test="licFilename != null  and licFilename != ''"> and lic_filename like concat('%', #{licFilename}, '%')</if>
            <if test="projName != null  and projName != ''"> and proj_name like concat('%', #{projName}, '%')</if>
            <if test="projType != null  and projType != ''"> and proj_type = #{projType}</if>
            <if test="conUnit != null  and conUnit != ''"> and con_unit = #{conUnit}</if>
            <if test="conUnitPerson != null  and conUnitPerson != ''"> and con_unit_person = #{conUnitPerson}</if>
            <if test="conUnitTel != null  and conUnitTel != ''"> and con_unit_tel = #{conUnitTel}</if>
            <if test="evalUnit != null  and evalUnit != ''"> and eval_unit = #{evalUnit}</if>
            <if test="evalUnitPerson != null  and evalUnitPerson != ''"> and eval_unit_person = #{evalUnitPerson}</if>
            <if test="evalUnitTel != null  and evalUnitTel != ''"> and eval_unit_tel = #{evalUnitTel}</if>
            <if test="urlRecord != null  and urlRecord != ''"> and url_record = #{urlRecord}</if>
            <if test="fileName != null">file_name,</if>

        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectRlcLicenceById" parameterType="Long" resultMap="RlcLicenceResult">
        <include refid="selectRlcLicenceVo"/>
        where id = #{id}
    </select>

    <insert id="insertRlcLicence" parameterType="RlcLicence" useGeneratedKeys="true" keyProperty="id">
        insert into rlc_licence
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="licCode != null">lic_code,</if>
            <if test="licDate != null">lic_date,</if>
            <if test="licDateExpr != null">lic_date_expr,</if>
            <if test="licDept != null">lic_dept,</if>
            <if test="licFilename != null">lic_filename,</if>
            <if test="projName != null">proj_name,</if>
            <if test="projType != null">proj_type,</if>
            <if test="conUnit != null">con_unit,</if>
            <if test="conUnitPerson != null">con_unit_person,</if>
            <if test="conUnitTel != null">con_unit_tel,</if>
            <if test="evalUnit != null">eval_unit,</if>
            <if test="evalUnitPerson != null">eval_unit_person,</if>
            <if test="evalUnitTel != null">eval_unit_tel,</if>
            <if test="urlRecord != null">url_record,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="fileName != null">file_name,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="licCode != null">#{licCode},</if>
            <if test="licDate != null">#{licDate},</if>
            <if test="licDateExpr != null">#{licDateExpr},</if>
            <if test="licDept != null">#{licDept},</if>
            <if test="licFilename != null">#{licFilename},</if>
            <if test="projName != null">#{projName},</if>
            <if test="projType != null">#{projType},</if>
            <if test="conUnit != null">#{conUnit},</if>
            <if test="conUnitPerson != null">#{conUnitPerson},</if>
            <if test="conUnitTel != null">#{conUnitTel},</if>
            <if test="evalUnit != null">#{evalUnit},</if>
            <if test="evalUnitPerson != null">#{evalUnitPerson},</if>
            <if test="evalUnitTel != null">#{evalUnitTel},</if>
            <if test="urlRecord != null">#{urlRecord},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="fileName != null">#{fileName},</if>

        </trim>
    </insert>

    <update id="updateRlcLicence" parameterType="RlcLicence">
        update rlc_licence
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="licCode != null">lic_code = #{licCode},</if>
            <if test="licDate != null">lic_date = #{licDate},</if>
            <if test="licDateExpr != null">lic_date_expr = #{licDateExpr},</if>
            <if test="licDept != null">lic_dept = #{licDept},</if>
            <if test="licFilename != null">lic_filename = #{licFilename},</if>
            <if test="projName != null">proj_name = #{projName},</if>
            <if test="projType != null">proj_type = #{projType},</if>
            <if test="conUnit != null">con_unit = #{conUnit},</if>
            <if test="conUnitPerson != null">con_unit_person = #{conUnitPerson},</if>
            <if test="conUnitTel != null">con_unit_tel = #{conUnitTel},</if>
            <if test="evalUnit != null">eval_unit = #{evalUnit},</if>
            <if test="evalUnitPerson != null">eval_unit_person = #{evalUnitPerson},</if>
            <if test="evalUnitTel != null">eval_unit_tel = #{evalUnitTel},</if>
            <if test="urlRecord != null">url_record = #{urlRecord},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="regInfo != null">reg_info = #{regInfo}, reg_time = now(), </if>
            <if test="fileName != null">file_name=#{fileName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRlcLicenceById" parameterType="Long">
        delete from rlc_licence where id = #{id}
    </delete>

    <delete id="deleteRlcLicenceByIds" parameterType="String">
        delete from rlc_licence where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
