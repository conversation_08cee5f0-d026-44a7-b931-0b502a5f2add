<#import "/macro/macro.ftl" as my />
<!DOCTYPE html>
<html>
<@my.head jqgrid=true component=true vueValidate=true elementUI=true ztree=true  ueditor=true>
    <title>客户端临时数据池</title>
</@my.head>
<body>
<div id="app" v-cloak>
    <div v-show="showList">
        <div class="grid-btn form-inline">
            <div class="form-group">
                <a class="btn btn-default" @click="query"><i class="fa fa-search"></i>&nbsp;查询</a>
                <a class="btn btn-default" @click="queryAll"><i class="fa fa-refresh"></i>&nbsp;全部</a>
            </div>
			<#if shiro.hasPermission("client:tempDataPool:save")>
            <div class="form-group">
            <a class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
            </div>
			</#if>
			<#if shiro.hasPermission("client:tempDataPool:delete")>
            <div class="form-group">
            <a class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;批量删除</a>
            </div>
			</#if>
        </div>
        <a class="pull-right" @click="drawer=true">更多查询<i class="fa fa-plus"></i></a>
        <div class="clearfix"></div>
        <el-drawer
                :visible.sync="drawer"
                :with-header="false"
                direction="rtl">
            <div class="form-horizontal" style="width:100%;padding: 10px">
                <div class="form-group">
                </div>
                <div class="form-group">
                    <a class="btn btn-default" @click="query('drawer')"><i class="fa fa-search"></i>&nbsp;查询</a>
                    <a class="btn btn-default" @click="clearQ"><i class="fa fa-refresh"></i>&nbsp;清空</a>
                </div>
            </div>
        </el-drawer>
        <my-table url="${request.contextPath}/client/tempDataPool/list" ref="tempDataPoolTable" row-key="id">
            <el-table-column  label="id 主键" min-width="80" prop="id" sortable="custom" column-key="ID"></el-table-column>
            <el-table-column  label="创建时间" min-width="80" prop="createTime" sortable="custom" column-key="CREATE_TIME"></el-table-column>
            <el-table-column  label="客户端ID" min-width="80" prop="clientId" sortable="custom" column-key="CLIENT_ID"></el-table-column>
            <el-table-column  label="磅站ID" min-width="80" prop="stationId" sortable="custom" column-key="STATION_ID"></el-table-column>
            <el-table-column  label="车牌号" min-width="80" prop="carNumber" sortable="custom" column-key="CAR_NUMBER"></el-table-column>
            <el-table-column  label="重量" min-width="80" prop="weight" sortable="custom" column-key="WEIGHT"></el-table-column>
            <el-table-column  label="照片对应附件表IDS" min-width="80" prop="imgFileIds" sortable="custom" column-key="IMG_FILE_IDS"></el-table-column>
            <el-table-column label="操作" column-key="caozuo" min-width="80" fixed="right">
                <template slot-scope="scope">
                    <button @click="update(scope.row.id)" class="btn btn-xs btn-success" title="编辑" type="button"><i
                            class="fa fa-edit" aria-hidden="true"></i></button>
                    <button @click="delOne(scope.row.id)" class="btn btn-xs btn-success" title="删除" type="button"><i
                            class="fa fa-trash" aria-hidden="true"></i></button>
                </template>
            </el-table-column>
        </my-table>
    </div>

    <div v-show="!showList" class="panel panel-default">
        <div class="panel-heading">{{title}}</div>
        <form class="form-horizontal" id="frm">
														                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">创建时间</div>
                <div class="col-sm-9">
                    <my-input type="text" id="createTime" v-model.trim="tempDataPool.createTime" placeholder="创建时间"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">客户端ID</div>
                <div class="col-sm-9">
                    <my-input type="text" id="clientId" v-model.trim="tempDataPool.clientId" placeholder="客户端ID"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">磅站ID</div>
                <div class="col-sm-9">
                    <my-input type="text" id="stationId" v-model.trim="tempDataPool.stationId" placeholder="磅站ID"></my-input>
                </div>
            </div>
											                                                                                
            <div class="form-group">
                <div class="col-sm-3 control-label">车牌号</div>
                <div class="col-sm-9">
                    <my-input type="text" id="carNumber" v-model.trim="tempDataPool.carNumber" placeholder="车牌号"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">重量</div>
                <div class="col-sm-9">
                    <my-input type="number" id="weight" v-model.trim="tempDataPool.weight" placeholder="重量"></my-input>
                </div>
            </div>
											                                                            
            <div class="form-group">
                <div class="col-sm-3 control-label">照片对应附件表IDS</div>
                <div class="col-sm-9">
                    <my-input type="text" id="imgFileIds" v-model.trim="tempDataPool.imgFileIds" placeholder="照片对应附件表IDS"></my-input>
                </div>
            </div>
							
            <div class="form-group center">
                <input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
                &nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
            </div>
        </form>
    </div>
</div>
<script src="${request.contextPath}/statics/js/modules/client/tempDataPool.js?_${sloth.version()}"></script>
</body>
</html>
