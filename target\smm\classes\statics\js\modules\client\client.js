																																									
var vm = new Vue({
	el:'#app',
	mixins: [basicTableMixin],
	data:{
		showList: true,
		title: null,
		drawer:false,
		q:{},
		client: {}
	},
	methods: {
		add: function(){
			vm.title = "新增";
			vm.client = {};
			vm.$nextTick(function () {
				vm.showList = false;
			})
		},
		update: function (id) {
			//var id = getSelectedRow();
			if(id == null){
				return ;
			}
			vm.showList = false;
            vm.title = "修改";
            vm.getInfo(id)
		},
		saveOrUpdate: function (event) {
		    if(!validateForm("frm")){
		        return;
			}
			var url = vm.client.id == null ? "client/client/save" : "client/client/update";
			var that=this;
			this.$http.postWithShade(baseURL + url, this.client)
					.then(function (r) {
						alert("操作成功", function () {
							that.reload();
						});
					});
		},
		del: function (event) {
			var ids = this.$refs.clientTable.getSelectRowKeys();
			if(ids == null){
				alert('请选择一条记录');
				return ;
			}
			var that=this;
			confirm('确定要删除选中的 ' + ids.length + ' 条记录？', function () {
				that.$http.post(baseURL + "client/client/delete", ids)
						.then(function (r) {
							alert("操作成功", function () {
								that.reload();
							});
						});
			});
		},
		delOne: function (id) {
			var that=this;
			confirm('确定要删除选中的记录？', function(){
				that.$http.post(baseURL + "client/client/delete/" +id)
						.then(function (r) {
							alert("操作成功", function () {
								that.reload();
							});
						});
			});
        },
		getInfo: function(id){
			var that=this
			this.$http.post(baseURL + "client/client/info/" +id)
					.then(function (r) {
						that.client = r.client;
					});
		},
		reload: function (event) {
			vm.showList = true;
			this.$refs.clientTable.search(this.q, event === 'query');
            $(".error-label").remove();
		}
	},
});
