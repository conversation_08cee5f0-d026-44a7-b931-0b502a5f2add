<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybkj.smm.modules.rlc.mapper.RlcProjParkMapper">

    <resultMap type="RlcProjPark" id="RlcProjParkResult">
        <result property="id"    column="id"    />
        <result property="licenceId"    column="licence_id"    />
        <result property="geojson"    column="geojson"    />
        <result property="subType"    column="sub_type"    />
        <result property="mainType"    column="main_type"    />
        <result property="rvName"    column="rv_name"    />
        <result property="rvCode"    column="rv_code"    />
        <result property="fsdaName"    column="fsda_name"    />
        <result property="fsdaCode"    column="fsda_code"    />
        <result property="basinName"    column="basin_name"    />
        <result property="basinCode"    column="basin_code"    />
        <result property="adminName"    column="admin_name"    />
        <result property="adminCode"    column="admin_code"    />
        <result property="crossLoc"    column="cross_loc"    />
        <result property="dfcSta"    column="dfc_sta"    />
        <result property="desFlow"    column="des_flow"    />
        <result property="desLevel"    column="des_level"    />
        <result property="coords"    column="coords"    />
        <result property="blAvgElev"    column="bl_avg_elev"    />
        <result property="moveSfCount"    column="move_sf_count"    />
        <result property="plantsType"    column="plants_type"    />
        <result property="wharfBottomElev"    column="wharf_bottom_elev"    />
        <result property="isOccupyFlSection"    column="is_occupy_fl_section"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="deptId"    column="dept_id"    />
    </resultMap>

    <sql id="selectRlcProjParkVo">
        select id, licence_id, geojson, sub_type, main_type, rv_name, rv_code, fsda_name, fsda_code, basin_name, basin_code, admin_name, admin_code, cross_loc, dfc_sta, des_flow, des_level, coords, bl_avg_elev, move_sf_count, plants_type, wharf_bottom_elev, is_occupy_fl_section, create_by, create_time, update_by, update_time, remark, dept_id from rlc_proj_park
    </sql>

    <select id="selectRlcProjParkList" parameterType="RlcProjPark" resultMap="RlcProjParkResult">
        <include refid="selectRlcProjParkVo"/>
        <where>
            <if test="licenceId != null "> and licence_id = #{licenceId}</if>
            <if test="geojson != null  and geojson != ''"> and geojson = #{geojson}</if>
            <if test="subType != null  and subType != ''"> and sub_type = #{subType}</if>
            <if test="mainType != null  and mainType != ''"> and main_type = #{mainType}</if>
            <if test="rvName != null  and rvName != ''"> and rv_name like concat('%', #{rvName}, '%')</if>
            <if test="rvCode != null  and rvCode != ''"> and rv_code = #{rvCode}</if>
            <if test="fsdaName != null  and fsdaName != ''"> and fsda_name like concat('%', #{fsdaName}, '%')</if>
            <if test="fsdaCode != null  and fsdaCode != ''"> and fsda_code = #{fsdaCode}</if>
            <if test="basinName != null  and basinName != ''"> and basin_name like concat('%', #{basinName}, '%')</if>
            <if test="basinCode != null  and basinCode != ''"> and basin_code = #{basinCode}</if>
            <if test="adminName != null  and adminName != ''"> and admin_name like concat('%', #{adminName}, '%')</if>
            <if test="adminCode != null  and adminCode != ''"> and admin_code = #{adminCode}</if>
            <if test="crossLoc != null  and crossLoc != ''"> and cross_loc = #{crossLoc}</if>
            <if test="dfcSta != null  and dfcSta != ''"> and dfc_sta = #{dfcSta}</if>
            <if test="desFlow != null  and desFlow != ''"> and des_flow = #{desFlow}</if>
            <if test="desLevel != null  and desLevel != ''"> and des_level = #{desLevel}</if>
            <if test="coords != null  and coords != ''"> and coords = #{coords}</if>
            <if test="blAvgElev != null  and blAvgElev != ''"> and bl_avg_elev = #{blAvgElev}</if>
            <if test="moveSfCount != null  and moveSfCount != ''"> and move_sf_count = #{moveSfCount}</if>
            <if test="plantsType != null  and plantsType != ''"> and plants_type = #{plantsType}</if>
            <if test="wharfBottomElev != null  and wharfBottomElev != ''"> and wharf_bottom_elev = #{wharfBottomElev}</if>
            <if test="isOccupyFlSection != null  and isOccupyFlSection != ''"> and is_occupy_fl_section = #{isOccupyFlSection}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
        </where>
    </select>

    <select id="selectRlcProjParkById" parameterType="Long" resultMap="RlcProjParkResult">
        <include refid="selectRlcProjParkVo"/>
        where id = #{id}
    </select>

    <insert id="insertRlcProjPark" parameterType="RlcProjPark" useGeneratedKeys="true" keyProperty="id">
        insert into rlc_proj_park
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="licenceId != null">licence_id,</if>
            <if test="geojson != null">geojson,</if>
            <if test="subType != null">sub_type,</if>
            <if test="mainType != null">main_type,</if>
            <if test="rvName != null">rv_name,</if>
            <if test="rvCode != null">rv_code,</if>
            <if test="fsdaName != null">fsda_name,</if>
            <if test="fsdaCode != null">fsda_code,</if>
            <if test="basinName != null">basin_name,</if>
            <if test="basinCode != null">basin_code,</if>
            <if test="adminName != null">admin_name,</if>
            <if test="adminCode != null">admin_code,</if>
            <if test="crossLoc != null">cross_loc,</if>
            <if test="dfcSta != null">dfc_sta,</if>
            <if test="desFlow != null">des_flow,</if>
            <if test="desLevel != null">des_level,</if>
            <if test="coords != null">coords,</if>
            <if test="blAvgElev != null">bl_avg_elev,</if>
            <if test="moveSfCount != null">move_sf_count,</if>
            <if test="plantsType != null">plants_type,</if>
            <if test="wharfBottomElev != null">wharf_bottom_elev,</if>
            <if test="isOccupyFlSection != null">is_occupy_fl_section,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="deptId != null">dept_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="licenceId != null">#{licenceId},</if>
            <if test="geojson != null">#{geojson},</if>
            <if test="subType != null">#{subType},</if>
            <if test="mainType != null">#{mainType},</if>
            <if test="rvName != null">#{rvName},</if>
            <if test="rvCode != null">#{rvCode},</if>
            <if test="fsdaName != null">#{fsdaName},</if>
            <if test="fsdaCode != null">#{fsdaCode},</if>
            <if test="basinName != null">#{basinName},</if>
            <if test="basinCode != null">#{basinCode},</if>
            <if test="adminName != null">#{adminName},</if>
            <if test="adminCode != null">#{adminCode},</if>
            <if test="crossLoc != null">#{crossLoc},</if>
            <if test="dfcSta != null">#{dfcSta},</if>
            <if test="desFlow != null">#{desFlow},</if>
            <if test="desLevel != null">#{desLevel},</if>
            <if test="coords != null">#{coords},</if>
            <if test="blAvgElev != null">#{blAvgElev},</if>
            <if test="moveSfCount != null">#{moveSfCount},</if>
            <if test="plantsType != null">#{plantsType},</if>
            <if test="wharfBottomElev != null">#{wharfBottomElev},</if>
            <if test="isOccupyFlSection != null">#{isOccupyFlSection},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deptId != null">#{deptId},</if>
         </trim>
    </insert>

    <update id="updateRlcProjPark" parameterType="RlcProjPark">
        update rlc_proj_park
        <trim prefix="SET" suffixOverrides=",">
            <if test="licenceId != null">licence_id = #{licenceId},</if>
            <if test="geojson != null">geojson = #{geojson},</if>
            <if test="subType != null">sub_type = #{subType},</if>
            <if test="mainType != null">main_type = #{mainType},</if>
            <if test="rvName != null">rv_name = #{rvName},</if>
            <if test="rvCode != null">rv_code = #{rvCode},</if>
            <if test="fsdaName != null">fsda_name = #{fsdaName},</if>
            <if test="fsdaCode != null">fsda_code = #{fsdaCode},</if>
            <if test="basinName != null">basin_name = #{basinName},</if>
            <if test="basinCode != null">basin_code = #{basinCode},</if>
            <if test="adminName != null">admin_name = #{adminName},</if>
            <if test="adminCode != null">admin_code = #{adminCode},</if>
            <if test="crossLoc != null">cross_loc = #{crossLoc},</if>
            <if test="dfcSta != null">dfc_sta = #{dfcSta},</if>
            <if test="desFlow != null">des_flow = #{desFlow},</if>
            <if test="desLevel != null">des_level = #{desLevel},</if>
            <if test="coords != null">coords = #{coords},</if>
            <if test="blAvgElev != null">bl_avg_elev = #{blAvgElev},</if>
            <if test="moveSfCount != null">move_sf_count = #{moveSfCount},</if>
            <if test="plantsType != null">plants_type = #{plantsType},</if>
            <if test="wharfBottomElev != null">wharf_bottom_elev = #{wharfBottomElev},</if>
            <if test="isOccupyFlSection != null">is_occupy_fl_section = #{isOccupyFlSection},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRlcProjParkById" parameterType="Long">
        delete from rlc_proj_park where id = #{id}
    </delete>

    <delete id="deleteRlcProjParkByIds" parameterType="String">
        delete from rlc_proj_park where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


</mapper>
