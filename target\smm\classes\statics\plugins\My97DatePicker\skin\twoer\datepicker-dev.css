/* 
 * My97 DatePicker 4.8 
 * auther : z<PERSON><PERSON><PERSON> , hejianting(design)
 * email : <EMAIL>
 * date : 2012-05-25
 */ 
.WdateDiv
{
	position:relative;
	padding:5px;
	width:180px;
	*width:190px;
	font-size:12px;
	color:#333;
	border:solid 1px #DEDEDE;
	background-color:#F2F0F1;
}

.WdateDiv2
{
	width:360px;
}

.WdateDiv .NavImg a,.WdateDiv .yminput,.WdateDiv .yminputfocus,.WdateDiv #dpQS 
{
	background:url(img.gif) no-repeat;
}

.WdateDiv .NavImg a
{
	float:left;
	width:16px;
	height:16px;
	cursor:pointer;
}
.WdateDiv .NavImgll a
{
	background-position:0px 5px;
}
.WdateDiv .NavImgl a
{
	background-position:0px -10px;
}
.WdateDiv .NavImgr a
{
	background-position:0px -25px;
	float:right;
}
.WdateDiv .NavImgrr a
{
	background-position:0px -40px;
	float:right;
}

.WdateDiv #dpTitle
{
	padding:3px 0px 0px 0px;
	line-height:0px;
	height:20px;
	*height:23;
}

.WdateDiv .yminput,.WdateDiv .yminputfocus
{
	margin-left:3px;
	width:50px;
	height:20px;
	line-height:16px;
	border:solid 1px #F2F0F1;
	cursor:pointer;
	background-position:35px -68px;
}

.WdateDiv .yminputfocus
{
	background-color:#fff;
	border:solid 1px #D8D8D8;
}

.WdateDiv .menuSel{
	z-index:1;
	position:absolute;
	background-color:#FFFFFF;
	border:#A3C6C8 1px solid;
	display:none;
}

.WdateDiv .menu
{
	background:#fff;
}
.WdateDiv .menuOn
{
	color:#fff;
	background:#64A3F3;
}

.WdateDiv .invalidMenu{
	color:#aaa;
}

.WdateDiv .MMenu,.WdateDiv .YMenu
{
	padding:2px;
	margin-top:20px;
	margin-left:-1px;
	width:68px;
	border:solid 1px #D9D9D9;
}
.WdateDiv .MMenu table,.WdateDiv .YMenu table
{
	width:100%;
}
.WdateDiv .MMenu table td,.WdateDiv .YMenu table td
{
	padding:0px;
	line-height:20px;
	text-align:center;
	font-size:12px;
	cursor: pointer;
}

.WdateDiv .Wweek {
 	text-align:center;
	background:#DAF3F5;
	border-right:#BDEBEE 1px solid;
 }

.WdateDiv td
{
	padding:1px;
	line-height:20px;
	font-size:12px;
	color:#999999;
	background:#fff;
	cursor:pointer;
}
.WdateDiv .MTitle td
{
	line-height:24px;
	color:#7D7D7D;
	background:#F2F0F1;
	cursor: default;
}

.WdateDiv .WdayTable2
{
	border-collapse:collapse;
	border:#808080 1px solid;
}
.WdateDiv .WdayTable2 table
{
	border:0;
}

.WdateDiv .WdayTable{
	line-height:20px;	
	color:#13777e;
	background-color:#edfbfb;
}
.WdateDiv .WdayTable td{
	text-align:center;
}

.WdateDiv .Wday
{
	color:#323232;
}

.WdateDiv .WdayOn
{
	color:#fff;
	background-color:#65A2F3;	
}

.WdateDiv .Wwday
{
	color:#65A4F3;
}

.WdateDiv .WwdayOn
{
	color:#fff;
	background-color:#65A2F3;	
}
.WdateDiv .Wtoday
{
	color:#FF6D10;
	background:#E0EDFE;
}
.WdateDiv .Wselday
{
	color:#fff;
	background-color:#65A2F3;	
}
.WdateDiv .WspecialDay{
	background-color:#66F4DF;
}

.WdateDiv .WotherDay
{ 
	color:#D4D4D4;	
}
.WdateDiv .WotherDayOn
{
	color:#fff;
	background-color:#65A2F3;	
}

.WdateDiv .WinvalidDay{
	color:#aaa;
}

.WdateDiv #dpTime
{
	position:relative;
	margin-top:5px;
}

.WdateDiv #dpTime #dpTimeStr
{
	display:inline-block;
	width:28px;
	*width:30px;
	color:#7d7d7d;
}

.WdateDiv #dpTime input
{
	padding:0px;
	margin:0px;
	width:25px;
	height:20px;
	line-height:20px;
	text-align:center;
	color:#333;
	border:#D9D9D9 1px solid;	
}

.WdateDiv #dpTime .tm
{
	width:7px;
	border:none;
	background:#F2F0F1;
}

.WdateDiv #dpTime #dpTimeUp
{
	display:none;
}

.WdateDiv #dpTime #dpTimeDown
{
	display:none;
}

.WdateDiv #dpQS 
 {
 	float:left;
	margin-right:3px;
	margin-top:9px;
	*margin-top:6px;
	width:16px;
	height:16px;
	cursor:pointer;
	background-position:0px -90px;
 }
.WdateDiv #dpControl 
{
	text-align:right;
	margin-top:3px;
}
.WdateDiv .dpButton
{ 
	margin-left:2px;
	line-height:18px;
	*line-height:16px;
	width:45px;
	background-color:#C3C3C3;
	*background-color:#64A3F3;
	color:#fff;
	border:none;
	cursor: pointer;
}
.WdateDiv .dpButton:hover
{ 
	background-color:#64A3F3;
}

.WdateDiv .hhMenu,
.WdateDiv .mmMenu,
.WdateDiv .ssMenu
{
	position:absolute;
	padding:3px;
	font-size:12px;
	color:#333;
	border:solid 1px #DEDEDE;
	background-color:#F2F0F1;
}

.WdateDiv #dpTime .menu,.WdateDiv #dpTime .menuOn
{
	width:18px;
	height:18px;
	line-height:18px;
	text-align:center;
	background:#fff;
}
.WdateDiv #dpTime .menuOn
{
	background:#65A2F3;
}

.WdateDiv #dpTime td
{
	background:#F2F0F1;
}

.WdateDiv .hhMenu
{
	top:-87px;
	left:35px;
	left:32px\9;
}

.WdateDiv .mmMenu
{
	top:-47px;
	left:35px;
	left:32px\9;
}

.WdateDiv .ssMenu
{
	top:-27px;
	left:35px;
	left:32px\9;
}