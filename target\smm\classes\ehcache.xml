<?xml version="1.0" encoding="UTF-8"?>
<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="ehcache.xsd"
         updateCheck="true" monitoring="autodetect"
         dynamicConfig="true">

       <diskStore path="user.dir/temp/ehcache" />

      <!-- default cache for hibernate -->
       <defaultCache
            maxElementsInMemory="10000"
            eternal="false"
            timeToIdleSeconds="1200"
            timeToLiveSeconds="1200"
            overflowToDisk="true"
            diskSpoolBufferSizeMB="30"
            maxElementsOnDisk="10000000"
            diskPersistent="false"
            diskExpiryThreadIntervalSeconds="120"
            memoryStoreEvictionPolicy="LRU"
            statistics="true">
       </defaultCache>

       <!-- httpSessionCache cache for httpSession   -->
       <cache
            name="httpSessionCache"
            maxElementsInMemory="10000"
            eternal="false"
            timeToIdleSeconds="3600"
            timeToLiveSeconds="7200"
            overflowToDisk="false"
            memoryStoreEvictionPolicy="LRU">
       </cache>
        <!--shiro cache-->
       <cache
               name="shiroCache"
               maxElementsInMemory="10000"
               eternal="false"
               timeToIdleSeconds="600"
               timeToLiveSeconds="600"
               overflowToDisk="false"
               memoryStoreEvictionPolicy="LRU">
       </cache>
       <!--token cache-->
       <cache
               name="tokenCache"
               maxElementsInMemory="100000"
               eternal="true"
               timeToLiveSeconds="0"
               overflowToDisk="false">
       </cache>

        <!-- constantsDataCache cache for system constants data   -->
       <cache
            name="constantsDataCache"
            maxElementsInMemory="10000"
            eternal="true"
            timeToLiveSeconds="0"
            overflowToDisk="false">
       </cache>

       <!--通过 @cacheAble注解缓存的数据-->
       <cache
               name="cacheAbleCache"
               maxElementsInMemory="10000"
               eternal="false"
               timeToIdleSeconds="600"
               timeToLiveSeconds="600"
               overflowToDisk="false"
               memoryStoreEvictionPolicy="LRU">
       </cache>

       <!-- open token cache -->
       <cache
               name="openTokenCache"
               maxElementsInMemory="1000"
               eternal="false"
               timeToIdleSeconds="3600"
               timeToLiveSeconds="7200"
               overflowToDisk="true">
       </cache>


</ehcache>
