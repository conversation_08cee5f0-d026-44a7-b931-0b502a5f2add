<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybkj.smm.modules.rlc.mapper.RlcProjRoadMapper">

    <resultMap type="RlcProjRoad" id="RlcProjRoadResult">
        <result property="id"    column="id"    />
        <result property="licenceId"    column="licence_id"    />
        <result property="geojson"    column="geojson"    />
        <result property="subType"    column="sub_type"    />
        <result property="mainType"    column="main_type"    />
        <result property="rvName"    column="rv_name"    />
        <result property="rvCode"    column="rv_code"    />
        <result property="fsdaName"    column="fsda_name"    />
        <result property="fsdaCode"    column="fsda_code"    />
        <result property="basinName"    column="basin_name"    />
        <result property="basinCode"    column="basin_code"    />
        <result property="adminName"    column="admin_name"    />
        <result property="adminCode"    column="admin_code"    />
        <result property="crossLoc"    column="cross_loc"    />
        <result property="dfcSta"    column="dfc_sta"    />
        <result property="desFlow"    column="des_flow"    />
        <result property="desLevel"    column="des_level"    />
        <result property="coords"    column="coords"    />
        <result property="aLng"    column="a_lng"    />
        <result property="aLat"    column="a_lat"    />
        <result property="bLng"    column="b_lng"    />
        <result property="bLat"    column="b_lat"    />
        <result property="crossAngle"    column="cross_angle"    />
        <result property="crossLen"    column="cross_len"    />
        <result property="minBottomElev"    column="min_bottom_elev"    />
        <result property="minBptopElev"    column="min_bptop_elev"    />
        <result property="ldikeTopHeadroom"    column="ldike_top_headroom"    />
        <result property="rdikeTopHeadroom"    column="rdike_top_headroom"    />
        <result property="fsdaRbedLen"    column="fsda_rbed_len"    />
        <result property="fsdaRbedStaLng"    column="fsda_rbed_sta_lng"    />
        <result property="fsdaRbedStaLat"    column="fsda_rbed_sta_lat"    />
        <result property="fsdaRbedEndLng"    column="fsda_rbed_end_lng"    />
        <result property="fsdaRbedEndLat"    column="fsda_rbed_end_lat"    />
        <result property="fsdaRbedAvgElev"    column="fsda_rbed_avg_elev"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="deptId"    column="dept_id"    />
    </resultMap>

    <sql id="selectRlcProjRoadVo">
        select id, licence_id, geojson, sub_type, main_type, rv_name, rv_code, fsda_name, fsda_code, basin_name, basin_code, admin_name, admin_code, cross_loc, dfc_sta, des_flow, des_level, coords, a_lng, a_lat, b_lng, b_lat, cross_angle, cross_len, min_bottom_elev, min_bptop_elev, ldike_top_headroom, rdike_top_headroom, fsda_rbed_len, fsda_rbed_sta_lng, fsda_rbed_sta_lat, fsda_rbed_end_lng, fsda_rbed_end_lat, fsda_rbed_avg_elev, create_by, create_time, update_by, update_time, remark, dept_id from rlc_proj_road
    </sql>

    <select id="selectRlcProjRoadList" parameterType="RlcProjRoad" resultMap="RlcProjRoadResult">
        <include refid="selectRlcProjRoadVo"/>
        <where>
            <if test="licenceId != null "> and licence_id = #{licenceId}</if>
            <if test="geojson != null  and geojson != ''"> and geojson = #{geojson}</if>
            <if test="subType != null  and subType != ''"> and sub_type = #{subType}</if>
            <if test="mainType != null  and mainType != ''"> and main_type = #{mainType}</if>
            <if test="rvName != null  and rvName != ''"> and rv_name like concat('%', #{rvName}, '%')</if>
            <if test="rvCode != null  and rvCode != ''"> and rv_code = #{rvCode}</if>
            <if test="fsdaName != null  and fsdaName != ''"> and fsda_name like concat('%', #{fsdaName}, '%')</if>
            <if test="fsdaCode != null  and fsdaCode != ''"> and fsda_code = #{fsdaCode}</if>
            <if test="basinName != null  and basinName != ''"> and basin_name like concat('%', #{basinName}, '%')</if>
            <if test="basinCode != null  and basinCode != ''"> and basin_code = #{basinCode}</if>
            <if test="adminName != null  and adminName != ''"> and admin_name like concat('%', #{adminName}, '%')</if>
            <if test="adminCode != null  and adminCode != ''"> and admin_code = #{adminCode}</if>
            <if test="crossLoc != null  and crossLoc != ''"> and cross_loc = #{crossLoc}</if>
            <if test="dfcSta != null  and dfcSta != ''"> and dfc_sta = #{dfcSta}</if>
            <if test="desFlow != null  and desFlow != ''"> and des_flow = #{desFlow}</if>
            <if test="desLevel != null  and desLevel != ''"> and des_level = #{desLevel}</if>
            <if test="coords != null  and coords != ''"> and coords = #{coords}</if>
            <if test="aLng != null  and aLng != ''"> and a_lng = #{aLng}</if>
            <if test="aLat != null  and aLat != ''"> and a_lat = #{aLat}</if>
            <if test="bLng != null  and bLng != ''"> and b_lng = #{bLng}</if>
            <if test="bLat != null  and bLat != ''"> and b_lat = #{bLat}</if>
            <if test="crossAngle != null  and crossAngle != ''"> and cross_angle = #{crossAngle}</if>
            <if test="crossLen != null  and crossLen != ''"> and cross_len = #{crossLen}</if>
            <if test="minBottomElev != null  and minBottomElev != ''"> and min_bottom_elev = #{minBottomElev}</if>
            <if test="minBptopElev != null  and minBptopElev != ''"> and min_bptop_elev = #{minBptopElev}</if>
            <if test="ldikeTopHeadroom != null  and ldikeTopHeadroom != ''"> and ldike_top_headroom = #{ldikeTopHeadroom}</if>
            <if test="rdikeTopHeadroom != null  and rdikeTopHeadroom != ''"> and rdike_top_headroom = #{rdikeTopHeadroom}</if>
            <if test="fsdaRbedLen != null  and fsdaRbedLen != ''"> and fsda_rbed_len = #{fsdaRbedLen}</if>
            <if test="fsdaRbedStaLng != null  and fsdaRbedStaLng != ''"> and fsda_rbed_sta_lng = #{fsdaRbedStaLng}</if>
            <if test="fsdaRbedStaLat != null  and fsdaRbedStaLat != ''"> and fsda_rbed_sta_lat = #{fsdaRbedStaLat}</if>
            <if test="fsdaRbedEndLng != null  and fsdaRbedEndLng != ''"> and fsda_rbed_end_lng = #{fsdaRbedEndLng}</if>
            <if test="fsdaRbedEndLat != null  and fsdaRbedEndLat != ''"> and fsda_rbed_end_lat = #{fsdaRbedEndLat}</if>
            <if test="fsdaRbedAvgElev != null  and fsdaRbedAvgElev != ''"> and fsda_rbed_avg_elev = #{fsdaRbedAvgElev}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
        </where>
    </select>

    <select id="selectRlcProjRoadById" parameterType="Long" resultMap="RlcProjRoadResult">
        <include refid="selectRlcProjRoadVo"/>
        where id = #{id}
    </select>

    <insert id="insertRlcProjRoad" parameterType="RlcProjRoad" useGeneratedKeys="true" keyProperty="id">
        insert into rlc_proj_road
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="licenceId != null">licence_id,</if>
            <if test="geojson != null">geojson,</if>
            <if test="subType != null">sub_type,</if>
            <if test="mainType != null">main_type,</if>
            <if test="rvName != null">rv_name,</if>
            <if test="rvCode != null">rv_code,</if>
            <if test="fsdaName != null">fsda_name,</if>
            <if test="fsdaCode != null">fsda_code,</if>
            <if test="basinName != null">basin_name,</if>
            <if test="basinCode != null">basin_code,</if>
            <if test="adminName != null">admin_name,</if>
            <if test="adminCode != null">admin_code,</if>
            <if test="crossLoc != null">cross_loc,</if>
            <if test="dfcSta != null">dfc_sta,</if>
            <if test="desFlow != null">des_flow,</if>
            <if test="desLevel != null">des_level,</if>
            <if test="coords != null">coords,</if>
            <if test="aLng != null">a_lng,</if>
            <if test="aLat != null">a_lat,</if>
            <if test="bLng != null">b_lng,</if>
            <if test="bLat != null">b_lat,</if>
            <if test="crossAngle != null">cross_angle,</if>
            <if test="crossLen != null">cross_len,</if>
            <if test="minBottomElev != null">min_bottom_elev,</if>
            <if test="minBptopElev != null">min_bptop_elev,</if>
            <if test="ldikeTopHeadroom != null">ldike_top_headroom,</if>
            <if test="rdikeTopHeadroom != null">rdike_top_headroom,</if>
            <if test="fsdaRbedLen != null">fsda_rbed_len,</if>
            <if test="fsdaRbedStaLng != null">fsda_rbed_sta_lng,</if>
            <if test="fsdaRbedStaLat != null">fsda_rbed_sta_lat,</if>
            <if test="fsdaRbedEndLng != null">fsda_rbed_end_lng,</if>
            <if test="fsdaRbedEndLat != null">fsda_rbed_end_lat,</if>
            <if test="fsdaRbedAvgElev != null">fsda_rbed_avg_elev,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="deptId != null">dept_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="licenceId != null">#{licenceId},</if>
            <if test="geojson != null">#{geojson},</if>
            <if test="subType != null">#{subType},</if>
            <if test="mainType != null">#{mainType},</if>
            <if test="rvName != null">#{rvName},</if>
            <if test="rvCode != null">#{rvCode},</if>
            <if test="fsdaName != null">#{fsdaName},</if>
            <if test="fsdaCode != null">#{fsdaCode},</if>
            <if test="basinName != null">#{basinName},</if>
            <if test="basinCode != null">#{basinCode},</if>
            <if test="adminName != null">#{adminName},</if>
            <if test="adminCode != null">#{adminCode},</if>
            <if test="crossLoc != null">#{crossLoc},</if>
            <if test="dfcSta != null">#{dfcSta},</if>
            <if test="desFlow != null">#{desFlow},</if>
            <if test="desLevel != null">#{desLevel},</if>
            <if test="coords != null">#{coords},</if>
            <if test="aLng != null">#{aLng},</if>
            <if test="aLat != null">#{aLat},</if>
            <if test="bLng != null">#{bLng},</if>
            <if test="bLat != null">#{bLat},</if>
            <if test="crossAngle != null">#{crossAngle},</if>
            <if test="crossLen != null">#{crossLen},</if>
            <if test="minBottomElev != null">#{minBottomElev},</if>
            <if test="minBptopElev != null">#{minBptopElev},</if>
            <if test="ldikeTopHeadroom != null">#{ldikeTopHeadroom},</if>
            <if test="rdikeTopHeadroom != null">#{rdikeTopHeadroom},</if>
            <if test="fsdaRbedLen != null">#{fsdaRbedLen},</if>
            <if test="fsdaRbedStaLng != null">#{fsdaRbedStaLng},</if>
            <if test="fsdaRbedStaLat != null">#{fsdaRbedStaLat},</if>
            <if test="fsdaRbedEndLng != null">#{fsdaRbedEndLng},</if>
            <if test="fsdaRbedEndLat != null">#{fsdaRbedEndLat},</if>
            <if test="fsdaRbedAvgElev != null">#{fsdaRbedAvgElev},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deptId != null">#{deptId},</if>
         </trim>
    </insert>

    <update id="updateRlcProjRoad" parameterType="RlcProjRoad">
        update rlc_proj_road
        <trim prefix="SET" suffixOverrides=",">
            <if test="licenceId != null">licence_id = #{licenceId},</if>
            <if test="geojson != null">geojson = #{geojson},</if>
            <if test="subType != null">sub_type = #{subType},</if>
            <if test="mainType != null">main_type = #{mainType},</if>
            <if test="rvName != null">rv_name = #{rvName},</if>
            <if test="rvCode != null">rv_code = #{rvCode},</if>
            <if test="fsdaName != null">fsda_name = #{fsdaName},</if>
            <if test="fsdaCode != null">fsda_code = #{fsdaCode},</if>
            <if test="basinName != null">basin_name = #{basinName},</if>
            <if test="basinCode != null">basin_code = #{basinCode},</if>
            <if test="adminName != null">admin_name = #{adminName},</if>
            <if test="adminCode != null">admin_code = #{adminCode},</if>
            <if test="crossLoc != null">cross_loc = #{crossLoc},</if>
            <if test="dfcSta != null">dfc_sta = #{dfcSta},</if>
            <if test="desFlow != null">des_flow = #{desFlow},</if>
            <if test="desLevel != null">des_level = #{desLevel},</if>
            <if test="coords != null">coords = #{coords},</if>
            <if test="aLng != null">a_lng = #{aLng},</if>
            <if test="aLat != null">a_lat = #{aLat},</if>
            <if test="bLng != null">b_lng = #{bLng},</if>
            <if test="bLat != null">b_lat = #{bLat},</if>
            <if test="crossAngle != null">cross_angle = #{crossAngle},</if>
            <if test="crossLen != null">cross_len = #{crossLen},</if>
            <if test="minBottomElev != null">min_bottom_elev = #{minBottomElev},</if>
            <if test="minBptopElev != null">min_bptop_elev = #{minBptopElev},</if>
            <if test="ldikeTopHeadroom != null">ldike_top_headroom = #{ldikeTopHeadroom},</if>
            <if test="rdikeTopHeadroom != null">rdike_top_headroom = #{rdikeTopHeadroom},</if>
            <if test="fsdaRbedLen != null">fsda_rbed_len = #{fsdaRbedLen},</if>
            <if test="fsdaRbedStaLng != null">fsda_rbed_sta_lng = #{fsdaRbedStaLng},</if>
            <if test="fsdaRbedStaLat != null">fsda_rbed_sta_lat = #{fsdaRbedStaLat},</if>
            <if test="fsdaRbedEndLng != null">fsda_rbed_end_lng = #{fsdaRbedEndLng},</if>
            <if test="fsdaRbedEndLat != null">fsda_rbed_end_lat = #{fsdaRbedEndLat},</if>
            <if test="fsdaRbedAvgElev != null">fsda_rbed_avg_elev = #{fsdaRbedAvgElev},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRlcProjRoadById" parameterType="Long">
        delete from rlc_proj_road where id = #{id}
    </delete>

    <delete id="deleteRlcProjRoadByIds" parameterType="String">
        delete from rlc_proj_road where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
