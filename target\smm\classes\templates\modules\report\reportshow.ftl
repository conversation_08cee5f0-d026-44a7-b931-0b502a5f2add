<#import "/macro/macro.ftl" as my />
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/html">
<@my.head jqgrid=true layDate=true validate=true elementUI=true ztree=true>
<title>${exportName!''}</title>
<style type="text/css">
        ${htmlReport.style}
</style>
</@my.head>
<body>
<#--查询部分-->
<form id="search" class="grid-btn form-inline" method="post" action="${request.contextPath}/modules/report/${reportName!'_'}.html?${fixedParameters!''}">
 <#--是否首次进入页面标记-->
 <input type="hidden" id="isFirstOpen" name="isFirstOpen" value="${isFirstOpen?string('true', 'false')}">
<#if paramArr?size gt 0>
    <#if pageIndex??&& pageIndex==0>
        <input type="hidden" name="pageIndex" value="0">
    </#if>
<#list paramArr as param>
    <div class="form-group">
    <#if (param.custom)?? && (param.custom)!=''>
    <#--优先使用自定义控件内容-->
        ${(param.custom)!}
    <#else >
        <#if param.type=='text'>
            <input type="text" id="${(param.id)!}" name="${(param.name)!}" value="${(params['${(param.name)!}'])!}" class="form-control"   placeholder="${(param.placeholder)!}"  ${(param.event)!}
                  <#if (param.readonly)?? && param.readonly=='true'> readonly="readonly" <#else> onKeyUp="keyUpEnter(event)" </#if>>
        <#elseif param.type=='radio'>
        <p class="form-control-static">${(param.placeholder)!}</p>
            <div class="radio">
                <#if (param.options)?? && (param.options)?size gt 0>
                    <#list param.options as option>
                <label class="radio-inline">
                    <input type="radio" name="${(param.name)!}"  value="${option.value}" ${(param.event)!} <#if params['${(param.name)!}']?? && params['${(param.name)!}']==option.value>checked</#if>/>${option.name}
                </label>
                    </#list>
                </#if>
            </div>
        <#--<#elseif param.type=='checkbox'>
                <p class="form-control-static">${(param.placeholder)!}</p>
            <div class="checkbox">
                <#if (param.options)?? && (param.options)?size gt 0>
                    <#list param.options as option>
                <label class="radio-inline">
                    <input type="checkbox" name="${(param.name)!}"  value="${option.value}" ${(param.event)!}/>${option.name}
                </label>
                    </#list>
                </#if>
            </div>-->
        <#elseif param.type=='date'>
           <input type="text" id="${(param.id)!}" name="${(param.name)!}" value="${(params['${(param.name)!}'])!}" readonly="readonly" class="form-control" title="${(param.placeholder)!}" placeholder="${(param.placeholder)!}" ${(param.event)!}>
            <script type="text/javascript">
                laydate.render({
                    elem: '#${(param.id)!}'//指定元素
                    <#if (param.datePattern)??>
                    ,format:'${(param.datePattern)!}'
                        <#else >
                    ,type: 'datetime'
                    </#if>
                    ,done:function (value) {
                        $("#${(param.id)!}").val(value);
                    }
                });
            </script>
        <#elseif param.type=='select'>
            <select id="${(param.id)!}" name="${(param.name)!}" class="form-control"  ${(param.event)!}>
                <#if (param.placeholder)??&&param.placeholder!=''>
                    <option value="">--${(param.placeholder)!}--</option>
                </#if>
                <#if (param.options)?? && (param.options)?size gt 0>
                    <#list param.options as option>
                <option value="${option.value}" <#if params['${(param.name)!}']?? && params['${(param.name)!}']==option.value> selected</#if> >${option.name}</option>
                    </#list>
                </#if>
            </select>
        <#elseif param.type=='hidden'>
            <input type="hidden" id="${(param.id)!}" name="${(param.name)!}" value="${(params['${(param.name)!}'])!}" class="form-control"   placeholder="${(param.placeholder)!}"  ${(param.event)!}
                      <#if (param.readonly)?? && param.readonly=='true'> readonly="readonly" </#if>>
        </#if>
    </#if>
    </div>
    <#if (param.script)?? && (param.script)!=''>
    <#--js-->
        <script type="text/javascript">
            ${(param.script)!}
        </script>
    </#if>
    <#if (param.hiddenHtml)?? && (param.hiddenHtml)!=''>
    <#--其他辅助html代码-->
        ${(param.hiddenHtml)!}
    </#if>
</#list>
    <div class="form-group">
        <a class="btn btn-default" onclick="query()"><i class="fa fa-search"></i>&nbsp;查询</a>
        <a class="btn btn-default" onclick="queryAll()"><i class="fa fa-refresh"></i>&nbsp;全部</a>
    </div>
    <#--button类型的显示-->
    <#list paramArr as param>
        <#if param.type=='button'>
       <div class="form-group">
           <a class="btn btn-default" ${(param.event)!}><i class="fa fa-${(param.name)!'search'}"></i>&nbsp;${(param.placeholder)!}</a>
       </div>
            <#if (param.script)?? && (param.script)!=''>
            <#--js-->
        <script type="text/javascript">
                ${(param.script)!}
        </script>
            </#if>
            <#if (param.hiddenHtml)?? && (param.hiddenHtml)!=''>
            <#--其他辅助html代码-->
                ${(param.hiddenHtml)!}
            </#if>
        </#if>
    </#list>
</#if>
</form>


<#--工具栏部分开始-->
<div style="border:solid 1px #ddd;border-radius:5px;height:35px;width:100%;margin-bottom:5px;background:#f8f8f8">
    <div style="text-align:${htmlReport.reportAlign}">
        <#if print>
        <button type="button" onclick="print()" class="btn btn-default ureport-pdf-direct-print" style="display:inline-block;padding:0;background:#f8f8f8;border:none;margin:3px" title="PDF在线打印">
            <img src='${request.contextPath}/ureport/res/ureport-asserts/icons/pdf-direct-print.svg' width="20px" height="20px">
        </button>
        </#if>
        <#if exportPdf>
        <button type="button" onclick="exportPdf()" class="btn btn-default ureport-export-pdf" style="display:inline-block;padding:0;background:#f8f8f8;border:none;margin:3px" title="导出PDF">
            <img src='${request.contextPath}/ureport/res/ureport-asserts/icons/pdf.svg' width="20px" height="20px">
        </button>
        </#if>
        <#if exportWord>
        <button type="button" onclick="exportWord()" class="btn btn-default ureport-export-word" style="display:inline-block;padding:0;background:#f8f8f8;border:none;margin:3px" title="导出WORD">
            <img src='${request.contextPath}/ureport/res/ureport-asserts/icons/word.svg' width="20px" height="20px">
        </button>
        </#if>
        <#if exportExcel>
        <button type="button" onclick="exportExcel()" class="btn btn-default ureport-export-excel" style="display:inline-block;padding:0;background:#f8f8f8;border:none;margin:3px" title="导出EXCEL">
            <img src='${request.contextPath}/ureport/res/ureport-asserts/icons/excel.svg' width="20px" height="20px">
        </button>
        <#--<button type="button" onclick="exportExcelPaging()" class="btn btn-default ureport-export-excel-paging" style="display:inline-block;padding:0;background:#f8f8f8;border:none;margin:3px" title="分页导出EXCEL">
            <img src='${request.contextPath}/ureport/res/ureport-asserts/icons/excel-paging.svg' width="20px" height="20px">
        </button>
        <button type="button" onclick="exportExcelSheet()" class="btn btn-default ureport-export-excel-paging-sheet" style="display:inline-block;padding:0;background:#f8f8f8;border:none;margin:3px" title="分页分Sheet导出EXCEL">
            <img src='${request.contextPath}/ureport/res/ureport-asserts/icons/excel-with-paging-sheet.svg' width="20px" height="20px">
        </button>-->
        </#if>
        <div class="btn-group">
            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" style="background:#f8f8f8;border:none;color:#337ab7">
                <#if htmlReport.pageIndex gt 0 >
                      分页预览
                <#else>
                      预览
                </#if>
                <span class="caret"></span>
            </button>
            <ul class="dropdown-menu" role="menu">
                <li><a href="${request.contextPath}/modules/report/${reportName!'_'}.html?pageIndex=0&${fixedParameters!''}${customParameters}" style="color:#337ab7">预览</a></li>
                <li><a href="${request.contextPath}/modules/report/${reportName!'_'}.html?pageIndex=1&${fixedParameters!''}${customParameters}" style="color:#337ab7">分页预览</a></li>
            </ul>
        </div>
        <#if htmlReport.pageIndex gt 0 >
        <span id='pagingContainer' style="font-size:14px;margin-left:1px;color:#337ab7">
			共<span id='totalPageLabel'>${htmlReport.totalPageWithCol}</span>页
			<select id="pageSelector" onchange="toPage()" class="form-control" style="display:inline-block;width:inherit;font-size:13px;height:28px;margin-top:2px">
                <#list 1..htmlReport.totalPage as index>
				<option <#if index==htmlReport.pageIndex> selected </#if>>${index}</option>
                </#list>
			</select>
			<span id='pageLinkContainer'></span>
		</span>
        </#if>
    </div>
</div>
<#--表格部分开始-->
<div id="_ureport_table" style="float:${htmlReport.reportAlign};overflow: auto">
${htmlReport.content}
</div>
<iframe name="_print_pdf_frame" width="0" height="0" frameborder="0" src="about:blank"></iframe>

<script type="text/javascript">
    $(function(){
	var searchDivHeight=$("#search").height()==null?0:$("#search").height();
        $(window).on("resize", function () {
            $("#_ureport_table").height($(window).height()-searchDivHeight-60);
            if ($('#_ureport_table').width() > $("#_ureport_table>table").width()) {
                $("#_ureport_table>table").width($("#_ureport_table").width()-5)
            }
        }).resize();
	})
    //键盘回车触发方法
    function keyUpEnter(event){
        if(event.keyCode==13){
            query();
        }
    }
    //查询
    function query() {
        $("#search").submit();
    }
    //显示全部
    function queryAll() {
        window.location.href="${request.contextPath}/modules/report/${reportName!'_'}.html?isFirstOpen=false&<#if pageIndex??&& pageIndex==0>pageIndex=0&${fixedParameters!''}<#else >${fixedParameters!''}</#if>"
    }
    function toPage() {
        window.location.href="${request.contextPath}/modules/report/${reportName!'_'}.html?pageIndex="+$("#pageSelector").val()+"&${fixedParameters!''}${customParameters}"
    }
    <#if print>
    var directPrintPdf=false
    function print() {
        showLoading();
        var url="${request.contextPath}/report/reportshow/export/print/${reportName!'_'}?${fixedParameters!''}${customParameters}";
        var iframe=window.frames['_print_pdf_frame'];
        if(!directPrintPdf){
            directPrintPdf=true;
            $("iframe[name='_print_pdf_frame']").on("load",function(){
                hideLoading();
                iframe.window.focus();
                iframe.window.print();
            });
        }
        iframe.window.focus();
        iframe.location.href=url;
    }
    function showLoading(){
        var url='${request.contextPath}/ureport/res/ureport-asserts/icons/loading.gif';
        var h=$(window).height()/2,w=$(window).width()/2;
        var cover=$('<div class="ureport-loading-cover" style="position: absolute;left: 0px;top: 0px;width:'+w*2+'px;height:'+h*2+'px;z-index: 1199;background:rgba(222,222,222,.5)"></div>');
        $(document.body).append(cover);
        var loading=$('<div class="ureport-loading" style="text-align: center;position: absolute;z-index: 1120;left: '+(w-35)+'px;top: '+(h-35)+'px;"><img src="'+url+'"> <div style="margin-top: 5px">打印数据加载中...</div></div>');
        $(document.body).append(loading);
    };
     function hideLoading(){
    $('.ureport-loading-cover').remove();
    $('.ureport-loading').remove();
};
    </#if>
    <#if exportPdf>
    function exportPdf() {
        window.open("${request.contextPath}/report/reportshow/export/pdf/${reportName!'_'}?${fixedParameters!''}${customParameters}")
    }
    </#if>
    <#if exportWord>
    function exportWord() {
        window.open("${request.contextPath}/report/reportshow/export/word/${reportName!'_'}?${fixedParameters!''}${customParameters}")
    }
    </#if>
    <#if exportExcel>
    function exportExcel() {
        window.open("${request.contextPath}/report/reportshow/export/excel/${reportName!'_'}?${fixedParameters!''}${customParameters}")
    }
    function exportExcelPaging() {
        window.open("${request.contextPath}/report/reportshow/export/excelPaging/${reportName!'_'}?${fixedParameters!''}${customParameters}")
    }
    function exportExcelSheet() {
        window.open("${request.contextPath}/report/reportshow/export/excelSheet/${reportName!'_'}?${fixedParameters!''}${customParameters}")

    }
    </#if>

</script>
</body>
